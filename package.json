{"name": "deca-apps", "private": true, "scripts": {"start": "echo 'Start'", "build": "turbo build", "dev": "turbo dev --concurrency 12", "dev:chatbox:client": "cd apps/chatbox && pnpm run dev:client ", "lint": "turbo lint", "test": "turbo test", "test:unit": "turbo run test:unit", "lint-staged-check": "turbo lint-staged-check", "lint:eslint:fix": "turbo lint:eslint:fix", "lint:fix": "turbo lint:fix", "format": "prettier --write \"**/*.{ts,tsx}\"", "release": "standard-version", "release:major": "turbo run release:major", "release:minor": "turbo run release:minor", "release:patch": "turbo run release:patch", "prepare": "husky install", "coverage": "turbo coverage --concurrency 68", "build:chatbox:client": "cd apps/chatbox && pnpm run build:client", "install:opencommit": "node scripts/opencommit/install.js", "commit": "node scripts/opencommit/run.js", "check-all-lint": "node ./scripts/run-command-with-all-apps.js lint", "check-all-test": "node ./scripts/run-command-with-all-apps.js test:unit", "check-all-build": "node ./scripts/run-command-with-all-apps.js build"}, "devDependencies": {"@changesets/cli": "^2.29.3", "@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@tsconfig/node20": "20.1.2", "@vitest/coverage-v8": "2.1.9", "glob": "^11.0.1", "husky": "^8.0.3", "identity-obj-proxy": "^3.0.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.1", "standard-version": "^9.5.0", "turbo": "1.11.2"}, "engines": {"node": "^20", "pnpm": "^9"}, "pnpm": {"patchedDependencies": {"@blocknote/react@0.15.5": "patches/@<EMAIL>"}}, "resolutions": {"prosemirror-model": "1.23.0"}}