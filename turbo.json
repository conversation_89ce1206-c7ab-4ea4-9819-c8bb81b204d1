{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "storybook-static/**"], "cache": false}, "lint": {"dependsOn": ["^lint"], "cache": false}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["^build"], "cache": false}, "test:unit": {"dependsOn": ["^build"], "cache": false}, "lint-staged-check": {"cache": false}, "lint:eslint:fix": {"cache": false}, "lint:fix": {"cache": false}, "coverage": {"dependsOn": ["^build"], "outputs": ["coverage/**"], "cache": false, "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"], "outputMode": "errors-only", "persistent": true}, "release:minor": {}, "release:major": {}, "release:patch": {}, "check-all-lint": {"cache": false}, "check-all-test": {"cache": false}, "check-all-build": {"cache": false}}}