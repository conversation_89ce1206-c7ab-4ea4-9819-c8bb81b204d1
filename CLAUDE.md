# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

DECA Apps is a comprehensive TypeScript monorepo containing all DECA Frontend Applications. Built with Turborepo for efficient workspace management, it houses 20+ React applications for various business domains including AI, CRM, chatbots, forms, and more.

## Key Commands

### Development
```bash
# Develop all apps (recommended - uses concurrency)
pnpm dev

# Develop specific app 
pnpm dev --filter [app-name]

# Develop chatbox client interface
pnpm dev:chatbox:client
```

### Testing
```bash
# Run all tests
pnpm test

# Run unit tests only
pnpm test:unit

# Run tests for specific app
pnpm test --filter [app-name]

# Generate coverage reports
pnpm coverage
```

### Code Quality
```bash
# Lint all code
pnpm lint

# Lint specific app
pnpm lint --filter [app-name]

# Fix linting issues
pnpm lint:fix

# Format code with Prettier
pnpm format
```

### Building
```bash
# Build all apps
pnpm build

# Build specific app
pnpm build --filter [app-name]

# Build chatbox client
pnpm build:chatbox:client
```

### Bulk Operations
```bash
# Check linting across all apps
pnpm check-all-lint

# Run tests across all apps
pnpm check-all-test

# Build all apps
pnpm check-all-build
```

## Architecture & Structure

### Monorepo Structure
- **apps/**: 20+ React applications (account, ai-studio, chatbot, crm, etc.)
- **packages/**: Shared libraries and utilities
  - `ui`: Shared React component library (Mantine-based)
  - `models`: Data models and TypeScript interfaces
  - `services`: Shared service layers and API clients
  - `utils`: Common utility functions
  - `shared-constants`: Application constants
  - `widget-engine`: Core widget engine for embedded components
  - `eslint-config`: Shared ESLint configuration
  - `typescript-config`: Shared TypeScript configurations

### Application Types
- **Vite Apps**: Most apps use Vite for development and building
- **Next.js Apps**: `livechat`, `page-builder`, and `form-client` use Next.js
- **Hybrid Apps**: `chatbox` has both admin interface (Vite) and client build

### Common Patterns
- **Configuration**: Apps store config in `src/configs/app.ts` using `@resola-ai/models` interfaces
- **Authentication**: Auth0 integration via `@resola-ai/ui` components
- **UI Framework**: Mantine v7 for consistent design system
- **Internationalization**: Tolgee for translations with fallback to English
- **Routing**: React Router v6 for SPA navigation
- **State Management**: Context API and React hooks patterns
- **Styling**: Emotion for CSS-in-JS with Mantine theme system

## Development Workflow

### Project Requirements
- Node.js v20 (exact version specified in engines)
- pnpm v9.x package manager
- TypeScript throughout the codebase

### Environment Setup
Apps typically require these environment variables:
```bash
VITE_ENV=dev
VITE_AUTH0_DOMAIN=your_auth0_domain
VITE_AUTH0_CLIENT_ID=your_auth0_client_id
VITE_API_SERVER_URL=your_api_server_url
VITE_BASE_PATH=/app-name
VITE_TOLGEE_URL=your_tolgee_url
VITE_TOLGEE_KEY=your_tolgee_api_key
```

### Testing Strategy
- **Framework**: Vitest for unit testing with jsdom environment
- **Testing Library**: React Testing Library for component testing
- **Coverage**: v8 coverage provider with lcov reports
- **Setup**: Each app has `setupTest.js` for test configuration

### Code Quality Tools
- **ESLint**: Shared configuration in `@resola-ai/eslint-config`
- **TypeScript**: Shared configs in `@resola-ai/typescript-config`
- **Prettier**: Code formatting with consistent rules
- **Husky**: Git hooks for pre-commit checks

## Application-Specific Notes

### Core Business Apps
- **chatbot**: Administrative interface for AI chatbot configuration
- **chatbox**: User-facing chat interface with admin portal
- **livechat**: Real-time customer support platform (Next.js)
- **ai-studio**: AI workflow and prompt management
- **crm**: Customer relationship management
- **form-admin/form-client**: Form builder and renderer

### Supporting Apps
- **account**: User account management
- **management**: Administrative dashboard
- **workshop**: Storybook for component development
- **apex**: Landing page and navigation hub

### Shared Packages Usage
- Import from workspace packages using `@resola-ai/package-name`
- `@resola-ai/ui` provides pre-built components and providers
- `@resola-ai/services-shared` handles API communication
- `@resola-ai/models` defines TypeScript interfaces

## Build & Deployment

### Turborepo Pipeline
- **Dependencies**: Build tasks depend on `^build` (upstream builds)
- **Caching**: Disabled for most tasks to ensure fresh builds
- **Outputs**: Apps output to `dist/` directory
- **Concurrency**: Development supports up to 12 concurrent processes

### CDN Integration
Apps support CDN deployment with dynamic asset prefixing:
- Uses `VITE_CDN_PREFIX` for asset URLs
- Supports preview environments via `AWS_PULL_REQUEST_ID`
- Base path configuration via `VITE_BASE_PATH`

## Commit & Branch Conventions

### Branch Naming
`<type>/<subject>` format with types: feature, fix, hotfix, release, refactor, chore, experimental, merge

### Commit Format
`<type>(<scope>): [<ticket-id>] - <subject>`

**Scopes**: Use app names (chatbot, crm, livechat) or 'shared' for cross-app changes
**Ticket Prefixes**: CB- (Chatbot), CRM-, LC- (Livechat), KB- (Knowledge Base), etc.

### Example Commits
```
feat(chatbot): CB-123 - implement multilingual support
fix(livechat): LC-456 - resolve agent status display
feat(shared): add new button component
```

## Key Files to Check

### For New Features
- `packages/ui/` - Check existing components before creating new ones
- `packages/models/` - Review data models and interfaces
- `HOW_TO_INIT_AN_APP.MD` - Guide for creating new applications

### For Bug Fixes
- App-specific `vite.config.ts` or `next.config.js` for build issues
- `setupTest.js` files for testing configuration
- `src/configs/app.ts` for environment and API configuration

### For Architecture Changes
- `turbo.json` - Monorepo build pipeline
- `pnpm-workspace.yaml` - Workspace configuration
- Shared package dependencies in `packages/*/package.json`

## Testing Practices

### Unit Testing
- Test files use `.test.tsx` or `.spec.tsx` extensions
- Place tests in `src/test/` directories or alongside components
- Use `describe` and `it` for test organization
- Mock external dependencies appropriately

### Coverage Requirements
- Generate coverage reports using `pnpm coverage`
- Coverage reports output to `coverage/` directory
- Exclude test files, config files, and type definitions

### E2E Testing
- Some apps have E2E tests in `tests/e2e/` directories
- API testing available in `tests/api/` with Postman collections

## Common Issues & Solutions

### Build Failures
- Check TypeScript errors with `pnpm build --filter [app-name]`
- Verify environment variables are properly set
- Ensure all dependencies are installed with `pnpm install`

### Development Server Issues
- Use `pnpm dev` for concurrent development of multiple apps
- Check port conflicts - apps use different ports
- Verify CDN and base path configurations

### Dependency Issues
- Use workspace protocol (`workspace:*`) for internal package dependencies
- Check `pnpm-lock.yaml` for version conflicts
- Patches are applied for specific packages (see `patches/` directory)

## Security Considerations

- Never commit sensitive environment variables
- Use proper Auth0 configuration for authentication
- Validate all user inputs and API responses
- Follow OWASP guidelines for web application security
- Use HTTPS for all API communications in production
