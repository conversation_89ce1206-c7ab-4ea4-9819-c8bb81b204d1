import { ActionIcon, Flex, <PERSON>ading<PERSON><PERSON>lay, rem, <PERSON>rollA<PERSON>, <PERSON>ack, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArrowLeft } from '@tabler/icons-react';
import { useMemo } from 'react';
import { useTranslate } from '@tolgee/react';
import { useNavigate, useParams } from 'react-router-dom';
import useSWR from 'swr';

import { CopyText } from '@/components';
import { HEADER_HEIGHT } from '@/constants/flow';
import { LineLogo } from '@/pages/IntegrationPage/components';
import { IntegrationAPI } from '@/services/api/integration';
import { maskString } from '@/utils/string';
import { ActionMenu } from './components';

const useStyles = createStyles(theme => ({
  container: {
    height: `calc(100vh - ${rem(HEADER_HEIGHT)})`,
    width: '100%',
  },
  backButton: {
    color: theme.colors.decaGrey[9],
    cursor: 'pointer',
  },
}));

export function InstalledDetail() {
  const navigate = useNavigate();
  const { t } = useTranslate('integration');
  const { classes } = useStyles();
  const { chatbotId = '', integrationId = '' } = useParams<{
    chatbotId: string;
    integrationId: string;
  }>();

  const { data: integrationResponse, isLoading } = useSWR(
    chatbotId ? ['Integration', chatbotId, integrationId] : null,
    () => IntegrationAPI.get(integrationId, chatbotId)
  );

  const integration = useMemo(() => integrationResponse?.data, [integrationResponse]);

  return (
    <>
      <ScrollArea className={classes.container}>
        <Stack p={rem(32)} gap={rem(28)}>
          <Flex
            align='center'
            gap={rem(12)}
            onClick={() => navigate(`/chatbot/${chatbotId}/integration/installed`)}
            className={classes.backButton}
          >
            <ActionIcon className={classes.backButton} variant='transparent'>
              <IconArrowLeft size={20} />
            </ActionIcon>
            <Text fz={rem(14)} fw={500}>
              {t('backToMenu')}
            </Text>
          </Flex>

          <Flex align='center' gap={rem(16)}>
            <LineLogo title={`LINE - ${integration?.name}`} />
            <ActionMenu integrationId={integrationId} chatbotId={chatbotId} />
          </Flex>

          <Stack gap={rem(20)}>
            <Text fz={rem(14)} fw={500} sx={{ textTransform: 'uppercase' }}>
              {t('channelInformation')}
            </Text>

            <Stack gap={rem(16)}>
              <Stack gap={rem(8)}>
                <Text fz={rem(14)} fw={500}>
                  {t('channelName')}
                </Text>
                <Text fz={rem(16)} fw={400} color='decaGrey.6'>
                  {integration?.name}
                </Text>
              </Stack>

              <Stack gap={rem(8)}>
                <Text fz={rem(14)} fw={500}>
                  {t('channelId')}
                </Text>
                <Text fz={rem(14)} fw={400} color='decaGrey.6'>
                  {integration?.configs?.channelId}
                </Text>
              </Stack>

              <Stack gap={rem(8)}>
                <Text fz={rem(14)} fw={500}>
                  {t('channelSecret')}
                </Text>
                <Text fz={rem(14)} fw={400} color='decaGrey.6'>
                  {maskString(integration?.configs?.channelSecret, '*', 4, 5)}
                </Text>
              </Stack>

              <Stack gap={rem(8)}>
                <Text fz={rem(14)} fw={500}>
                  {t('accessToken')}
                </Text>
                <Text fz={rem(14)} fw={400} color='decaGrey.6'>
                  {maskString(integration?.configs?.channelAccessToken, '*', 4, 5)}
                </Text>
              </Stack>

              <Stack gap={rem(8)}>
                <Text fz={rem(14)} fw={500}>
                  {t('webhookUrl')}
                </Text>
                {integration?.metadata?.webhookUrl && (
                  <>
                    <CopyText copyText={integration?.metadata?.webhookUrl}>
                      <Text fz={rem(14)} fw={400} color='decaGrey.6'>
                        {integration?.metadata?.webhookUrl}
                      </Text>
                    </CopyText>
                    <Text fz={rem(12)} fw={400} color='decaGrey.3'>
                      {t('webhookUrlDescription')}
                    </Text>
                  </>
                )}
              </Stack>
            </Stack>
          </Stack>
        </Stack>
      </ScrollArea>
      {isLoading && <LoadingOverlay visible={true} />}
    </>
  );
}
