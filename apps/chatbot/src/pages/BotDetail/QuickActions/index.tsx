import Section from '@/components/Section';
import { Flex, Grid, Stack, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight, IconMessages, IconPlugConnected, IconShare } from '@tabler/icons-react';
import { useMemo } from 'react';
import { useTranslate } from '@tolgee/react';
import { NavLink } from 'react-router-dom';
import { AppConfig } from '@/configs';
import { BLUE_1, BLUE_6 } from '@/constants/themeConfiguration';

const BASE_PATH = AppConfig.BASE_PATH;

const useStyles = createStyles(theme => ({
  quickActions: {
    flexDirection: 'column',
    gap: rem(10),
    marginBottom: rem(20),

    ['&:hover']: {
      borderColor: BLUE_6,
      backgroundColor: BLUE_1,
    },
  },
  iconBox: {
    backgroundColor: theme.colors.decaNavy[0],
    width: rem(32),
    height: rem(32),
    borderRadius: '50%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  link: {
    textDecoration: 'none',
    color: 'inherit',
  },
}));

interface QuickActionsProps {
  chatbotId: string;
}

const QuickActions = ({ chatbotId }: QuickActionsProps) => {
  const { t } = useTranslate('botDetail');
  const { classes } = useStyles();

  const links = useMemo(() => {
    const publishLink = `${BASE_PATH}${chatbotId}/flow-editor/`;
    const chatLogLink = `${BASE_PATH}${chatbotId}/chat-logs/`;
    const integrationLink = `${BASE_PATH}${chatbotId}/integration/`;

    return { publishLink, chatLogLink, integrationLink };
  }, [chatbotId]);

  return (
    <Stack gap={rem(20)}>
      <Text fw={500}>{t('quick_actions')}</Text>
      <Grid>
        <Grid.Col span={4}>
          <NavLink to={links.publishLink} className={classes.link}>
            <Section className={classes.quickActions}>
              <Flex className={classes.iconBox}>
                <IconShare size={13} color='#1D2088' />
              </Flex>
              <Flex align={'center'} gap={rem(10)}>
                <Text fw={500}>{t('publish_bot')}</Text>
                <IconChevronRight size={16} />
              </Flex>
              <Text size={rem(12)}>{t('publish_bot_desc')}</Text>
            </Section>
          </NavLink>
        </Grid.Col>
        <Grid.Col span={4}>
          <NavLink to={links.chatLogLink} className={classes.link}>
            <Section className={classes.quickActions}>
              <Flex className={classes.iconBox}>
                <IconMessages size={rem(13)} color='#1D2088' />
              </Flex>
              <Flex align={'center'} gap={rem(10)}>
                <Text fw={500}>{t('view_chat_log')}</Text>
                <IconChevronRight size={rem(16)} />
              </Flex>
              <Text size={rem(12)}>{t('view_chat_log_desc')}</Text>
            </Section>
          </NavLink>
        </Grid.Col>
        <Grid.Col span={4}>
          <NavLink to={links.integrationLink} className={classes.link}>
            <Section className={classes.quickActions}>
              <Flex className={classes.iconBox}>
                <IconPlugConnected size={rem(13)} color='#1D2088' />
              </Flex>
              <Flex align={'center'} gap={rem(10)}>
                <Text fw={500}>{t('integration')}</Text>
                <IconChevronRight size={16} />
              </Flex>
              <Text size={rem(12)}>{t('integration_desc')}</Text>
            </Section>
          </NavLink>
        </Grid.Col>
      </Grid>
    </Stack>
  );
};

export default QuickActions;
