import { RemoveButton } from '@/components/Common';
import { TButton } from '@/types';
import type { UniqueIdentifier } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { Flex, Text, rem, useMantineTheme } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconGripHorizontal, IconLayoutList } from '@tabler/icons-react';
import { memo, useEffect } from 'react';
import { useCarouselSettingsContext } from './CarouselSettingsContext';
import { useFlowContext } from '@/contexts/FlowContext';
import { getDndCssStyle } from '@/utils/dnd';

const useStyles = createStyles(theme => ({
  button: {
    padding: rem(10),
    flex: 1,
    alignItems: 'center',
    height: rem(36),
    border: `1px solid ${theme.colors.silverFox[5]}`,
    borderRadius: rem(4),
    backgroundColor: 'white',
    cursor: 'pointer',
  },
  label: {
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    maxWidth: rem(100),
  },
  sortItem: {
    cursor: 'pointer',
  },
}));

type Props = {
  buttonField: TButton;
  buttonFieldIndex: number;
  remove: (i: number) => void;
  id: UniqueIdentifier;
  name: string;
};

const ButtonItemSettings = ({
  buttonField: field,
  buttonFieldIndex: index,
  remove,
  id,
  name,
}: Props) => {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const { activeCarouselButtonName } = useFlowContext();

  const { attributes, isDragging, listeners, setNodeRef, transform, transition } = useSortable({
    id,
  });

  const { toggleEditButton, setActiveButtonName } = useCarouselSettingsContext();

  const handleClickEdit = () => {
    toggleEditButton(true);
    setActiveButtonName(name);
  };

  // TODO: later
  useEffect(() => {
    if (activeCarouselButtonName) {
      toggleEditButton(true);
      setActiveButtonName(activeCarouselButtonName);
    } else {
      toggleEditButton(false);
    }
  }, [activeCarouselButtonName]);

  return (
    <div {...attributes} {...listeners}>
      <Flex
        justify={'space-between'}
        align={'center'}
        gap={rem(15)}
        ref={setNodeRef}
        style={getDndCssStyle({ isDragging, transform, transition })}
        className={classes.sortItem}
      >
        <IconGripHorizontal size={20} color={theme.colors.silverFox[6]} />
        <Flex gap={rem(15)} className={classes.button} onClick={() => handleClickEdit()}>
          <IconLayoutList size={16} />
          <Text className={classes.label}>{field?.label}</Text>
        </Flex>
        <RemoveButton
          onClick={() => {
            remove(index);
          }}
        />
      </Flex>
    </div>
  );
};

export default memo(ButtonItemSettings);
