import React, { memo, useMemo, useCallback } from 'react';
import { useTranslate } from '@tolgee/react';
import get from 'lodash/get';
import isEmpty from 'lodash/isEmpty';
import cloneDeep from 'lodash/cloneDeep';
import { updateParentDataURLByActionId } from '@/utils/node';
import { rem, Box, Text, ScrollArea, Transition, ActionIcon } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { EnumActionTypes, NodeAction, TBuiltInNode, TActionData } from '@/types';
import withNodeSettings, { FlowCallbacks } from '@/hocs/withNodeSettings';
import { useFlowContext } from '@/contexts/FlowContext';
import { useRefHeight } from '@/hooks';
import { FADE_RIGHT_IN } from '@/constants/flow';
import URLActionForm from './URLActionForm';
import VariableActionForm from './VariableActionForm';
import GotoNodeActionForm from './GotoNodeActionForm';
import { IconCurrentLocation } from '@tabler/icons-react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useChatbotContext } from '@/contexts';

interface ActionNodeSettingsProps {
  nodeData: TBuiltInNode;
  isSidebarSettingClosed?: boolean;
  submit: (nodeData: TBuiltInNode) => void;
  showHeader?: boolean;
  flowCallbacks?: FlowCallbacks;
}

const useStyles = createStyles(theme => ({
  root: {
    height: '100%',
    position: 'relative',
  },
  header: {
    borderBottom: `1px solid ${theme.colors.silverFox[5]}`,
    paddingBottom: rem(12),
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
  },
  title: {
    fontWeight: 700,
    fontSize: rem(20),
    color: theme.colors.silverFox[9],
    lineHeight: 1,
  },
  content: {
    padding: `${theme.spacing.sm} 0`,
  },
  backButton: {
    marginRight: theme.spacing.sm,
  },
  scrollWrapper: {
    marginRight: rem(-8),
    paddingRight: rem(8),
  },
  divider: {
    marginTop: rem(16),
  },
  jumpToNodeButton: {
    color: theme.colors.silverFox[9],
    backgroundColor: 'transparent',
  },
  disabled: {
    color: theme.colors.silverFox[7],
  },
}));

const OFFSET_SCROLL_AREA_HEIGHT = 76;
const ActionNodeSettings: React.FC<ActionNodeSettingsProps> = ({
  nodeData: { ID, parentId },
  isSidebarSettingClosed,
  showHeader = true,
  flowCallbacks,
  submit,
}) => {
  const { t } = useTranslate('nodeSetting');
  const { classes, cx } = useStyles();
  const navigate = useNavigate();
  const { currChatbotId } = useChatbotContext();
  const [searchParams, setSearchParams] = useSearchParams();
  const { activeFlow, setActiveFlow, resetFlowEditorState, setNodeStartId } = useFlowContext();
  const [ref, height] = useRefHeight<HTMLDivElement>(OFFSET_SCROLL_AREA_HEIGHT);
  const nodeData = useMemo(() => activeFlow?.nodes?.[ID], [ID, activeFlow]) as TBuiltInNode;
  const actionData = useMemo(() => get(nodeData, 'data') as TActionData, [nodeData]);

  /**
   * Submit updated data to parent component
   * @param updatedData
   * @dependencies nodeData, submit
   */
  const handleSubmit = useCallback(
    updatedData => {
      submit({
        ...nodeData,
        data: {
          ...updatedData,
        },
      });

      flowCallbacks?.triggerUpdateFlowPosition();
    },
    [nodeData, submit, flowCallbacks]
  );

  const jumpToNode = useCallback(() => {
    const flow = get(actionData, 'flow', '');
    const target = get(actionData, 'target', '');
    resetFlowEditorState();
    setSearchParams(prev => {
      prev.set('node', target);
      return prev;
    });
    setNodeStartId(target);
    navigate(`/chatbot/${currChatbotId}/flow-editor/${flow}?${searchParams.toString()}`);
  }, [currChatbotId, actionData, activeFlow, navigate, setSearchParams, searchParams]);

  /**
   * Sync URL to parent node data
   * @param {string} url
   * @dependencies parentId, ID, setActiveFlow
   * @returns {void}
   */
  const syncURLToParentNode = useCallback(
    (url: string) => {
      setActiveFlow(prev => {
        if (isEmpty(prev)) return prev;
        const updatedNodes = updateParentDataURLByActionId({
          flowNodes: cloneDeep(prev.nodes) ?? {},
          parentNodeId: parentId,
          actionId: ID,
          url,
        });

        return { ...prev, nodes: updatedNodes };
      });
    },
    [parentId, ID, setActiveFlow]
  );

  /**
   * Render action settings form based on action type
   * @returns {React.ReactNode}
   * @actionType {EnumActionTypes}
   */
  const renderActionSettingsForm = useCallback(() => {
    switch (actionData?.actionType) {
      case EnumActionTypes.url:
        return (
          <URLActionForm
            defaultValue={get(actionData, 'url', '')}
            onChanged={(url: string) => {
              const updatedData = { ...actionData, url };
              syncURLToParentNode(url);
              handleSubmit?.(updatedData);
            }}
            nodeId={ID}
          />
        );

      case EnumActionTypes.variable:
        return (
          <VariableActionForm
            defaultValue={get(actionData, 'variables', [])}
            onChanged={variablesData => {
              const updatedData = { ...actionData, ...variablesData };
              handleSubmit?.(updatedData);
            }}
            nodeId={ID}
          />
        );

      case EnumActionTypes.node:
        return (
          <GotoNodeActionForm
            defaultValue={
              {
                flow: get(actionData, 'flow', ''),
                target: get(actionData, 'target', ''),
              } as NodeAction
            }
            onChanged={(action: NodeAction) => {
              const updatedData = { ...actionData, ...action };
              handleSubmit?.(updatedData);
            }}
            nodeId={ID}
          />
        );

      default:
        return null;
    }
  }, [ID, actionData, handleSubmit, syncURLToParentNode]);

  return (
    <Transition
      mounted={!isSidebarSettingClosed}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {styles => (
        <Box ref={ref} component='section' className={classes.root} style={styles}>
          {showHeader && (
            <header className={classes.header}>
              <Text className={classes.title}>
                {t('action')}: {t(`actionTypes.${actionData?.actionType}`)}
              </Text>
              {actionData?.actionType === EnumActionTypes.node && (
                <ActionIcon
                  ml='auto'
                  variant='transparent'
                  disabled={!actionData?.flow || !actionData?.target}
                  onClick={jumpToNode}
                  className={cx(classes.jumpToNodeButton, {
                    [classes.disabled]: !actionData?.flow || !actionData?.target,
                  })}
                >
                  <IconCurrentLocation size={20} />
                </ActionIcon>
              )}
            </header>
          )}
          <Box component='section' className={classes.content}>
            <ScrollArea h={height} className={classes.scrollWrapper}>
              {renderActionSettingsForm()}
            </ScrollArea>
          </Box>
        </Box>
      )}
    </Transition>
  );
};

export default memo(withNodeSettings(ActionNodeSettings));
