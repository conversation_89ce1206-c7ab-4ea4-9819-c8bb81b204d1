import { useEffect, useMemo, useCallback } from 'react';
import { useTranslate } from '@tolgee/react';
import { useForm } from 'react-hook-form';
import { Divider, Flex, Text, Transition, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import get from 'lodash/get';

import { BlockNoteMarkdown } from '@resola-ai/blocknote-editor';
import { TBuiltInNode, TTextStepData } from '@/types';
import { FADE_RIGHT_IN } from '@/constants/flow';
import { textSchema } from '@/schemas';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFlowContext } from '@/contexts';
import { tolgee } from '@/tolgee';
import { getVariableSuggestions } from '@/utils/variables';

interface TextNodeSettingsProps {
  stepId: string;
  stepData: TBuiltInNode;
  defaultValues: TTextStepData;
  submit: (data: TBuiltInNode) => void;
  maxHeight?: number;
}

const useStyles = createStyles((theme, { maxHeight }: { maxHeight: number }) => ({
  title: {
    fontSize: theme.fontSizes.xl,
    fontWeight: 700,
    lineHeight: rem(31),
  },
  editor: {
    '&.bn-container': {
      maxHeight: rem(maxHeight),
    },
  },
}));

const SETTING_PANEL_HEADER_OFFSET = 55;
const TextNodeSettings: React.FC<TextNodeSettingsProps> = ({
  stepId,
  stepData,
  defaultValues,
  submit,
  maxHeight = 600,
}) => {
  const { classes } = useStyles({ maxHeight: maxHeight - SETTING_PANEL_HEADER_OFFSET });
  const { t } = useTranslate('nodeSetting');
  const { openSettingDrawer, variables } = useFlowContext();

  const { getValues, setValue, reset } = useForm<TTextStepData>({
    mode: 'onBlur',
    defaultValues,
    resolver: zodResolver(textSchema),
  });

  const onSubmit = useCallback(() => {
    const value = getValues('texts')?.[0]?.trim();

    const newData = {
      ...stepData.data,
      texts: [value],
    } as TTextStepData;

    submit({
      ...stepData,
      data: newData,
    });
  }, [getValues, submit, stepData]);

  const onBlockNoteChange = useCallback(
    (markdown: string) => {
      setValue('texts.0', markdown);
    },
    [setValue]
  );

  useEffect(() => {
    // Add delay to wait for new setting to be mounted, and avoid update old setting
    setTimeout(() => {
      reset(defaultValues);
    }, 0);
  }, [stepId]);

  const initialText = useMemo(() => get(stepData, 'data.texts.0', ''), [stepId]);
  const variableSuggestions = useMemo(() => getVariableSuggestions(variables), [variables]);

  return (
    <Transition
      mounted={openSettingDrawer}
      transition={FADE_RIGHT_IN}
      duration={300}
      timingFunction={'ease'}
    >
      {styles => (
        <form style={styles}>
          <Flex direction='column' gap={rem(12)}>
            <Text className={classes.title}>{t('text')}</Text>
            <Divider />
            <BlockNoteMarkdown
              key={stepId}
              autoFocus
              className={classes.editor}
              initialMarkdown={initialText}
              usingCustomSuggestionVariable
              variableSuggestions={variableSuggestions}
              language={tolgee.getLanguage()}
              onChange={onBlockNoteChange}
              onBlur={onSubmit}
            />
          </Flex>
        </form>
      )}
    </Transition>
  );
};

export default TextNodeSettings;
