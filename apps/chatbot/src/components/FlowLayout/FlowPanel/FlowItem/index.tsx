import { MouseEvent, useCallback, useEffect, useRef, useState, useMemo } from 'react';
import ConfirmModal from '@/components/ConfirmModal';
import { useFlowContext } from '@/contexts/FlowContext';
import { Box, Collapse, Flex, Menu, Text, TextInput, Tooltip, rem } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { useTranslate } from '@tolgee/react';
import { FLOW_TYPES, TFlow } from '@/types';
import { IconChevronRight } from '@tabler/icons-react';
import DropdownItems from '../DropdownItems';
import { flowIcon } from './iconConfig';
import { useNavigate } from 'react-router-dom';
import { useChatbotContext } from '@/contexts';

const useStyles = createStyles(theme => ({
  root: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: `0 ${rem(8)}`,
    marginTop: rem(4),
    height: rem(40),
    borderRadius: rem(4),
    border: '1px solid transparent',
    fontSize: rem(14),
    width: '100%',
    '&:hover': {
      backgroundColor: theme.colors.silverFox[2],
    },
  },
  edit: {
    padding: rem(8),
    [`& .${getStylesRef('flowInput')} input`]: {
      paddingLeft: rem(31),
    },
  },
  selected: {
    borderColor: theme.colors.silverFox[5],
    backgroundColor: theme.colors.silverFox[2],
  },
  iconWrapper: {
    width: rem(20),
    height: rem(20),
    marginRight: rem(8),
    '&:hover': {
      backgroundColor: theme.colors.gray[2],
      borderRadius: rem(4),
    },
  },
  icon: {
    transition: 'all 0.2s',
    strokeWidth: rem(2),
    stroke: theme.colors.silverFox[9],
    '&:hover': {
      stroke: theme.colors.decaNavy[5],
    },
  },
  input: {
    ref: getStylesRef('flowInput'),
    flexGrow: 1,
  },
  flowName: {
    maxWidth: rem(160),
    lineHeight: rem(20),
    fontSize: rem(14),
  },
  rotate90: {
    transform: 'rotate(90deg)',
  },
}));

type FlowItemHandleProps = {
  flow: TFlow;
  setEditMode: (status: boolean) => void;
  className?: string;
  editMode: boolean;
  setIsAdding?: React.Dispatch<React.SetStateAction<boolean>>;
};

const FlowItemViewMode = ({
  flow,
  setEditMode,
  className,
}: Pick<FlowItemHandleProps, 'flow' | 'setEditMode' | 'className'>) => {
  const { t } = useTranslate('flowEditor');
  const navigate = useNavigate();
  const { classes, cx } = useStyles();
  const { activeFlow, deleteFlow, duplicateFlow, saveCurrentFlow } = useFlowContext();
  const { currChatbotId } = useChatbotContext();
  const [opened, { open, close }] = useDisclosure(false);
  const [openedDuplicate, { open: openDuplicate, close: closeDuplicate }] = useDisclosure(false);
  const [openMenuContext, setOpenMenuContext] = useState(false);
  const [openDropdown, setOpenDropdown] = useState(false);

  const handleDeleteConfirm = useCallback(
    (e: MouseEvent) => {
      open();
      e?.preventDefault();
      e?.stopPropagation();
    },
    [open]
  );

  const handleDuplicateConfirm = useCallback(
    (e: MouseEvent) => {
      openDuplicate();
      e?.preventDefault();
      e?.stopPropagation();
    },
    [openDuplicate]
  );

  const handleDelete = useCallback(() => {
    close();
    deleteFlow(flow);
  }, [close, deleteFlow, flow]);

  const handleDuplicate = useCallback(() => {
    closeDuplicate();
    duplicateFlow(flow);
  }, [closeDuplicate, duplicateFlow, flow]);

  const handleContextMenu = useCallback(
    (e: MouseEvent) => {
      e.preventDefault();
      if (flow.type === FLOW_TYPES.normal) {
        setOpenMenuContext(true);
      }
    },
    [flow.type]
  );

  const handleEdit = useCallback(
    (e: MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setEditMode(true);
    },
    [setEditMode]
  );

  const onChangeFlow = useCallback(
    (e: MouseEvent) => {
      if (flow.id === activeFlow?.id) return e.preventDefault();
      saveCurrentFlow();
      navigate(`/chatbot/${currChatbotId}/flow-editor/${flow.id}`);
    },
    [currChatbotId, activeFlow, flow]
  );

  useEffect(() => {
    if (flow.id === activeFlow?.id) setOpenDropdown(true);
  }, [activeFlow, flow]);

  return (
    <>
      <Box
        onClick={onChangeFlow}
        className={cx(classes.root, className, activeFlow?.id === flow.id && classes.selected)}
        key={flow.id}
        onContextMenu={handleContextMenu}
        onBlur={() => setOpenMenuContext(false)}
      >
        <Menu
          shadow='md'
          width={rem(200)}
          withinPortal
          position='right-start'
          withArrow
          arrowPosition='center'
          opened={openMenuContext}
          onChange={() => openMenuContext && setOpenMenuContext(false)}
        >
          <Menu.Target>
            <Flex align='center'>
              <Flex
                justify='center'
                align='center'
                className={classes.iconWrapper}
                onClick={e => {
                  setOpenDropdown(prev => !prev);
                  e.preventDefault();
                  e.stopPropagation();
                }}
              >
                <IconChevronRight
                  className={cx(classes.icon, openDropdown && classes.rotate90)}
                  size={16}
                />
              </Flex>
              <Tooltip
                label={t(flow.name.toLowerCase())}
                multiline
                disabled={flow.type !== 'normal'}
                maw={rem(200)}
                withArrow
                transitionProps={{ duration: 100 }}
                openDelay={500}
              >
                <Text className={classes.flowName} lineClamp={1}>
                  {flow.type === 'normal' ? flow.name : t(flow.name.toLowerCase())}
                </Text>
              </Tooltip>
            </Flex>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item p={rem(10)} onClick={handleEdit}>
              {t('rename')}
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item p={rem(10)} onClick={handleDuplicateConfirm}>
              {t('duplicate')}
            </Menu.Item>
            <Menu.Divider />
            <Menu.Item p={rem(10)} onClick={handleDeleteConfirm}>
              {t('delete')}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Box>
      <Collapse in={openDropdown}>
        <DropdownItems flow={flow} />
      </Collapse>

      <ConfirmModal
        opened={opened}
        close={close}
        title={t('deleteFlowConfirmation')}
        message={t('flowDeleteConfirmationMessage', { name: flow.name })}
        onConfirm={handleDelete}
        btnConfirmLabel={t('delete')}
      />

      <ConfirmModal
        opened={openedDuplicate}
        close={closeDuplicate}
        title={t('duplicateFlowConfirmation')}
        message={t('duplicateFlowConfirmationMessage', { name: flow.name })}
        btnConfirmLabel={t('duplicateFlowConfirmButton')}
        onConfirm={handleDuplicate}
        isDelete={false}
      />
    </>
  );
};

const FlowItemEditMode = ({
  flow,
  setEditMode,
  editMode,
  className,
}: Pick<FlowItemHandleProps, 'flow' | 'setEditMode' | 'editMode' | 'className'>) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate('flowEditor');
  const { activeFlow, updateFlow, setNamingFlowId } = useFlowContext();
  const nameRef = useRef<HTMLInputElement>(null);
  const FlowIcon = flowIcon(flow.type);

  const handleSave = useCallback(() => {
    const newName = nameRef.current?.value.trim();
    if (
      newName &&
      newName !== (flow.type === FLOW_TYPES.normal ? flow.name : t(flow.name.toLowerCase())) &&
      flow
    ) {
      updateFlow({
        ...flow,
        name: newName,
      });
    }
    setNamingFlowId(null);
    setEditMode(false);
  }, [flow, setEditMode, updateFlow, setNamingFlowId]);

  const handleKeyDown = useCallback(
    e => {
      if (e.key === 'Enter') {
        handleSave();
      }
      if (e.key === 'Escape') {
        setEditMode(false);
        setNamingFlowId(null);
      }
    },
    [handleSave, setEditMode, setNamingFlowId]
  );

  useEffect(() => {
    if (editMode) {
      nameRef.current?.select();
    }
  }, [editMode]);

  return (
    <div
      className={cx(
        classes.root,
        classes.edit,
        className,
        activeFlow?.id === flow.id && classes.selected
      )}
    >
      <TextInput
        defaultValue={flow.type === FLOW_TYPES.normal ? flow.name : t(flow.name.toLowerCase())}
        className={classes.input}
        ref={nameRef}
        autoFocus
        onClick={e => e.stopPropagation()}
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        leftSection={<FlowIcon className={classes.icon} size={16} />}
      />
    </div>
  );
};

interface FlowItemProps {
  className?: string;
  flow: TFlow;
}

const FlowItem: React.FC<FlowItemProps> = ({ className, flow }) => {
  const { activeFlow, namingFlowId } = useFlowContext();
  const [editMode, setEditMode] = useState(false);

  const isEditMode = useMemo(
    () => (editMode || namingFlowId === flow.id) && activeFlow?.type === FLOW_TYPES.normal,
    [editMode, activeFlow, flow, namingFlowId]
  );

  return (
    <Box px={rem(12)}>
      {isEditMode ? (
        <FlowItemEditMode
          flow={flow}
          setEditMode={(status: boolean) => {
            setEditMode(status);
          }}
          editMode={isEditMode}
          className={className}
        />
      ) : (
        <FlowItemViewMode
          flow={flow}
          setEditMode={(status: boolean) => {
            setEditMode(status);
          }}
          className={className}
        />
      )}
    </Box>
  );
};

export default FlowItem;
