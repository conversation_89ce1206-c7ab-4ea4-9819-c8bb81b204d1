{"name": "crm", "private": true, "version": "1.14.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "cleanup": "npx rimraf node_modules", "release": "standard-version -t crm@", "release:minor": "standard-version -t crm@ --release-as minor", "release:patch": "standard-version -t crm@ --release-as patch", "release:major": "standard-version -t crm@ --release-as major", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emoji-mart/data": "1.1.2", "@emoji-mart/react": "1.1.0", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@resola-ai/models": "workspace:*", "@resola-ai/schema": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tanstack/react-virtual": "3.11.2", "@tiptap/core": "2.10.4", "@tiptap/extension-character-count": "^2.8.0", "@tiptap/extension-font-family": "^2.9.1", "@tiptap/extension-hard-break": "^2.8.0", "@tiptap/extension-heading": "^2.9.0", "@tiptap/extension-link": "^2.1.16", "@tiptap/extension-mention": "^2.11.7", "@tiptap/extension-placeholder": "^2.1.16", "@tiptap/extension-text-align": "^2.9.0", "@tiptap/extension-text-style": "^2.9.1", "@tiptap/extension-underline": "^2.2.4", "@tiptap/pm": "^2.1.16", "@tiptap/react": "^2.1.16", "@tiptap/starter-kit": "^2.1.16", "@tolgee/react": "^5.29.1", "@tolgee/web": "^5.29.2", "axios": "^1.8.2", "dayjs": "^1.11.12", "dompurify": "3.2.4", "dotenv": "16.3.1", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "jwt-decode": "^3.1.2", "lodash": "^4.17.21", "mantine-react-table": "2.0.0-beta.7", "react": "18.2.0", "react-dom": "18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-i18next": "14.0.1", "react-pdf": "^9.2.1", "react-router-dom": "6.21.3", "standard-version": "^9.5.0", "swr": "^2.2.2", "timezone": "link:@dayjs/plugin/timezone", "timezones-list": "^3.0.3", "tippy.js": "^6.3.7", "uuid": "^9.0.1", "zod": "^3.24.1"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@testing-library/user-event": "^14.6.1", "@types/lodash": "^4.14.199", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "2.1.9", "@vitest/ui": "2.1.9", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "handlebars": "4.7.8", "jest-environment-jsdom": "^29.7.0", "typescript": "5.6.3", "vite": "5.4.19", "vite-plugin-circular-dependency": "^0.4.1", "vite-tsconfig-paths": "^4.2.0", "vitest": "2.1.9"}}