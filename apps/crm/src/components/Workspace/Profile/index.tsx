import { HEIGHT_OF_HEADER } from '@/constants';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { SortableContext } from '@dnd-kit/sortable';
import {
  ActionIcon,
  Box,
  Divider,
  Drawer,
  Flex,
  LoadingOverlay,
  ScrollArea,
  Tabs,
  Text,
  rem,
} from '@mantine/core';
import { useToggle } from '@mantine/hooks';
import {
  IconArrowsMaximize,
  IconArrowsMinimize,
  IconChevronDown,
  IconChevronUp,
  IconChevronsRight,
  IconDirections,
  IconFileAnalytics,
  IconFloatLeft,
  IconHistory,
  IconKeyframes,
} from '@tabler/icons-react';
import React, { Suspense, useCallback, useEffect, useMemo, useState } from 'react';
import { useWorkspaceContext } from '@/contexts/WorkspaceContext';
import BoxCardAdvance from './BoxCardAdvance';
import ObjectFields from './ObjectFields';
import ProfileContact from './ProfileContact';
import { BOX_CARDS, ProfileContextProvider, useProfileContext } from '@/contexts/ProfileContext';
import Tags from './Tags';
import { PREFERENCES } from '@/constants/workspace';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useParams } from 'react-router-dom';
import Breadcrumbs from './Breadcrumbs';
import { isPermissionAllowed, PERMISSION_KEYS } from '@resola-ai/ui/components/DecaTable/utils';
import NoPermissionAccess from '../NoPermissionAccess';
import { If } from '@resola-ai/ui';

// Lazy load tab components
const Activities = React.lazy(() => import('./Activities'));
const Identities = React.lazy(() => import('./Identities'));
const Files = React.lazy(() => import('./Files'));
const History = React.lazy(() => import('./History'));
const LongText = React.lazy(() => import('./LongText'));
const Forms = React.lazy(() => import('./Forms'));

interface ITab {
  icon: React.ReactNode;
  component: React.ReactNode;
  value: string;
}

const useStyles = createStyles(theme => ({
  drawer: {
    '& .mantine-Drawer-content': {
      borderLeft: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTop: `${rem(1)} solid ${theme.colors.decaBlue[5]}`,
      borderTopLeftRadius: rem(6),
      boxShadow: `0 0 ${rem(4)} 0 ${theme.colors.decaBlue[5]}`,
      willChange: 'unset !important',
    },
    '& .mantine-Drawer-inner': {
      top: HEIGHT_OF_HEADER,
      height: `calc(100vh - ${HEIGHT_OF_HEADER}px)`,
    },
    '& .mantine-Drawer-body': {
      padding: 0,
      height: 'calc(100% - 22px)',
    },
  },
  header: {
    svg: {
      width: rem(18),
      color: theme.colors.decaDark[1],
    },
  },
  leftBoxCardAdvance: {
    position: 'relative',
    height: '100%',
    flexGrow: 1,
    background: theme.colors.decaLight[1],
  },

  tabList: {
    svg: {
      width: rem(18),
      color: theme.colors.decaDark[1],
    },
    '& button': {
      fontSize: rem(14),
      '&[data-active]': {
        borderColor: theme.colors.decaNavy[4],
        color: theme.colors.decaNavy[4],
        svg: {
          color: theme.colors.decaNavy[4],
        },
      },
    },
  },
  tabPanel: {
    padding: rem(16),
    borderLeft: `2px solid ${theme.colors.decaLight[1]}`,
    height: 'calc(100% - 42px)',
    overflowY: 'auto',
    overflowX: 'hidden',
  },
  disabled: {
    pointerEvents: 'none',
  },
}));

// Component references defined outside the component to avoid recreating on each render
const tabIcons = {
  [PREFERENCES.activities]: <IconDirections />,
  [PREFERENCES.identities]: <IconKeyframes />,
  [PREFERENCES.files]: <IconFileAnalytics />,
  [PREFERENCES.history]: <IconHistory />,
  [PREFERENCES.longText]: <IconFloatLeft />,
};

// Loading component for Suspense fallback
const TabLoading = () => (
  <Box pos='relative' h='100%'>
    <LoadingOverlay visible={true} loaderProps={{ size: 'sm' }} />
  </Box>
);

const ProfileSettings = () => {
  const { t } = useTranslate('workspace');
  const { opened, data, currRecordIndex, object, activeView, openProfile, viewLoading } =
    useWorkspaceContext();
  const { classes } = useStyles();
  const {
    onSetDragging: setDragging,
    handleDragEnd,
    handleChangeSize,
    sortItems,
    getForm,
    closeProfile,
  } = useProfileContext();
  const { recordId } = useParams();
  const canReadView = isPermissionAllowed(activeView?.permission || {}, PERMISSION_KEYS.VIEW_READ);
  const [isFullview, toggleFullview] = useToggle([false, true]);
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  const objectFieldsCard = sortItems.find(item => item.id === BOX_CARDS.objectFields) || {
    id: BOX_CARDS.objectFields,
    height: 'auto',
  };

  const allowOrderCards = sortItems.filter(item => {
    if (item.id === 'tags') {
      return object?.hasTags;
    }
    return item.id !== BOX_CARDS.objectFields;
  });

  // Use useMemo to create tab configurations, but only references to components - not the actual components
  const tabs = useMemo(() => {
    const newTabs: ITab[] = [];

    if (!object?.profileSettings) return newTabs;
    // Iterate through profileSettings array to maintain order
    object.profileSettings.forEach(setting => {
      if (setting.enabled) {
        if (activeView?.displayLongText?.length && setting.type === 'pinLongText') {
          newTabs.push({
            value: PREFERENCES.longText,
            component: PREFERENCES.longText,
            icon: tabIcons[PREFERENCES.longText],
          });
        }
        if (setting.type === 'tasks') {
          newTabs.push({
            value: PREFERENCES.history,
            component: PREFERENCES.history,
            icon: tabIcons[PREFERENCES.history],
          });
        }
        if (setting.type === 'attachments') {
          newTabs.push({
            value: PREFERENCES.files,
            component: PREFERENCES.files,
            icon: tabIcons[PREFERENCES.files],
          });
        }
        if (setting.type === 'activities') {
          newTabs.push({
            value: PREFERENCES.activities,
            component: PREFERENCES.activities,
            icon: tabIcons[PREFERENCES.activities],
          });
        }
        if (setting.type === 'identities') {
          newTabs.push({
            value: PREFERENCES.identities,
            component: PREFERENCES.identities,
            icon: tabIcons[PREFERENCES.identities],
          });
        }
      }
    });
    return newTabs;
  }, [object?.profileSettings, activeView?.displayLongText]);

  // Set active tab only when needed: when tabs change or current record changes
  useEffect(() => {
    if (tabs.length > 0 && (!activeTab || !tabs.some(tab => tab.value === activeTab))) {
      setActiveTab(tabs[0]?.value);
    }
  }, [tabs, currRecordIndex, activeTab]);

  const moveProfile = useCallback(
    (direction: 'prev' | 'next') => {
      const moveToIndex = direction === 'prev' ? currRecordIndex - 1 : currRecordIndex + 1;
      if (moveToIndex >= 0 && moveToIndex < data.length) {
        openProfile(data[moveToIndex].id, data[moveToIndex]);
      }
    },
    [currRecordIndex, data, openProfile]
  );

  const hasTabs = tabs.length > 0;

  // Render the correct component based on tab value
  const renderTabComponent = (tabValue: string) => {
    switch (tabValue) {
      case PREFERENCES.activities:
        return <Activities />;
      case PREFERENCES.identities:
        return <Identities />;
      case PREFERENCES.files:
        return <Files />;
      case PREFERENCES.history:
        return <History />;
      case PREFERENCES.longText:
        return <LongText />;
      default:
        return null;
    }
  };

  const renderBoxCardAdvance = boxCard => {
    switch (boxCard.id) {
      case 'tags':
        return <Tags />;
      default:
        return (
          <Suspense fallback={<TabLoading />}>
            <Forms form={getForm(boxCard.id)} />
          </Suspense>
        );
    }
  };

  const recordIdIndex = useMemo(() => {
    return `${recordId}_${currRecordIndex}`;
  }, [recordId, currRecordIndex]);

  return (
    <Drawer
      data-testid='profile-drawer'
      opened={opened}
      onClose={closeProfile}
      position='right'
      size={isFullview ? '100vw' : '70vw'}
      withCloseButton={false}
      className={classes.drawer}
      transitionProps={{
        transition: 'slide-left',
        duration: 500,
        timingFunction: 'ease-in-out',
      }}
      overlayProps={{ opacity: 0.1, blur: 0 }}>
      <>
        <If condition={viewLoading}>
          <LoadingOverlay visible={true} />
        </If>
        <If condition={!viewLoading && !!activeView && !canReadView}>
          <NoPermissionAccess />
        </If>
        <If condition={!viewLoading && canReadView}>
          <>
            <Flex className={classes.header} px={rem(16)} py={rem(6)} data-testid='profile-header'>
              <Flex align='center' gap={rem(4)}>
                <ActionIcon onClick={closeProfile} variant='subtle' c={'decaGrey.5'}>
                  <IconChevronsRight />
                </ActionIcon>
                <ActionIcon
                  data-testid='fullview-toggle'
                  onClick={() => toggleFullview()}
                  variant='subtle'
                  c={'decaGrey.5'}>
                  {isFullview ? <IconArrowsMinimize /> : <IconArrowsMaximize />}
                </ActionIcon>
                {currRecordIndex >= 0 && (
                  <>
                    <ActionIcon
                      variant='subtle'
                      c={'decaGrey.5'}
                      onClick={() => moveProfile('prev')}
                      className={currRecordIndex <= 0 ? classes.disabled : ''}>
                      <IconChevronUp />
                    </ActionIcon>
                    <ActionIcon
                      variant='subtle'
                      c={'decaGrey.5'}
                      onClick={() => moveProfile('next')}
                      className={currRecordIndex >= data.length - 1 ? classes.disabled : ''}>
                      <IconChevronDown />
                    </ActionIcon>
                  </>
                )}
              </Flex>
              <Flex align='center' style={{ flex: 1 }}>
                <Breadcrumbs />
              </Flex>
            </Flex>
            <Divider />
            <Flex w={'inherit'} h={'inherit'} sx={{ overflow: 'hidden' }}>
              <ScrollArea
                type='hover'
                w={hasTabs ? '50%' : '100%'}
                h={'100%'}
                p={rem(16)}
                className={classes.leftBoxCardAdvance}>
                <Flex gap={rem(16)} direction='column'>
                  <ProfileContact />
                  <BoxCardAdvance
                    recordId={recordIdIndex}
                    boxCard={objectFieldsCard}
                    onChangeSize={handleChangeSize}
                    allowDrag={false}>
                    <ObjectFields />
                  </BoxCardAdvance>
                  <DndContext
                    modifiers={[restrictToVerticalAxis]}
                    sensors={sensors}
                    onDragStart={() => {
                      setDragging(true);
                    }}
                    onDragEnd={({ active, over }) => {
                      setDragging(false);
                      if (over && active.id !== over?.id) {
                        handleDragEnd(active, over);
                      }
                    }}>
                    <SortableContext items={allowOrderCards}>
                      {allowOrderCards.map(boxCard =>
                        boxCard.id === 'forms' &&
                        (object?.childObjects?.length ?? 0) === 0 ? null : (
                          <BoxCardAdvance
                            key={boxCard?.id}
                            recordId={currRecordIndex?.toString() || ''}
                            boxCard={boxCard}
                            onChangeSize={handleChangeSize}>
                            {renderBoxCardAdvance(boxCard)}
                          </BoxCardAdvance>
                        )
                      )}
                    </SortableContext>
                  </DndContext>
                </Flex>
              </ScrollArea>
              {(hasTabs || !!activeView?.displayLongText?.length) && activeTab && (
                <Box w='50%' bg='decaLight.0'>
                  <Tabs value={activeTab} h={'100%'} onChange={setActiveTab}>
                    <Tabs.List className={classes.tabList}>
                      {tabs.map((tab, i) => (
                        <Tabs.Tab
                          key={i}
                          value={tab.value}
                          data-testid={`tab-${tab.value}`}
                          leftSection={tab.icon}>
                          <Text fw={500}>{t(tab.value)}</Text>
                        </Tabs.Tab>
                      ))}
                    </Tabs.List>
                    <Tabs.Panel value={activeTab} className={classes.tabPanel}>
                      <Suspense fallback={<TabLoading />}>{renderTabComponent(activeTab)}</Suspense>
                    </Tabs.Panel>
                  </Tabs>
                </Box>
              )}
            </Flex>
          </>
        </If>
      </>
    </Drawer>
  );
};

export const Profile = () => {
  return (
    <ProfileContextProvider>
      <ProfileSettings />
    </ProfileContextProvider>
  );
};

export default React.memo(Profile);
