import '@testing-library/jest-dom';
import { expect, afterEach, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import * as matchers from '@testing-library/jest-dom/matchers';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };

  Object.defineProperty(window, 'ResizeObserver', {
    writable: true,
    value: vi.fn().mockImplementation(() => ({
      disconnect: vi.fn(),
      observe: vi.fn(),
      unobserve: vi.fn(),
    })),
  });

  // Mock requestAnimationFrame and cancelAnimationFrame
  Object.defineProperty(global, 'requestAnimationFrame', {
    writable: true,
    value: vi.fn(callback => setTimeout(callback, 16)),
  });

  Object.defineProperty(global, 'cancelAnimationFrame', {
    writable: true,
    value: vi.fn(id => clearTimeout(id)),
  });
});

vi.mock('@tolgee/react', () => ({
  TolgeeProvider: ({ children }) => children,
  useTranslate: () => ({
    t: vi.fn(key => key),
  }),
  useTolgee: () => ({
    tolgee: {
      getLanguage: vi.fn(),
      changeLanguage: vi.fn(),
    },
  }),
}));

// Extend Vitest's expect method with testing-library methods
expect.extend(matchers);

// Cleanup after each test case (e.g. clearing jsdom)
afterEach(() => {
  cleanup();
});

// Mock matchMedia for testing
if (typeof window !== 'undefined') {
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(),
      removeListener: vi.fn(),
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });
}

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});

global.requestAnimationFrame = callback => {
  return setTimeout(callback, 16); // 16ms ≈ 60fps
};

global.cancelAnimationFrame = id => {
  clearTimeout(id);
};

vi.mock('gsap/ScrollTrigger', () => ({
  ScrollTrigger: {
    create: jest.fn(),
    refresh: jest.fn(),
    update: jest.fn(),
    killAll: jest.fn(),
    register: jest.fn(),
  },
}));

vi.mock('gsap/dist/ScrollTrigger', () => ({
  ScrollTrigger: {
    create: jest.fn(),
    refresh: jest.fn(),
    update: jest.fn(),
    killAll: jest.fn(),
    register: jest.fn(),
  },
}));
