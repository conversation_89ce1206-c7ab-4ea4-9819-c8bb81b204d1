{"action": {"add": "追加", "cancel": "キャンセル", "remove": "削除"}, "agent": {"processing": "「リクエストを処理中です、少々お待ちください！」"}, "builderLayout": {"apply": "適用", "cancel": "キャンセル", "publish": "公開", "published": "公開成功", "run": "実行"}, "button": {"add": "追加", "cancel": "キャンセル", "close": "閉じる", "confirm": "確認", "delete": "削除", "duplicate": "複製", "edit": "編集", "export": "エクスポート", "reconnect": "再接続", "remove": "削除", "save": "保存", "saveAsTemplate": "テンプレートとして保存", "test": "テスト"}, "error": {"pattern": "値は{pattern}のパターンに一致する必要があります", "required": "必須項目です"}, "historyRuns": {"error": "エラー", "failed": "失敗", "input": "入力", "logs": "ログ", "noError": "エラーは発生しませんでした", "noInput": "入力が提供されていません。", "noLogs": "現在、ログはありません。", "noOutput": "現在、出力はありません。", "output": "出力", "running": "実行中", "success": "成功", "title": "最近の実行"}, "instructions": {"generate": "生成", "placeholder": "エージェントの指示を入力してください", "title": "指示"}, "key": "キー", "messageBuilder": {"copy": "コピー", "replay": "繰り返し"}, "pageHeader": {"buttonMenuActions": {"fromScratch": "ゼロから作成", "fromTemplate": "テンプレートから作成", "importFromFile": "ファイルからインポート"}}, "settings": {"model": {"info": {"frequencyPenalty": "頻度ペナルティ:", "presencePenalty": "存在ペナルティ:", "temperature": "温度:", "textFormat": "応答形式:", "textFormatValue": "text", "tokens": "トークン数:", "toolChoice": "tool_choice:", "toolChoiceValue": "web_search_preview", "topP": "Top P:"}, "label": "モデル", "multiAgents": {"collaboration": {"addCollaborator": "新しい協力者を追加", "agent": "協力者エージェント", "agentPlaceholder": "協力者エージェントを選択してください", "collaborator": {"description": "マルチエージェント協力により、このエージェントは他のエージェントにタスクを割り当てることができます。監督エージェントとして、それはその協力者エージェントからの応答を収集して整理します。", "title": "協力者"}, "description": "マルチエージェント協力により、このエージェントは他のエージェントにタスクを割り当てることができます。監督エージェントとして、それはその協力者エージェントからの応答を収集して整理します。", "editCollaborator": "協力者を編集", "handoffInstruction": "協力者の手渡し指示", "handoffInstructionDescription": "マルチエージェント協力でのエージェントの役割を説明します。明確で具体的な例を使用することをお勧めします。また、スタイルとトーンを好みに応じて定義することもできます。エージェントを参照する際は、その協力者の名前を使用してください。", "mode": {"description": "このエージェントが応答を管理する方法を選択します:", "options": {"routing": {"description": "エージェントは応答を適切な協力者エージェントにルーティングします。", "title": "ルーティング"}, "supervisor": {"description": "エージェントは協力者エージェントからの応答を収集して、最終的な応答を生成します。", "title": "監督"}}, "title": "協力モード"}, "name": "協力者名", "nameDescription": "協力者の名前を入力してください", "noAgent": "まだ協力者エージェントが存在しません。まず作成してください。", "title": "マルチエージェント協力"}, "mode": {"description": "このエージェントが応答を管理する方法を選択します:", "title": "協力モード"}, "title": "エージェント"}, "placeholder": "モデルを選択", "settings": {"frequencyPenalty": "頻度ペナルティ", "maxTokens": "トークン数", "presencePenalty": "存在ペナルティ", "responseFormat": "応答形式", "stopSequences": "停止シーケンス", "stopSequencesPlaceholder": "シーケンスを入力してTabキーを押します", "temperature": "温度", "topP": "Top P"}, "tools": {"all": "すべて", "builtIn": "組み込みツール", "city": "都市", "configureWebsearch": "Web検索ツールの設定", "country": "国", "function": "関数", "large": "大", "medium": "中", "noBuiltInTools": "組み込みツールはありません", "region": "地域", "searchContextSize": "検索コンテキストサイズ", "searchPlaceholder": "検索ツールと機能...", "selectCountry": "国を選択...", "selectTimezone": "タイムゾーンを選択...", "small": "小", "timezone": "タイムゾーン", "title": "ツール", "tool": "ツール", "websearch": "Web検索"}}, "title": "設定"}, "systemMessageSettings": {"message": {"ai": "AIメッセージ", "assistant": "アシスタントメッセージ", "placeholder": "システムメッセージを入力してください", "user": "ユーザーメッセージ"}, "title": "メッセージ"}, "unsavedChangesModal": {"cancel": "キャンセル", "confirm": "保存せずに続行", "description": "続行しますか？未保存の変更は失われます。必ず変更を公開してください", "title": "未保存の変更"}, "value": "値", "versionControl": {"createVersion": "バージョン履歴に追加", "descriptionPlaceholder": "変更を説明", "editVersion": "バージョン情報編集", "name": "バージョン名", "rename": "名前変更", "restore": "復元", "searchVersion": "バージョン検索", "title": "バージョン", "titlePlaceholder": "タイトル", "restoreThisVersion": "このバージョンを復元", "restoreVersionDescription": "このバージョンを復元しますか？現在の変更は上書きされます。\nまた、現在の変更を保存してから復元することもできます。", "saveAndRestore": "保存して復元", "cancel": "キャンセル", "restoreSuccess": "“{name}”を復元しました。", "saveAndRestoreSuccess": "現在のプロンプトを新しいバージョンとして保存しました。“{name}”を復元しました。"}}