import { useCallback } from 'react';
import { Box, Flex } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useNavigate, useParams } from 'react-router-dom';
import { FunctionContextProvider, useFunctionContext } from '@/contexts/Function/FunctionContext';
import {
  FunctionBuilderContextProvider,
  useFunctionBuilderContext,
} from '@/contexts/Function/FunctionBuilderContext';
import BuilderLayout from '@/components/BuilderLayout';
import { LeftBuilderSection, RightBuilderSection, AIGenerateSection } from '@/components/Functions';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import { useTranslate } from '@tolgee/react';
import RunWrapper from '@/components/BuilderLayout/RunWrapper';
import { useAppContext } from '@/contexts/AppContext';
import VariablesWrapper from '@/components/BuilderLayout/VariablesWrapper';
import { VersionContextProvider } from '@/contexts/VersionContext';

const useStyles = createStyles(theme => ({
  container: {
    flex: 1,
    minWidth: 0,
    height: '100%',
  },
  leftSection: {
    flex: 7,
    width: '100%',
    minWidth: 0,
    height: '100%',
  },
  rightSection: {
    flex: 3,
    backgroundColor: theme.colors.decaMono[1],
    borderLeft: `1px solid ${theme.colors.decaNavy[1]}`,
  },
  col2: {
    flex: 2,
  },
  col5: {
    flex: 5,
  },
  col7: {
    flex: 7,
  },
  col3: {
    flex: 3,
  },
}));

const FunctionBuilder = () => {
  const { t } = useTranslate('function');
  const navigate = useNavigate();
  const { workspaceId, functionId } = useParams();
  const { cx, classes } = useStyles();
  const { updateFunction, createFunction, executeFunction, deleteFunction, duplicateFunction } =
    useFunctionContext();
  const {
    isExpandOutputExample,
    showExecuteResults,
    showInputParametersPanel,
    currentFunction,
    mutateCurrentFunction,
    form,
    showAIGenerate,
    handlePanelState,
  } = useFunctionBuilderContext();
  const { openConfirmModal, closeConfirmModal } = useAppContext();

  const titleValue = !functionId ? 'Untitled Function' : currentFunction?.name || '';

  const goBack = useCallback(() => {
    navigate(`/studio/${workspaceId}/functions`);
  }, [navigate, workspaceId]);

  const handleTitleChange = useCallback(
    async (name: string) => {
      if (!currentFunction?.id) return;
      await updateFunction({ ...currentFunction, name });
      await mutateCurrentFunction();
    },
    [currentFunction, updateFunction, mutateCurrentFunction]
  );

  const handleOpenInputParametersPanel = useCallback(() => {
    handlePanelState(true, false, false, false);
  }, [handlePanelState]);

  const handleOpenExecuteResults = useCallback(() => {
    if (!functionId) return;
    const codeValue = form.getValues('code') ?? '';
    const inputParameters = form.getValues('settings.input') ?? [];
    const inputList = Array.isArray(inputParameters)
      ? inputParameters.reduce(
          (acc, item) => {
            acc[item.name] = item.defaultValue ?? '';
            return acc;
          },
          {} as Record<string, string>
        )
      : {};

    executeFunction(inputList, codeValue, functionId);
    handlePanelState(false, true, false, false);
  }, [handlePanelState, form, functionId]);

  const handlePublish = useCallback(async () => {
    const payload = form.getValues();
    const { name, settings } = payload;

    if (!name || name.trim() === '') {
      form.setError('name', {
        type: 'required',
        message: t('functionBuilder.nameRequired'),
      });
      return;
    } else {
      form.clearErrors('name');
    }

    const customPayload = {
      ...payload,
      settings: {
        ...settings,
        output: {
          ...settings.output,
          properties: {
            processed: {
              type: 'string',
            },
          },
        },
      },
    };

    if (!functionId) {
      const newFunction = await createFunction(customPayload);
      if (newFunction?.id) {
        navigate(`/studio/${workspaceId}/function/${newFunction.id}`);
      }
    } else {
      await updateFunction({ ...currentFunction, ...customPayload });
      await mutateCurrentFunction();
    }
  }, [
    form,
    createFunction,
    updateFunction,
    functionId,
    mutateCurrentFunction,
    navigate,
    workspaceId,
    currentFunction,
  ]);

  const handleDelete = useCallback(async () => {
    if (!functionId) return;

    openConfirmModal({
      title: t('deleteModalTitle'),
      name: currentFunction?.name ?? '',
      onConfirm: async () => {
        await deleteFunction(functionId);
        closeConfirmModal();
        navigate(`/studio/${workspaceId}/functions`);
      },
      onCancel: closeConfirmModal,
    });
  }, [deleteFunction, functionId, navigate, workspaceId, currentFunction, closeConfirmModal]);

  const handleDuplicate = useCallback(() => {
    if (!functionId) return;

    duplicateFunction(functionId);
    navigate(`/studio/${workspaceId}/functions`);
  }, [duplicateFunction, functionId, navigate, workspaceId]);

  return (
    <BuilderLayout
      onBack={goBack}
      title={titleValue}
      onTitleChange={handleTitleChange}
      onPublish={handlePublish}
      variablesControl={
        <VariablesWrapper
          resourceId={functionId ?? ''}
          openVariables={handleOpenInputParametersPanel}
        />
      }
      onRun={handleOpenExecuteResults}
      versionControl={<InnerVersionControl resourceId={functionId ?? ''} resourceType='function' />}
      historyRuns={<RunWrapper resourceId={functionId ?? ''} resourceType='function' />}
      seeMoreActions={{
        editLabel: 'Edit',
        onDuplicate: handleDuplicate,
        onDelete: handleDelete,
      }}
    >
      <Flex w='100%' className={classes.container} data-testid='function-builder'>
        <Box
          className={cx(classes.leftSection, {
            [classes.col7]: !showInputParametersPanel,
            [classes.col2]: showExecuteResults && showInputParametersPanel,
            [classes.col5]: isExpandOutputExample,
            [classes.col3]: showAIGenerate,
          })}
        >
          <LeftBuilderSection />
        </Box>
        {showAIGenerate && (
          <Box className={classes.col3}>
            <AIGenerateSection />
          </Box>
        )}
        <Box
          className={cx(classes.rightSection, {
            [classes.col3]: isExpandOutputExample,
          })}
        >
          <RightBuilderSection />
        </Box>
      </Flex>
    </BuilderLayout>
  );
};

const FunctionBuilderPage = () => {
  const { functionId } = useParams();

  return (
    <FunctionContextProvider>
      <FunctionBuilderContextProvider>
        <VersionContextProvider resourceType='function' resourceId={functionId || ''}>
          <FunctionBuilder />
        </VersionContextProvider>
      </FunctionBuilderContextProvider>
    </FunctionContextProvider>
  );
};

export default FunctionBuilderPage;
