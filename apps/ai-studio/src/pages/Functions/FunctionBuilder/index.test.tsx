import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import { renderWithMantine } from '@/utils/test';
import { BrowserRouter, useNavigate, useParams } from 'react-router-dom';
import { AppContextProvider } from '@/contexts/AppContext';
import { FunctionContextProvider } from '@/contexts/Function/FunctionContext';
import { FunctionBuilderContextProvider } from '@/contexts/Function/FunctionBuilderContext';
import FunctionBuilder from './index';
import userEvent from '@testing-library/user-event';

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(),
    useParams: vi.fn(),
  };
});

vi.mock('@/contexts/Function/FunctionContext', async () => {
  const actual = await vi.importActual('@/contexts/Function/FunctionContext');
  return {
    ...actual,
    useFunctionContext: vi.fn().mockReturnValue({
      updateFunction: vi.fn().mockResolvedValue({}),
      createFunction: vi.fn().mockResolvedValue({ id: 'new-function-id' }),
      executeFunction: vi.fn().mockResolvedValue({}),
    }),
  };
});

vi.mock('@/contexts/Function/FunctionBuilderContext', async () => {
  const actual = await vi.importActual('@/contexts/Function/FunctionBuilderContext');
  return {
    ...actual,
    useFunctionBuilderContext: vi.fn().mockReturnValue({
      isExpandOutputExample: false,
      setIsExpandOutputExample: vi.fn(),
      setShowInputParametersPanel: vi.fn(),
      showExecuteResults: false,
      showInputParametersPanel: false,
      setShowExecuteResults: vi.fn(),
      currentFunction: { id: 'function-id', name: 'Test Function' },
      mutateCurrentFunction: vi.fn().mockResolvedValue({}),
      executeFunction: vi.fn().mockResolvedValue({}),
      form: {
        getValues: vi.fn().mockImplementation(path => {
          if (path === 'code') return 'test code';
          if (path === 'settings.input') return [{ name: 'test', defaultValue: 'test value' }];
          if (path === 'name') return 'Test Function';
          return { name: 'Test Function', settings: { input: [], output: {} } };
        }),
        setError: vi.fn(),
        clearErrors: vi.fn(),
      },
    }),
  };
});

//inner version control
vi.mock('@/components/BuilderLayout/VersionControlWrapper', async () => {
  const actual = await vi.importActual('@/components/BuilderLayout/VersionControlWrapper');
  return {
    ...actual,
    InnerVersionControl: () => <div data-testid='version-control-wrapper'>Version Control</div>,
  };
});

vi.mock('@/components/BuilderLayout/HistoryRuns', () => ({
  default: () => <div data-testid='history-runs'>History Runs</div>,
}));

vi.mock('@/components/Functions/LeftBuilderSection', () => ({
  default: () => <div data-testid='left-builder-section'>Left Builder Section</div>,
}));

vi.mock('@/components/Functions/RightBuilderSection', () => ({
  default: () => <div data-testid='right-builder-section'>Right Builder Section</div>,
}));

describe('FunctionBuilder', () => {
  const mockNavigate = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useNavigate as any).mockReturnValue(mockNavigate);
    (useParams as any).mockReturnValue({ workspaceId: 'workspace-id', functionId: 'function-id' });
  });

  const renderWithProvider = () => {
    return renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <FunctionContextProvider>
            <FunctionBuilderContextProvider>
              <FunctionBuilder />
            </FunctionBuilderContextProvider>
          </FunctionContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );
  };

  it('renders the component correctly', () => {
    renderWithProvider();

    expect(screen.getByTestId('function-builder')).toBeInTheDocument();
    expect(screen.getByTestId('left-builder-section')).toBeInTheDocument();
    expect(screen.getByTestId('right-builder-section')).toBeInTheDocument();
  });

  it('renders the builder layout with correct title', () => {
    renderWithProvider();

    expect(screen.getByText('Test Function')).toBeInTheDocument();
  });

  it('should navigate back when back button is clicked', async () => {
    renderWithProvider();

    const backButton = screen.getByTestId('header-back-button');
    fireEvent.click(backButton);

    expect(mockNavigate).toHaveBeenCalledWith('/studio/workspace-id/functions');
  });

  it('should handle title change', async () => {
    const { useFunctionContext } = await import('@/contexts/Function/FunctionContext');
    const mockUpdateFunction = vi.fn().mockResolvedValue({});
    const mockMutateCurrentFunction = vi.fn().mockResolvedValue({});

    (useFunctionContext as any).mockReturnValue({
      updateFunction: mockUpdateFunction,
      createFunction: vi.fn(),
      executeFunction: vi.fn(),
    });

    const { useFunctionBuilderContext } = await import(
      '@/contexts/Function/FunctionBuilderContext'
    );
    (useFunctionBuilderContext as any).mockReturnValue({
      isExpandOutputExample: false,
      setIsExpandOutputExample: vi.fn(),
      setShowInputParametersPanel: vi.fn(),
      showExecuteResults: false,
      showInputParametersPanel: false,
      setShowExecuteResults: vi.fn(),
      currentFunction: { id: 'function-id', name: 'Test Function' },
      mutateCurrentFunction: mockMutateCurrentFunction,
      form: {
        getValues: vi.fn().mockImplementation(path => {
          if (path === 'code') return 'test code';
          if (path === 'settings.input') return [{ name: 'test', defaultValue: 'test value' }];
          if (path === 'name') return 'Test Function';
          return { name: 'Test Function', settings: { input: [], output: {} } };
        }),
        setError: vi.fn(),
        clearErrors: vi.fn(),
      },
    });

    renderWithProvider();
    const user = userEvent.setup();

    // Find the edit button with the check icon (green color)
    const editButton = screen.getByRole('button', { name: 'title-name-edit-button' });
    await user.click(editButton);

    const titleInput = screen.getByTestId('title-name-input');
    await user.clear(titleInput);
    await user.type(titleInput, 'New Function Name');

    // Click the check button to confirm the edit (green color)
    const confirmButton = screen.getAllByRole('button', { name: 'title-name-edit-button' })[0];
    await user.click(confirmButton);

    await waitFor(() => {
      expect(mockUpdateFunction).toHaveBeenCalledWith({
        id: 'function-id',
        name: 'New Function Name',
      });
      expect(mockMutateCurrentFunction).toHaveBeenCalled();
    });
  });

  it('should handle publishing an existing function when functionId is provided', async () => {
    const { useFunctionContext } = await import('@/contexts/Function/FunctionContext');
    const mockUpdateFunction = vi.fn().mockResolvedValue({});
    const mockMutateCurrentFunction = vi.fn().mockResolvedValue({});

    (useFunctionContext as any).mockReturnValue({
      updateFunction: mockUpdateFunction,
      createFunction: vi.fn(),
      executeFunction: vi.fn(),
    });

    const { useFunctionBuilderContext } = await import(
      '@/contexts/Function/FunctionBuilderContext'
    );
    (useFunctionBuilderContext as any).mockReturnValue({
      isExpandOutputExample: false,
      setIsExpandOutputExample: vi.fn(),
      setShowInputParametersPanel: vi.fn(),
      showExecuteResults: false,
      showInputParametersPanel: false,
      setShowExecuteResults: vi.fn(),
      currentFunction: { id: 'function-id', name: 'Test Function' },
      mutateCurrentFunction: mockMutateCurrentFunction,
      form: {
        getValues: vi.fn().mockReturnValue({
          name: 'Test Function',
          settings: {
            input: [],
            output: { type: 'json_object' },
          },
        }),
        setError: vi.fn(),
        clearErrors: vi.fn(),
      },
    });

    renderWithProvider();

    const publishButton = screen.getByTestId('builder-layout-publish-button');
    fireEvent.click(publishButton);

    await waitFor(() => {
      expect(mockUpdateFunction).toHaveBeenCalled();
      expect(mockMutateCurrentFunction).toHaveBeenCalled();
    });
  });

  it('displays function builder', () => {
    renderWithProvider();
    expect(screen.getByTestId('function-builder')).toBeInTheDocument();
  });

  it('handles go back', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    const goBackButton = screen.getByRole('button', { name: 'go-back' });
    await user.click(goBackButton);
    expect(mockNavigate).toHaveBeenCalledWith('/studio/workspace-id/functions');
  });

  it('handles publish function', async () => {
    const { functionId } = useParams();
    const mockCreateFunction = vi.fn().mockResolvedValue({ id: 'new-function-id' });

    const { useFunctionContext } = await import('@/contexts/Function/FunctionContext');
    (useFunctionContext as any).mockReturnValue({
      updateFunction: vi.fn(),
      createFunction: mockCreateFunction,
      executeFunction: vi.fn(),
    });

    renderWithProvider();
    const user = userEvent.setup();
    const publishButton = screen.getByTestId('builder-layout-publish-button');
    await user.click(publishButton);

    if (!functionId) {
      expect(mockCreateFunction).toHaveBeenCalled();
      expect(mockNavigate).toHaveBeenCalledWith('/studio/workspace-id/function/new-function-id');
    }
  });

  it('handles publish function with invalid name', async () => {
    const mockCreateFunction = vi.fn().mockResolvedValue({ id: 'new-function-id' });
    const { useFunctionBuilderContext } = await import(
      '@/contexts/Function/FunctionBuilderContext'
    );
    const { useFunctionContext } = await import('@/contexts/Function/FunctionContext');

    (useFunctionContext as any).mockReturnValue({
      updateFunction: vi.fn(),
      createFunction: mockCreateFunction,
      executeFunction: vi.fn(),
    });

    (useFunctionBuilderContext as any).mockReturnValue({
      isExpandOutputExample: false,
      setIsExpandOutputExample: vi.fn(),
      setShowInputParametersPanel: vi.fn(),
      showExecuteResults: false,
      showInputParametersPanel: false,
      setShowExecuteResults: vi.fn(),
      currentFunction: { id: 'function-id', name: '' },
      mutateCurrentFunction: vi.fn(),
      executeFunction: vi.fn().mockResolvedValue({}),
      form: {
        getValues: vi.fn().mockImplementation(path => {
          if (path === 'name') return '';
          return { name: '', settings: { input: [], output: {} } };
        }),
        setError: vi.fn(),
        clearErrors: vi.fn(),
      },
    });

    renderWithProvider();
    const user = userEvent.setup();
    const publishButton = screen.getByTestId('builder-layout-publish-button');
    await user.click(publishButton);
    expect(mockCreateFunction).not.toHaveBeenCalled();
  });
});
