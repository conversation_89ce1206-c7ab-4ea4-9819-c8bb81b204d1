import { screen, fireEvent } from '@testing-library/react';
import Functions from './index';
import { renderWithMantine } from '@/utils/test';
import { vi } from 'vitest';
import { FunctionContextProvider } from '@/contexts/Function/FunctionContext';
import { AppContextProvider } from '@/contexts/AppContext';
import { BrowserRouter } from 'react-router-dom';

// Mock the FunctionContext
const mockFunctionContext = {
  functions: { data: [] },
  searchValue: '',
  limit: 10,
  setLimit: vi.fn(),
  cursor: '',
  setCursor: vi.fn(),
  updateSearchText: vi.fn(),
  isLoadingFunctions: false,
};

vi.mock('@/contexts/Function/FunctionContext', () => ({
  FunctionContextProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useFunctionContext: () => mockFunctionContext,
}));

// Mock the components used in Functions
vi.mock('@/components', () => ({
  PageHeaderWithActions: ({
    title,
    description,
    searchPlaceholder,
    searchValue,
    onSearchChange,
    layoutType,
    onLayoutChange,
    buttonActionLabel,
    isUsingButtonMenuActions,
    handleCreateFromScratch,
  }) => (
    <div data-testid='page-header-with-actions'>
      <div data-testid='title'>{title}</div>
      <div data-testid='description'>{description}</div>
      <input
        data-testid='search-input'
        placeholder={searchPlaceholder}
        value={searchValue}
        onChange={e => onSearchChange(e.target.value)}
      />
      <div data-testid='layout-type'>{layoutType}</div>
      <button
        data-testid='layout-change'
        onClick={() => onLayoutChange(layoutType === 'grid' ? 'list' : 'grid')}
      >
        Change Layout
      </button>
      <button data-testid='button-action'>{buttonActionLabel}</button>
      <button data-testid='create-function-button' onClick={handleCreateFromScratch}>
        Create Function
      </button>
      <div data-testid='using-menu-actions'>{isUsingButtonMenuActions.toString()}</div>
    </div>
  ),
}));

// Mock the FunctionList from the Functions folder
vi.mock('@/components/Functions', () => ({
  FunctionList: ({
    functions,
    openedEditModal,
    closeEditModal,
    isCreatingFunction,
    editFunction,
    setEditFunction,
  }) => (
    <div data-testid='functions-container'>
      {/* Always render modal state elements for testing */}
      <div data-testid='opened-edit-modal'>{openedEditModal.toString()}</div>
      <div data-testid='is-creating-function'>{isCreatingFunction.toString()}</div>
      <div data-testid='edit-function'>{editFunction ? 'has-function' : 'no-function'}</div>
      <button data-testid='close-modal-button' onClick={closeEditModal}>
        Close Modal
      </button>
      <button
        data-testid='set-edit-function-button'
        onClick={() => setEditFunction({ id: 'test-function', name: 'Test Function' })}
      >
        Set Edit Function
      </button>
      {/* Render AIEmpty when no functions, similar to real component */}
      {functions.length === 0 && <div data-testid='ai-empty'>No functions</div>}
    </div>
  ),
}));

describe('Functions Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset mock context to default state
    mockFunctionContext.functions = { data: [] };
    mockFunctionContext.isLoadingFunctions = false;
  });

  const renderWithProvider = () => {
    return renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <FunctionContextProvider>
            <Functions />
          </FunctionContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );
  };

  it('renders correctly with default values', () => {
    renderWithProvider();

    expect(screen.getByTestId('title')).toHaveTextContent('title');
    expect(screen.getByTestId('description')).toHaveTextContent('description');
    expect(screen.getByTestId('search-input')).toHaveAttribute('placeholder', 'searchPlaceholder');
    expect(screen.getByTestId('layout-type')).toHaveTextContent('grid');
    expect(screen.getByTestId('button-action')).toHaveTextContent('createFunctionLabel');
    expect(screen.getByTestId('using-menu-actions')).toHaveTextContent('true');
  });

  it('changes layout type when layout control is clicked', () => {
    renderWithProvider();

    expect(screen.getByTestId('layout-type')).toHaveTextContent('grid');

    fireEvent.click(screen.getByTestId('layout-change'));

    expect(screen.getByTestId('layout-type')).toHaveTextContent('list');
  });

  it('updates search value when search input changes', () => {
    renderWithProvider();

    const searchInput = screen.getByTestId('search-input');
    expect(searchInput).toHaveValue('');

    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(searchInput).toHaveValue('test search');
  });

  it('uses translations from the function namespace', () => {
    renderWithProvider();

    expect(screen.getByTestId('title')).toHaveTextContent('title');
    expect(screen.getByTestId('description')).toHaveTextContent('description');
    expect(screen.getByTestId('search-input')).toHaveAttribute('placeholder', 'searchPlaceholder');
  });

  it('displays empty list when no functions exist', async () => {
    renderWithProvider();
    expect(screen.getByTestId('ai-empty')).toBeInTheDocument();
  });

  describe('Modal functionality', () => {
    it('opens modal and sets creating function state when create function button is clicked', () => {
      renderWithProvider();

      // Initially modal should be closed and not creating
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('false');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('false');

      // Click create function button to trigger openModalCreateFunction
      fireEvent.click(screen.getByTestId('create-function-button'));

      // Modal should be opened and isCreatingFunction should be true
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('true');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('true');
    });

    it('closes modal and resets state when close modal is called', () => {
      renderWithProvider();

      // First open the modal and set some state
      fireEvent.click(screen.getByTestId('create-function-button'));
      fireEvent.click(screen.getByTestId('set-edit-function-button'));

      // Verify modal is open and states are set
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('true');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('true');
      expect(screen.getByTestId('edit-function')).toHaveTextContent('has-function');

      // Click close modal button to trigger handleCloseModal
      fireEvent.click(screen.getByTestId('close-modal-button'));

      // Modal should be closed and all states should be reset
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('false');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('false');
      expect(screen.getByTestId('edit-function')).toHaveTextContent('no-function');
    });

    it('maintains correct modal state flow from create to close', () => {
      renderWithProvider();

      // Initial state
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('false');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('false');
      expect(screen.getByTestId('edit-function')).toHaveTextContent('no-function');

      // Open modal for creating function
      fireEvent.click(screen.getByTestId('create-function-button'));
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('true');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('true');

      // Close modal
      fireEvent.click(screen.getByTestId('close-modal-button'));
      expect(screen.getByTestId('opened-edit-modal')).toHaveTextContent('false');
      expect(screen.getByTestId('is-creating-function')).toHaveTextContent('false');
      expect(screen.getByTestId('edit-function')).toHaveTextContent('no-function');
    });
  });

  describe('Loading state', () => {
    it('does not render FunctionList when isLoadingFunctions is true', () => {
      // Set loading state
      mockFunctionContext.isLoadingFunctions = true;

      renderWithProvider();

      // FunctionList should not be rendered when loading
      expect(screen.queryByTestId('functions-container')).not.toBeInTheDocument();
    });

    it('renders FunctionList when isLoadingFunctions is false', () => {
      // Set not loading state
      mockFunctionContext.isLoadingFunctions = false;

      renderWithProvider();

      // FunctionList should be rendered when not loading
      expect(screen.getByTestId('functions-container')).toBeInTheDocument();
    });
  });
});
