import '@xyflow/react/dist/style.css';

import BuilderLayout from '@/components/BuilderLayout';
import HistoryRuns from '@/components/BuilderLayout/RunWrapper/HistoryRuns';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import CatalogModal from '@/components/FlowBuilder/ModalPlacements/CatalogModal';
import RightFormPanel from '@/components/FlowBuilder/ModalPlacements/RightFormPanel';
import { FlowBuilderProvider, useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { useMantineTheme } from '@mantine/core';
import {
  Background,
  BackgroundVariant,
  ConnectionMode,
  Controls,
  MarkerType,
  ReactFlow,
} from '@xyflow/react';
import { useMemo } from 'react';
import nodeTypes from './nodeTypes';
import { useParams } from 'react-router-dom';
import { VersionContextProvider } from '@/contexts/VersionContext';

const defaultEdgeOptions = {
  type: 'floating',
  markerEnd: {
    type: MarkerType.ArrowClosed,
  },
};

function FlowBuilderContent() {
  const {
    flow,
    nodes,
    edges,
    loading,
    handleRun,
    handleBack,
    closeCatalog,
    historyRuns,
    catalogModalRef,
    openedRightPanel,
    handlePublish,
    handleTitleChange,
    // handleOpenTemplate,
    handleOnSelectCatalog,
    handleCloseRightPanel,
  } = useFlowBuilderContext();

  const theme = useMantineTheme();

  const initialFitViewNodes = useMemo(() => {
    return Object.keys(flow?.triggers || {}).map(key => {
      return {
        id: key,
      };
    });
  }, [flow?.triggers]);

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      <BuilderLayout
        onBack={handleBack}
        title={flow?.name || 'Untitled Flow'}
        onTitleChange={handleTitleChange}
        onPublish={handlePublish}
        onRun={handleRun}
        // openTemplate={handleOpenTemplate}
        versionControl={<InnerVersionControl resourceId={flow?.id || ''} resourceType='flow' />}
        historyRuns={<HistoryRuns data={historyRuns} />}
      >
        <ReactFlow
          fitView
          nodes={nodes}
          edges={edges}
          minZoom={0.7}
          maxZoom={1.5}
          nodeTypes={nodeTypes}
          fitViewOptions={{
            padding: 0.2,
            minZoom: 1.0,
            maxZoom: 1.5,
            includeHiddenNodes: false,
            nodes: initialFitViewNodes,
          }}
          deleteKeyCode={null}
          connectionMode={ConnectionMode.Loose}
          defaultEdgeOptions={defaultEdgeOptions}
          defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}>
          <Background
            color={theme.colors.silverFox[5]}
            bgColor={theme.colors.silverFox[2]}
            gap={[30, 30]}
            size={3}
            variant={BackgroundVariant.Dots}
          />
          <Controls showInteractive={false} />
        </ReactFlow>
        <RightFormPanel opened={openedRightPanel} onClose={handleCloseRightPanel} />
      </BuilderLayout>
      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default function FlowBuilder() {
  const { flowId } = useParams();

  return (
    <FlowBuilderProvider>
      <VersionContextProvider resourceType='flow' resourceId={flowId || ''}>
        <FlowBuilderContent />
      </VersionContextProvider>
    </FlowBuilderProvider>
  );
}
