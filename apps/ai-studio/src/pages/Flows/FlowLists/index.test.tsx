import * as timeHelper from '@/helpers/timeHelper'; // Import the module
import { FlowApi } from '@/services/api/flow';
import { LayoutType } from '@/types';
import { mockLibraries, renderWithRouterAppContext } from '@/utils/test';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useTranslate } from '@tolgee/react';
import { useLocation, useParams } from 'react-router-dom';
import { describe, expect, it, vi, beforeEach } from 'vitest';
import { ModelIcons } from '../../../components/FlowList/ModelIcons';
import FlowList from './index';
import { SAMPLE_FLOW_LIST } from '@/mockdata/flow';

// Helper function to wait for all pending state updates to complete
const waitForStateUpdates = async () => {
  // This creates a microtask that will run after all state updates
  // Using a small timeout to ensure all state updates are processed
  await new Promise(resolve => setTimeout(resolve, 50));
};

mockLibraries();

// Mock the required hooks and modules
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useParams: vi.fn(),
    useLocation: vi.fn(),
    useNavigate: vi.fn(),
  };
});

vi.mock('@resola-ai/ui/hooks', () => ({
  usePathParams: () => ({
    createPathWithLngParam: (path: string) => path,
  }),
}));

vi.mock('@/configs', () => ({
  AppConfig: {
    BASE_PATH: '/studio/',
  },
}));

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

vi.mock('@/services/api/flow', () => ({
  FlowApi: {
    update: vi.fn(),
    delete: vi.fn(),
    create: vi.fn(),
    getList: vi.fn(),
  },
}));

describe('FlowList', () => {
  const basePath = '/studio/';
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    // Setup default mocks
    (useParams as any).mockReturnValue({ workspaceId: 'test-workspace' });
    (useLocation as any).mockReturnValue({
      pathname: `${basePath}test-workspace/flows`,
    });
    (FlowApi.getList as any).mockResolvedValue({
      data: SAMPLE_FLOW_LIST,
      hasMore: false,
    });
    // Mock timeAgo to return a static string
    vi.spyOn(timeHelper, 'timeAgo').mockReturnValue('Updated 2 days ago');
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string, params: any) => {
        if (key === 'updateAtLabel') {
          return `Updated ${params.time}`;
        }
        if (key === 'searchPlaceholder') {
          return `searchPlaceholder`;
        }
        if (key === 'title') {
          return `title`;
        }
        if (key === 'description') {
          return `description`;
        }
        if (key === 'buttonCreateFlowLabel') {
          return `buttonCreateFlowLabel`;
        }
        return key;
      },
    });
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  it('renders FlowList component', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      expect(screen.getByText('title')).toBeInTheDocument();
      expect(screen.getByText('description')).toBeInTheDocument();
      expect(screen.getByText('buttonCreateFlowLabel')).toBeInTheDocument();
    });
  });

  it('renders AICard components in grid mode', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      const cards = screen.getAllByTestId('aic-card');
      expect(cards.length).toBe(12);
    });
  });

  it('renders AICard components in list mode', async () => {
    renderWithRouterAppContext(<FlowList />);
    const layoutControl = screen.getByTestId('layout-control');
    const listOption = layoutControl.querySelector(`[value="${LayoutType.LIST}"]`);
    if (!listOption) {
      throw new Error('List option not found');
    }

    // Click the list option
    await user.click(listOption);

    // Wait for state updates
    await waitForStateUpdates();

    await waitFor(() => {
      const cards = screen.getAllByTestId('aic-card-full-width');
      expect(cards.length).toBe(12);
    });
  });

  it('renders search bar', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      const searchBar = screen.getByTestId('search-bar');
      expect(searchBar).toBeInTheDocument();
    });
  });

  it('renders button menu actions', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      const buttonMenuActions = screen.getByTestId('button-menu-actions');
      expect(buttonMenuActions).toBeInTheDocument();
    });
  });

  it('should change layout when click on layout control', async () => {
    renderWithRouterAppContext(<FlowList />);
    const layoutControl = screen.getByTestId('layout-control');
    const listOption = layoutControl.querySelector(`[value="${LayoutType.LIST}"]`);
    const gridOption = layoutControl.querySelector(`[value="${LayoutType.GRID}"]`);

    await waitFor(() => {
      expect(layoutControl).toBeInTheDocument();
      const cards = screen.getAllByTestId('aic-card');
      expect(cards.length).toBe(12);
    });

    if (!listOption || !gridOption) {
      throw new Error('List or Grid option not found');
    }

    // Switch to list view
    await user.click(listOption);
    await waitForStateUpdates();

    // Wait for the layout to change and the new cards to be rendered
    await waitFor(() => {
      expect(screen.getAllByTestId('aic-card-full-width').length).toBe(12);
    });
    // Wait for the old cards to be removed
    await waitFor(() => {
      expect(screen.queryAllByTestId('aic-card').length).toBe(0);
    });

    let cards = screen.queryAllByTestId('aic-card');
    expect(cards.length).toBe(0);

    const cardsFullWidth = screen.getAllByTestId('aic-card-full-width');
    expect(cardsFullWidth.length).toBe(12);
    const timeAgoElements = screen.getAllByText((_, element) => {
      return element?.textContent === 'Updated Updated 2 days ago';
    });
    expect(timeAgoElements.length).toBe(12);

    // Switch back to grid view
    await user.click(gridOption);
    await waitForStateUpdates();

    // Wait for the layout to change back and the original cards to be rendered
    await waitFor(() => {
      expect(screen.getAllByTestId('aic-card').length).toBe(12);
    });
    // Wait for the full width cards to be removed
    await waitFor(() => {
      expect(screen.queryAllByTestId('aic-card-full-width').length).toBe(0);
    });
    cards = screen.getAllByTestId('aic-card');
    expect(cards.length).toBe(12);
    const cardsFullWidthAfter = screen.queryAllByTestId('aic-card-full-width');
    expect(cardsFullWidthAfter.length).toBe(0);
  });

  it('should call handleOnChangeSearch when search bar value changes', async () => {
    renderWithRouterAppContext(<FlowList />);

    const inputElement = screen.getByTestId('search-input');
    await userEvent.type(inputElement, 'test');

    await waitFor(() => {
      expect(FlowApi.getList).toHaveBeenCalled();
    });
  });

  it('should render all menu actions when button is clicked', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      const buttonMenuActions = screen.getByTestId('button-menu-actions');
      expect(buttonMenuActions).toBeInTheDocument();
    });
    await user.click(screen.getByTestId('button-menu-actions'));

    const createFromScratch = await screen.findByTestId('create-from-scratch');
    const importFromFile = await screen.findByTestId('import-from-file');
    const createFromTemplate = await screen.findByTestId('create-from-template');

    expect(createFromScratch).toBeInTheDocument();
    expect(importFromFile).toBeInTheDocument();
    expect(createFromTemplate).toBeInTheDocument();
  });

  it('should handle create from scratch action', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-scratch'));

    await waitFor(() => {
      expect(screen.getByTestId('create-flow-modal')).toBeInTheDocument();
    });
  });

  it('should handle import from file action', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('import-from-file'));

    await waitFor(() => {
      expect(console.log).toHaveBeenCalledWith('handleImportFromFile');
    });
  });

  it('should handle create from template action', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-template'));

    await waitFor(() => {
      expect(screen.getByTestId('flow-template-modal')).toBeInTheDocument();
    });
  });

  it('should render model icons in AICard', async () => {
    renderWithRouterAppContext(<FlowList />);
    await waitFor(() => {
      const modelIcons = screen.getAllByTestId('aic-model-icon-chatGpt');
      expect(modelIcons.length).toBe(12);
    });
  });

  it('should open flow item menu and show all actions', async () => {
    renderWithRouterAppContext(<FlowList />);

    await waitFor(() => {
      const amTargetButtons = screen.getAllByTestId('am-target-button');
      expect(amTargetButtons.length).toBe(12);
    });

    // Click the menu button
    await user.click(screen.getAllByTestId('am-target-button')[0]);
    await waitForStateUpdates();

    const editAction = await screen.findByTestId('am-item-action.edit');
    const exportAction = await screen.findByTestId('am-item-action.export');
    const duplicateAction = await screen.findByTestId('am-item-action.duplicate');
    const deleteAction = await screen.findByTestId('am-item-action.delete');

    expect(editAction).toBeInTheDocument();
    expect(exportAction).toBeInTheDocument();
    expect(duplicateAction).toBeInTheDocument();
    expect(deleteAction).toBeInTheDocument();
  });

  it('should handle edit action click', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getAllByTestId('am-target-button')[0]);
    await user.click(await screen.findByTestId('am-item-action.edit'));

    await waitFor(() => {
      expect(screen.getByTestId('edit-modal')).toBeInTheDocument();
    });
  });

  it('should handle duplicate action click', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getAllByTestId('am-target-button')[0]);
    await user.click(await screen.findByTestId('am-item-action.duplicate'));

    await waitFor(() => {
      expect(FlowApi.create).toHaveBeenCalled();
    });
  });

  it('should handle export action click', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getAllByTestId('am-target-button')[0]);
    await user.click(await screen.findByTestId('am-item-action.export'));

    await waitFor(() => {
      expect(console.log).toHaveBeenCalledWith('onExport');
    });
  });

  it('should render model icon when length less then 3', async () => {
    const flowModels = ['test1', 'test2'];
    renderWithRouterAppContext(<ModelIcons flowModels={flowModels} />);
    await waitFor(() => {
      flowModels.map(model => {
        const modelIcons = screen.getAllByTestId(`aic-few-model-icon-${model}`);
        expect(modelIcons.length).toBe(1);
      });
    });
    vi.unmock('@/mockdata/flow');
  });

  it('should handle create from scratch action and open create flow modal', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-scratch'));

    await waitFor(() => {
      expect(screen.getByTestId('create-flow-modal')).toBeInTheDocument();
    });
  });

  it('should handle create from scratch action, open create flow modal, return data correctly', async () => {
    (FlowApi.create as any).mockResolvedValueOnce({
      id: 'mocked-new-flow-id',
      name: 'Test Title',
      description: 'Test Description',
      updatedAt: '2 days ago',
      status: 'enabled',
    });
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-scratch'));

    await waitFor(() => {
      expect(screen.getByTestId('create-flow-modal')).toBeInTheDocument();
    });
    const createModal = screen.getByTestId('create-flow-modal');
    expect(createModal).toBeInTheDocument();

    // Wait for the modal content to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('title-input')).toBeInTheDocument();
      expect(screen.getByTestId('description-input')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-edit-modal')).toBeInTheDocument();
    });

    const titleInput = screen.getByTestId('title-input');
    const descriptionInput = screen.getByTestId('description-input');
    const submitButton = screen.getByTestId('confirm-edit-modal');

    await user.type(titleInput, 'Test Title');
    await user.type(descriptionInput, 'Test Description');
    await user.click(submitButton);

    await waitFor(() => {
      expect(FlowApi.create).toHaveBeenCalled();
    });
    await waitFor(() => {
      expect(screen.queryByTestId('create-flow-modal-content')).not.toBeInTheDocument();
    });
  });
  it('should handle select template to create action', async () => {
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-template'));

    await waitFor(() => {
      expect(screen.getByTestId('flow-template-modal')).toBeInTheDocument();
    });

    // Wait for template cards to be rendered
    await waitFor(() => {
      const templateCards = screen.getAllByTestId('flow-template-card');
      expect(templateCards.length).toBeGreaterThan(0);
    });

    const firstTemplate = screen.getAllByTestId('flow-template-card')[0];
    await user.click(firstTemplate);

    await waitFor(() => {
      expect(screen.getByTestId('create-flow-modal')).toBeInTheDocument();
    });
  });

  it('should open waiting modal when create from template', async () => {
    (FlowApi.create as any).mockResolvedValueOnce({
      id: 'mocked-new-flow-id',
      name: 'Test Title',
      description: 'Test Description',
      updatedAt: '2 days ago',
      status: 'enabled',
    });
    renderWithRouterAppContext(<FlowList />);

    await user.click(screen.getByTestId('button-menu-actions'));
    await user.click(await screen.findByTestId('create-from-template'));

    await waitFor(() => {
      expect(screen.getByTestId('flow-template-modal')).toBeInTheDocument();
    });

    // Wait for template cards to be rendered
    await waitFor(() => {
      const templateCards = screen.getAllByTestId('flow-template-card');
      expect(templateCards.length).toBeGreaterThan(0);
    });

    const firstTemplate = screen.getAllByTestId('flow-template-card')[0];
    await user.click(firstTemplate);

    await waitFor(() => {
      expect(screen.getByTestId('create-flow-modal')).toBeInTheDocument();
    });

    // Wait for the modal content to be rendered
    await waitFor(() => {
      expect(screen.getByTestId('title-input')).toBeInTheDocument();
      expect(screen.getByTestId('description-input')).toBeInTheDocument();
      expect(screen.getByTestId('confirm-edit-modal')).toBeInTheDocument();
    });

    const titleInput = screen.getByTestId('title-input');
    const descriptionInput = screen.getByTestId('description-input');
    const submitButton = screen.getByTestId('confirm-edit-modal');

    await user.type(titleInput, 'Test Title');
    await user.type(descriptionInput, 'Test Description');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByTestId('waiting-while-creating-modal')).toBeInTheDocument();
    });
  });

  it('should render empty state when no flows are available', async () => {
    // Mock the API to return an empty list
    (FlowApi.getList as any).mockResolvedValue({
      data: [],
      hasMore: false,
    });

    // Mock the useTranslate to return the key as is
    (useTranslate as any).mockReturnValue({
      t: (key: string) => key,
    });

    renderWithRouterAppContext(<FlowList />);

    // Wait for the component to render
    await waitFor(() => {
      expect(screen.getByText('title')).toBeInTheDocument();
    });

    // Verify that the FlowListComponent is not rendered
    expect(screen.queryByTestId('aic-card-full-width')).not.toBeInTheDocument();

    // The empty state text should be rendered - verify by checking for the description text
    expect(screen.getByText('description')).toBeInTheDocument();

    // Note: We're not checking for pagination here because the component might still render it
    // even when there are no flows, which is a valid design decision
  });
});
