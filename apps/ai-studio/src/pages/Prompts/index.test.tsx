import { AppContextProvider } from '@/contexts/AppContext';
import { renderWithMantine } from '@/utils/test';
import '@testing-library/jest-dom';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import Prompts, { TitleTemplateModal } from '.';
import { PromptContextProvider, usePromptContext } from '../../contexts/PromptContext';

// Mock the PromptContext module
vi.mock('../../contexts/PromptContext', () => ({
  PromptContextProvider: ({ children }: { children: React.ReactNode }) => children,
  usePromptContext: vi.fn(),
}));

describe('Prompts', () => {
  // Create mock functions that can be accessed in tests
  const mockUpdatePrompt = vi.fn();
  const mockCreatePrompt = vi.fn();
  const mockDuplicatePrompt = vi.fn();
  const mockDeletePrompt = vi.fn();
  const mockExportPrompt = vi.fn();
  const mockSetLimit = vi.fn();
  const mockSetCursor = vi.fn();
  const mockSetSearchValue = vi.fn();
  const mockSetSelectedCategory = vi.fn();
  const mockSetSelectedTemplate = vi.fn();
  const mockGetSearchPrompts = vi.fn();
  const mockCreateTemplate = vi.fn();
  const mockHandleDeletePrompt = vi.fn();
  const mockHandleSaveAsTemplate = vi.fn();

  beforeEach(() => {
    // Clear all mocks before each test
    vi.clearAllMocks();

    // Set up default mock return value
    vi.mocked(usePromptContext).mockReturnValue({
      prompts: {
        data: [
          {
            id: '1',
            name: 'Sample Prompt',
            description: 'Sample description',
            workspaceId: 'workspace-1',
          },
          {
            id: '2',
            name: 'Another Prompt',
            description: 'Another description',
            workspaceId: 'workspace-1',
          },
        ],
        nextCursor: 'next-cursor',
        prevCursor: 'prev-cursor',
        hasMore: true,
      },
      promptModels: [],
      limit: 10,
      cursor: '',
      searchValue: '',
      setLimit: mockSetLimit,
      setCursor: mockSetCursor,
      setSearchValue: mockSetSearchValue,
      createPrompt: mockCreatePrompt,
      duplicatePrompt: mockDuplicatePrompt,
      updatePrompt: mockUpdatePrompt,
      deletePrompt: mockDeletePrompt,
      exportPrompt: mockExportPrompt,
      setSelectedCategory: mockSetSelectedCategory,
      selectedCategory: null,
      selectedTemplate: null,
      setSelectedTemplate: mockSetSelectedTemplate,
      getSearchPrompts: mockGetSearchPrompts,
      createTemplate: mockCreateTemplate,
      handleDeletePrompt: mockHandleDeletePrompt,
      handleSaveAsTemplate: mockHandleSaveAsTemplate,
    });
  });

  const renderWithProvider = () => {
    return renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <PromptContextProvider>
            <Prompts />
          </PromptContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );
  };

  it('renders without crashing', () => {
    renderWithProvider();
  });

  it('displays prompt list', () => {
    renderWithProvider();
    expect(screen.getByText('title')).toBeInTheDocument();
    expect(screen.getByText('description')).toBeInTheDocument();
  });

  it('show search input', () => {
    renderWithProvider();
    expect(screen.getByPlaceholderText('searchPlaceholder')).toBeInTheDocument();
  });

  it('show create prompt button', () => {
    renderWithProvider();
    expect(screen.getByText('createPromptLabel')).toBeInTheDocument();
  });

  it('show pagination', () => {
    renderWithProvider();
    expect(screen.getByTestId('aip-pagination')).toBeInTheDocument();
    expect(screen.getByText('pagination.previous')).toBeInTheDocument();
    expect(screen.getByText('pagination.next')).toBeInTheDocument();
  });

  it('handles search input change', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    const searchInput = screen.getByPlaceholderText('searchPlaceholder');
    await user.type(searchInput, 'test search');
    expect(searchInput).toHaveValue('test search');
  });

  it('display grid layout', async () => {
    renderWithProvider();
    expect(await screen.findAllByTestId('aic-card')).toHaveLength(2);
  });

  it('change list layout successfully', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const segmentedControl = screen.getByTestId('layout-control');
    const listOption = segmentedControl.querySelector('[value="list"]');

    if (listOption) {
      await user.click(listOption);

      await waitFor(
        () => {
          const listCards = screen.queryAllByTestId('aic-card-full-width');
          expect(listCards.length).toBeGreaterThan(0);
        },
        { timeout: 1000 }
      );
    } else {
      throw new Error('List option not found');
    }
  });

  it('show menu actions', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    const createPromptButton = screen.getByText('createPromptLabel');
    await user.click(createPromptButton);
    await waitFor(() => {
      expect(screen.getByText('pageHeader.buttonMenuActions.fromTemplate')).toBeInTheDocument();
      expect(screen.getByText('pageHeader.buttonMenuActions.fromScratch')).toBeInTheDocument();
      expect(screen.getByText('pageHeader.buttonMenuActions.importFromFile')).toBeInTheDocument();
    });
  });

  it('can create open template modal', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    const createPromptButton = screen.getByText('createPromptLabel');
    await user.click(createPromptButton);

    const createFromTemplateButton = await screen.findByText(
      'pageHeader.buttonMenuActions.fromTemplate'
    );
    await user.click(createFromTemplateButton);

    await waitFor(() => {
      expect(screen.getByTestId('prompt-template')).toBeInTheDocument();
    });
  });

  it('can create prompt from scratch with onCreateFromScratch', async () => {
    const createPromptMock = vi.fn();

    const onCreateFromScratch = () => {
      createPromptMock({
        name: 'Untitled Prompt',
        description: '',
      });
    };

    onCreateFromScratch();

    expect(createPromptMock).toHaveBeenCalledTimes(1);
    expect(createPromptMock).toHaveBeenCalledWith({
      name: 'Untitled Prompt',
      description: '',
    });
  });

  it('close template modal', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const createPromptButton = screen.getByText('createPromptLabel');
    await user.click(createPromptButton);

    await waitFor(() => {
      expect(screen.getByText('pageHeader.buttonMenuActions.fromTemplate')).toBeInTheDocument();
    });

    const createFromTemplateButton = screen.getByText('pageHeader.buttonMenuActions.fromTemplate');
    await user.click(createFromTemplateButton);

    await waitFor(() => {
      expect(screen.getByTestId('prompt-template')).toBeInTheDocument();
    });

    await user.keyboard('{Escape}');

    await waitFor(() => {
      expect(screen.queryByTestId('prompt-template')).not.toBeInTheDocument();
    });
  });

  it('show edit modal when edit button is clicked', async () => {
    renderWithProvider();

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('editAction');
    await user.click(editAction);

    const promptName = await screen.findByText('promptName');
    expect(promptName).toBeInTheDocument();
  });

  it('closes edit modal when cancel button is clicked', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('editAction');
    await user.click(editAction);

    const cancelButton = await screen.findByTestId('cancel-edit-modal');
    await user.click(cancelButton);
    await waitFor(
      () => {
        expect(screen.queryByText('editPrompt')).toBeNull();
      },
      { timeout: 1000 }
    );
  });

  it('handles search functionality with debounce', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const searchInput = screen.getByPlaceholderText('searchPlaceholder');
    await user.type(searchInput, 'test search');

    // Wait for debounced search
    await waitFor(
      () => {
        expect(mockGetSearchPrompts).toHaveBeenCalledWith('test search');
      },
      { timeout: 1000 }
    );
  });

  it('handles pagination next', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const nextButton = screen.getByText('pagination.next');
    await user.click(nextButton);

    expect(mockSetCursor).toHaveBeenCalledWith('next-cursor');
  });

  it('handles pagination previous', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const previousButton = screen.getByText('pagination.previous');
    await user.click(previousButton);

    expect(mockSetCursor).toHaveBeenCalledWith('prev-cursor');
  });

  it('displays empty state when no prompts', async () => {
    // Mock empty prompts data
    vi.mocked(usePromptContext).mockReturnValue({
      prompts: {
        data: [],
        nextCursor: undefined,
        prevCursor: undefined,
        hasMore: false,
      },
      promptModels: [],
      limit: 10,
      cursor: '',
      searchValue: '',
      setLimit: mockSetLimit,
      setCursor: mockSetCursor,
      setSearchValue: mockSetSearchValue,
      createPrompt: mockCreatePrompt,
      duplicatePrompt: mockDuplicatePrompt,
      updatePrompt: mockUpdatePrompt,
      deletePrompt: mockDeletePrompt,
      exportPrompt: mockExportPrompt,
      setSelectedCategory: mockSetSelectedCategory,
      selectedCategory: null,
      selectedTemplate: null,
      setSelectedTemplate: mockSetSelectedTemplate,
      getSearchPrompts: mockGetSearchPrompts,
      createTemplate: mockCreateTemplate,
      handleDeletePrompt: mockHandleDeletePrompt,
      handleSaveAsTemplate: mockHandleSaveAsTemplate,
    });

    renderWithProvider();

    expect(screen.getByTestId('ai-empty')).toBeInTheDocument();
  });

  it('handles template modal navigation', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const createPromptButton = screen.getByText('createPromptLabel');
    await user.click(createPromptButton);

    const createFromTemplateButton = await screen.findByText(
      'pageHeader.buttonMenuActions.fromTemplate'
    );
    await user.click(createFromTemplateButton);

    await waitFor(() => {
      expect(screen.getByTestId('prompt-template')).toBeInTheDocument();
    });

    // Test template modal title
    expect(screen.getByText('allTemplates')).toBeInTheDocument();
  });

  it('handles edit modal with existing prompt data', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const editAction = await screen.findByText('editAction');
    await user.click(editAction);

    // Check that existing data is populated
    const titleInput = await screen.findByTestId('title-input');
    const descriptionInput = await screen.findByTestId('description-input');

    expect(titleInput).toHaveValue('Sample Prompt');
    expect(descriptionInput).toHaveValue('Sample description');
  });

  it('handles multiple prompt cards', async () => {
    renderWithProvider();

    // Should display both prompts
    const cards = await screen.findAllByTestId('aic-card');
    expect(cards).toHaveLength(2);

    // Check that both prompts are displayed
    expect(screen.getByText('Sample Prompt')).toBeInTheDocument();
    expect(screen.getByText('Another Prompt')).toBeInTheDocument();
  });

  it('handles search value sync with context', async () => {
    // Mock context with search value
    vi.mocked(usePromptContext).mockReturnValue({
      prompts: {
        data: [
          {
            id: '1',
            name: 'Sample Prompt',
            description: 'Sample description',
            workspaceId: 'workspace-1',
          },
        ],
        nextCursor: undefined,
        prevCursor: undefined,
        hasMore: false,
      },
      promptModels: [],
      limit: 10,
      cursor: '',
      searchValue: 'existing search',
      setLimit: mockSetLimit,
      setCursor: mockSetCursor,
      setSearchValue: mockSetSearchValue,
      createPrompt: mockCreatePrompt,
      duplicatePrompt: mockDuplicatePrompt,
      updatePrompt: mockUpdatePrompt,
      deletePrompt: mockDeletePrompt,
      exportPrompt: mockExportPrompt,
      setSelectedCategory: mockSetSelectedCategory,
      selectedCategory: null,
      selectedTemplate: null,
      setSelectedTemplate: mockSetSelectedTemplate,
      getSearchPrompts: mockGetSearchPrompts,
      createTemplate: mockCreateTemplate,
      handleDeletePrompt: mockHandleDeletePrompt,
      handleSaveAsTemplate: mockHandleSaveAsTemplate,
    });

    renderWithProvider();

    const searchInput = screen.getByPlaceholderText('searchPlaceholder');
    expect(searchInput).toHaveValue('existing search');
  });

  it('render TitleTemplateModal with all templates title', async () => {
    renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <PromptContextProvider>
            <TitleTemplateModal text='allTemplates' onClick={() => {}} />
          </PromptContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );
    await waitFor(() => {
      expect(screen.getByText('allTemplates')).toBeInTheDocument();
    });
  });

  it('TitleTemplateModal handles click events', async () => {
    const mockOnClick = vi.fn();

    renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <PromptContextProvider>
            <TitleTemplateModal text='allTemplates' onClick={mockOnClick} />
          </PromptContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );

    const user = userEvent.setup();
    const templateModal = screen.getByText('allTemplates');

    await user.click(templateModal);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });
});
