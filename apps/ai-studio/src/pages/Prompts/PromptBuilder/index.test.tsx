import { AppContextProvider } from '@/contexts/AppContext';
import { PromptBuilderContextProvider } from '@/contexts/PromptBuilderContext';
import { renderWithMantine } from '@/utils/test';
import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import PromptBuilder from '.';
import { PromptContextProvider } from '../../../contexts/PromptContext';

// Mock function declarations
const navigate = vi.fn();
const mockUpdatePrompt = vi.fn();
const mockDuplicatePrompt = vi.fn();
const mockDeletePrompt = vi.fn();
const mockCreateTemplate = vi.fn();
const mockHandleSaveAsTemplate = vi.fn();

// Mocks
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual<typeof import('react-router-dom')>('react-router-dom');
  return {
    ...actual,
    useNavigate: () => navigate,
    useParams: () => ({ workspaceId: '1' }),
  };
});

vi.mock('../../../contexts/PromptContext', () => ({
  PromptContextProvider: ({ children }: { children: React.ReactNode }) => children,
  usePromptContext: () => ({
    updatePrompt: mockUpdatePrompt,
    duplicatePrompt: mockDuplicatePrompt,
    handleDeletePrompt: mockDeletePrompt,
    createTemplate: mockCreateTemplate,
    handleSaveAsTemplate: mockHandleSaveAsTemplate,
    promptModels: [],
  }),
}));

vi.mock('../../../contexts/PromptBuilderContext', () => ({
  PromptBuilderContextProvider: ({ children }: { children: React.ReactNode }) => children,
  usePromptBuilderContext: () => ({
    prompt: { id: '1', name: 'Test Prompt' },
    messages: [],
    isLoading: false,
    handleSendMessage: vi.fn(),
    mutatePrompt: vi.fn(),
    setMessages: vi.fn(),
    clearMessages: vi.fn(),
  }),
}));

beforeAll(() => {
  Element.prototype.scrollIntoView = vi.fn();
});

beforeEach(() => {
  vi.clearAllMocks();
});

describe('PromptBuilder', () => {
  const renderWithProvider = () =>
    renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <PromptContextProvider>
            <PromptBuilderContextProvider>
              <PromptBuilder />
            </PromptBuilderContextProvider>
          </PromptContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );

  it('renders prompt detail sections', () => {
    renderWithProvider();
    expect(screen.getByTestId('split-pane')).toBeInTheDocument();
    expect(screen.getByTestId('message-builder')).toBeInTheDocument();
    expect(screen.getByTestId('agent-prompt-settings')).toBeInTheDocument();
  });

  it('navigates back when go-back is clicked', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    const goBackButton = screen.getByRole('button', { name: 'go-back' });
    await user.click(goBackButton);
    expect(navigate).toHaveBeenCalledWith('/studio/1/prompts');
  });

  it('edits the prompt title', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: 'title-name-edit-button' }));
    const input = screen.getByTestId('title-name-input');
    await user.clear(input);
    await user.type(input, 'New Prompt Name');
    await user.keyboard('{Enter}');
    expect(screen.getByText('New Prompt Name')).toBeInTheDocument();
  });

  it('duplicates the prompt', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    await user.click(screen.getByTestId('am-target-button'));
    await user.click(await screen.findByText('action.duplicate'));
    expect(mockDuplicatePrompt).toHaveBeenCalled();
  });

  it('deletes the prompt', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    await user.click(screen.getByTestId('am-target-button'));
    await user.click(await screen.findByText('action.delete'));
    expect(mockDeletePrompt).toHaveBeenCalled();
  });

  it('saves prompt as template', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    await user.click(screen.getByTestId('am-target-button'));
    await user.click(await screen.findByText('action.saveAsTemplate'));
    expect(mockHandleSaveAsTemplate).toHaveBeenCalled();
  });

  it('publishes the prompt', async () => {
    renderWithProvider();
    const user = userEvent.setup();
    await user.click(screen.getByRole('button', { name: 'builderLayout.publish' }));
    expect(mockUpdatePrompt).toHaveBeenCalled();
  });
});
