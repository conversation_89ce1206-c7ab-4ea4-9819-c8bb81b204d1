import { useTranslate } from '@tolgee/react';
import { useNavigate, useParams } from 'react-router-dom';
import { rem, ScrollArea, Stack } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useEffect, useState } from 'react';

import { UnsavedChangesModal } from '@/components/UnsavedChangesModal';
import {
  PromptBuilderContextProvider,
  usePromptBuilderContext,
} from '@/contexts/PromptBuilderContext';
import { PromptContextProvider, usePromptContext } from '@/contexts/PromptContext';
import { AgentContextProvider, useAgentContext } from '@/contexts/AgentContext';
import { useUnsavedChangesPrompt } from '@/hooks/useUnsavedChangesPrompt';
import { IPromptSettings } from '@/models';
import { ModelSettings, SystemMessage } from '@/types/model';
import AgentPromptSettings from '@/components/AgentPromptBuilder/AgentPromptSettings';
import Instructions from '@/components/AgentPromptBuilder/Instructions';
import MessageBuilder from '@/components/AgentPromptBuilder/MessageBuilder';
import SystemMessageSettings from '@/components/AgentPromptBuilder/SystemMessageSettings';
import AISplitPane from '@/components/AISplitPane';
import BuilderLayout from '@/components/BuilderLayout';
import RunWrapper from '@/components/BuilderLayout/RunWrapper';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import { useVersionContext, VersionContextProvider } from '@/contexts/VersionContext';

const useStyles = createStyles(() => ({
  scrollArea: {
    height: `calc(100vh - ${rem(108)})`,
  },
}));

const PromptBuilder = () => {
  const { workspaceId } = useParams();
  const navigate = useNavigate();
  const { promptModels, updatePrompt, handleDeletePrompt, duplicatePrompt, handleSaveAsTemplate } =
    usePromptContext();
  const { prompt, messages, isLoading, handleSendMessage, settings, setSettings, mutatePrompt } =
    usePromptBuilderContext();
  const { onGeneratePrompt } = useAgentContext();
  const { t } = useTranslate('prompt');
  const { classes } = useStyles();
  const [name, setName] = useState(prompt?.name);
  const { mutateVersions } = useVersionContext();

  useEffect(() => {
    setName(prompt?.name);
  }, [prompt]);

  useEffect(() => {
    if (!prompt) return;
    const hasChanged =
      prompt.name !== name || JSON.stringify(prompt.settings) !== JSON.stringify(settings);

    setUnChanged(hasChanged);
  }, [prompt, name, settings]);

  const goBack = () => {
    navigate(`/studio/${workspaceId}/prompts`);
  };

  const [unChanged, setUnChanged] = useState(false);

  const { showPrompt, confirmNavigation, cancelNavigation } = useUnsavedChangesPrompt({
    when: unChanged,
    onConfirm: () => {
      setUnChanged(false);
    },
  });

  const onSettingsChange = (values: IPromptSettings) => {
    setSettings({
      ...settings,
      model: values.model,
      modelOptions: { ...values } as ModelSettings,
    });
  };

  const onInstructionsChange = (instructions: string) => {
    setSettings({ ...settings, responseFormat: instructions });
  };

  const onMessagesChange = (messages: SystemMessage[]) => {
    setSettings({ ...settings, messages });
  };

  const handlePublish = async () => {
    prompt && (await updatePrompt({ ...prompt, name: name || '', settings }));
    setUnChanged(false);
    mutatePrompt();
    mutateVersions();
  };

  const handleRestoreVersion = () => {
    mutatePrompt();
    mutateVersions();
  };

  return (
    <BuilderLayout
      onBack={goBack}
      title={prompt?.name || 'Untitled Prompt'}
      onTitleChange={setName}
      onPublish={handlePublish}
      versionControl={
        <InnerVersionControl
          resourceId={prompt?.id || ''}
          resourceType='prompt'
          onRestoreVersion={handleRestoreVersion}
          snapshot={{
            name: prompt?.name,
            settings,
          }}
        />
      }
      historyRuns={<RunWrapper resourceId={prompt?.id || ''} resourceType='prompt' />}
      seeMoreActions={{
        onDuplicate: () => {
          prompt && duplicatePrompt(prompt, true);
        },
        onDelete: () => {
          prompt && handleDeletePrompt(prompt, () => goBack());
        },
        onSaveAsTemplate: () => {
          prompt && handleSaveAsTemplate(prompt);
        },
      }}
    >
      <AISplitPane
        left={
          <ScrollArea className={classes.scrollArea} scrollbarSize={rem(10)} type='never'>
            <Stack gap={rem(10)}>
              <AgentPromptSettings
                models={promptModels}
                settings={settings}
                onSettingsChange={onSettingsChange}
              />
              <Instructions
                instructions={prompt?.settings?.responseFormat || ''}
                onInstructionsChange={onInstructionsChange}
                onGeneratePrompt={onGeneratePrompt}
              />
              <SystemMessageSettings
                messages={settings?.messages || []}
                onMessagesChange={onMessagesChange}
              />
            </Stack>
          </ScrollArea>
        }
        right={
          <MessageBuilder
            messages={messages}
            onSendMessage={handleSendMessage}
            isLoading={isLoading}
            placeholder={t('messagePlaceholder')}
          />
        }
      />
      <UnsavedChangesModal
        opened={showPrompt}
        onCancel={cancelNavigation}
        onConfirm={confirmNavigation}
      />
    </BuilderLayout>
  );
};

const PromptBuilderPage = () => {
  const { promptId } = useParams();

  return (
    <PromptContextProvider>
      <PromptBuilderContextProvider>
        <VersionContextProvider resourceType='prompt' resourceId={promptId || ''}>
          <AgentContextProvider>
            <PromptBuilder />
          </AgentContextProvider>
        </VersionContextProvider>
      </PromptBuilderContextProvider>
    </PromptContextProvider>
  );
};

export default PromptBuilderPage;
