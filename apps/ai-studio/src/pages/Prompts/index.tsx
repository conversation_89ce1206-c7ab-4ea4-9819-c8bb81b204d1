import { PageHeaderWithActions } from '@/components';
import AIEmpty from '@/components/AIEmpty';
import AIPagination from '@/components/AIPagination';
import { EditModal } from '@/components/Modals';
import PromptList from '@/components/Prompt/PromptList';
import PromptTemplate from '@/components/Prompt/PromptTemplate';
import { PromptContextProvider, usePromptContext } from '@/contexts/PromptContext';
import { IPrompt } from '@/models/prompt';
import { EditModalData, LayoutType } from '@/types';
import { Box, Flex, Modal, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useDisclosure } from '@mantine/hooks';
import { IconArrowLeft } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import debounce from 'lodash/debounce';
import { useCallback, useEffect, useState, useMemo } from 'react';

const useStyles = createStyles(() => ({
  pageHeader: {
    marginBottom: rem(24),
  },
  pagination: {
    marginBottom: rem(24),
  },
  modalTemplate: {
    '.mantine-Modal-content': {
      height: '80vh',
      boxShadow: 'none',
    },
    '.mantine-Modal-body': {
      marginBottom: rem(30),
      paddingBottom: rem(30),
    },
  },
}));

export const TitleTemplateModal = ({ text, onClick }: { text: string; onClick: () => void }) => {
  return (
    <Flex
      align='center'
      gap={rem(8)}
      c={'decaNavy.4'}
      onClick={onClick}
      style={{ cursor: 'pointer' }}
    >
      <IconArrowLeft size={24} />
      {text}
    </Flex>
  );
};

const Prompts = () => {
  const { t } = useTranslate('prompt');
  const { classes } = useStyles();
  const {
    prompts,
    searchValue,
    selectedCategory,
    setSelectedCategory,
    selectedTemplate,
    setSelectedTemplate,
    createPrompt,
    getSearchPrompts,
    limit,
    setLimit,
    cursor,
    setCursor,
    updatePrompt,
  } = usePromptContext();
  const [layoutType, setLayoutType] = useState<LayoutType>(LayoutType.GRID);

  const [openedTemplateModal, { open: openTemplateModal, close: closeTemplateModal }] =
    useDisclosure(false);
  const [openedEditModal, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [editPrompt, setEditPrompt] = useState<IPrompt | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const [search, setSearch] = useState('');

  useEffect(() => {
    setSearch(searchValue);
  }, [searchValue]);

  const handleLayoutChange = (value: LayoutType) => {
    setLayoutType(value);
  };

  const debounceSearch = useMemo(
    () =>
      debounce((value: string) => {
        getSearchPrompts(value);
      }, 500),
    [getSearchPrompts]
  );

  useEffect(() => {
    return () => {
      debounceSearch.cancel();
    };
  }, [debounceSearch]);

  const handleSearchChange = useCallback(
    (val: string) => {
      setSearch(val);
      debounceSearch(val);
    },
    [debounceSearch]
  );

  const renderTitleTemplateModal = useCallback(() => {
    return (
      <Box fz='lg' fw={600}>
        {selectedTemplate ? (
          <TitleTemplateModal
            text={selectedCategory?.name ?? ''}
            onClick={() => setSelectedTemplate(null)}
          />
        ) : selectedCategory ? (
          <TitleTemplateModal
            text={t('promptTemplateCategories')}
            onClick={() => setSelectedCategory(null)}
          />
        ) : (
          t('allTemplates', { count: 10 })
        )}
      </Box>
    );
  }, [selectedTemplate, selectedCategory]);

  const onCloseTemplateModal = () => {
    closeTemplateModal();
    setSelectedCategory(null);
    setSelectedTemplate(null);
  };

  const handleEditPrompt = (prompt: IPrompt) => {
    setIsCreating(false);
    setEditPrompt(prompt);
    openEditModal();
  };

  const handleConfirm = async (data: EditModalData) => {
    const { title: name, description } = data;
    if (isCreating) {
      await createPrompt({ name, description });
    } else {
      editPrompt && (await updatePrompt({ ...editPrompt, name, description }));
    }
    closeEditModal();
    setEditPrompt(null);
    setIsCreating(false);
  };

  return (
    <Box>
      <PageHeaderWithActions
        title={t('title')}
        description={t('description')}
        searchPlaceholder={t('searchPlaceholder')}
        searchValue={search}
        onSearchChange={handleSearchChange}
        layoutType={layoutType}
        onLayoutChange={handleLayoutChange}
        isUsingButtonMenuActions
        buttonActionLabel={t('createPromptLabel')}
        handleCreateFromScratch={() => {
          setIsCreating(true);
          openEditModal();
        }}
        handleCreateFromTemplate={openTemplateModal}
        className={classes.pageHeader}
        hasData={!!prompts?.data?.length}
      />
      {prompts?.data?.length ? (
        <>
          <Box mb={rem(30)}>
            <PromptList
              prompts={prompts.data}
              isFullWidth={layoutType === LayoutType.LIST}
              onEditPrompt={handleEditPrompt}
            />
          </Box>
          <AIPagination
            key={cursor}
            limit={limit}
            onChangeLimit={setLimit}
            onCursorChange={setCursor}
            nextCursor={prompts?.nextCursor}
            prevCursor={prompts?.prevCursor}
            onNext={() => setCursor(prompts?.nextCursor ?? '')}
            onPrevious={() => setCursor(prompts?.prevCursor ?? '')}
          />
        </>
      ) : (
        <AIEmpty />
      )}
      <Modal
        centered
        opened={openedTemplateModal}
        onClose={onCloseTemplateModal}
        size='75vw'
        className={classes.modalTemplate}
        title={renderTitleTemplateModal()}
      >
        <PromptTemplate />
      </Modal>

      <EditModal
        opened={openedEditModal}
        onCancel={closeEditModal}
        onConfirm={handleConfirm}
        title={isCreating ? t('createPromptLabel') : t('editPrompt')}
        options={{
          titleLabel: t('promptName'),
        }}
        initialValues={
          isCreating
            ? {
                title: '',
                description: '',
              }
            : editPrompt
              ? {
                  title: editPrompt.name,
                  description: editPrompt.description,
                }
              : undefined
        }
      />
    </Box>
  );
};

const PromptPage = () => {
  return (
    <PromptContextProvider>
      <Prompts />
    </PromptContextProvider>
  );
};

export default PromptPage;
