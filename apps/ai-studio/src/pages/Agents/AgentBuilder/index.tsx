import { useCallback, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslate } from '@tolgee/react';

import { AgentContextProvider, useAgentContext } from '@/contexts/AgentContext';
import { useAppContext } from '@/contexts/AppContext';
import { IAgent, IAgentSettings } from '@/models/agent';
import { ModelSettings } from '@/types/model';
import AISplitPane from '@/components/AISplitPane';
import AgentPromptSettings from '@/components/AgentPromptBuilder/AgentPromptSettings';
import BuilderLayout from '@/components/BuilderLayout';
import Instructions from '@/components/AgentPromptBuilder/Instructions';
import MessageBuilder from '@/components/AgentPromptBuilder/MessageBuilder';
import { InnerVersionControl } from '@/components/BuilderLayout/VersionControlWrapper';
import { VersionContextProvider } from '@/contexts/VersionContext';

const AgentBuilderContainer = () => {
  const navigate = useNavigate();
  const { t } = useTranslate('agent');
  const { workspaceId } = useParams();
  const {
    agentDetails,
    agentModels,
    onDuplicateAgent,
    onDeleteAgent,
    onGeneratePrompt,
    agents,
    setApiParams,
    onUpdateAgent,
    settings,
    setSettings,
    messages,
    onSendMessageStream,
    messagesLoading,
  } = useAgentContext();
  const { openConfirmModal, closeConfirmModal } = useAppContext();

  const onBack = () => {
    navigate(`/studio/${workspaceId}/agents`);
  };

  const handleTitleChange = useCallback(
    async (name: string) => {
      if (agentDetails?.id) {
        await onUpdateAgent({ ...agentDetails, name });
      }
    },
    [onUpdateAgent, agentDetails]
  );

  const onHandleSendMessage = useCallback(
    (content: string) => {
      onSendMessageStream({
        message: content,
      });
    },
    [onSendMessageStream]
  );

  const onSettingsChange = useCallback(
    (settings: ModelSettings) => {
      setSettings(settings as IAgentSettings);
    },
    [setSettings]
  );

  const onHandleDeleteAgent = useCallback(
    (agent: IAgent) => {
      openConfirmModal({
        title: t('actions.delete'),
        name: agent.name,
        onConfirm: async () => {
          await onDeleteAgent(agent.id);
          closeConfirmModal();
          onBack();
        },
        onCancel: closeConfirmModal,
      });
    },
    [onDeleteAgent, onBack]
  );

  useEffect(() => {
    setApiParams(prev => ({
      ...prev,
      workspaceId,
    }));
  }, [workspaceId]);

  if (!agentDetails) {
    return null;
  }

  return (
    <BuilderLayout
      onBack={onBack}
      title={agentDetails.name}
      onTitleChange={handleTitleChange}
      onPublish={() => {
        agentDetails && onUpdateAgent({ ...agentDetails, settings: settings as IAgentSettings });
      }}
      versionControl={
        <InnerVersionControl
          resourceId={agentDetails.id || ''}
          resourceType='agent'
          snapshot={{
            name: agentDetails.name,
            settings,
          }}
        />
      }
      seeMoreActions={{
        onDuplicate: () => {
          agentDetails && onDuplicateAgent(agentDetails);
        },
        onDelete: () => {
          agentDetails && onHandleDeleteAgent(agentDetails);
        },
      }}
    >
      <AISplitPane
        left={
          <>
            <AgentPromptSettings
              isAgent
              settings={settings}
              models={agentModels}
              onSettingsChange={onSettingsChange}
              agents={agents?.data.filter(agent => agent.id !== agentDetails.id) ?? []}
            />
            <Instructions
              instructions={agentDetails.settings?.systemPrompt}
              onInstructionsChange={instruction =>
                setSettings(prev => ({ ...prev, systemPrompt: instruction }) as IAgentSettings)
              }
              onGeneratePrompt={onGeneratePrompt}
            />
          </>
        }
        right={
          <MessageBuilder
            messages={messages}
            onSendMessage={onHandleSendMessage}
            isLoading={messagesLoading}
            placeholder={t('builder.message.placeholder')}
          />
        }
      />
    </BuilderLayout>
  );
};

const AgentBuilder = () => {
  const { agentId } = useParams();

  return (
    <AgentContextProvider>
      <VersionContextProvider resourceType='agent' resourceId={agentId || ''}>
        <AgentBuilderContainer />
      </VersionContextProvider>
    </AgentContextProvider>
  );
};

export default AgentBuilder;
