// Only json_object is supported for now.
export const OUTPUT_FORMAT_OPTIONS = [{ label: 'json_object', value: 'json_object' }];

export const SAMPLE_CODE = `function process(input) {
  return input.greeting;
}
`;

export const INITIAL_SETTINGS = {
  input: [
    {
      id: 'b6b4c572-4e55-40d8-8cc3-29e4aa8gs',
      name: 'greeting',
      type: 'string',
      required: 'yes',
      description: 'Input a greeting',
      defaultValue: 'HelloWorld',
      order: 0,
    },
  ],
  output: {
    type: 'json_object',
    properties: {
      processed: {
        type: 'string',
      },
    },
  },
};
