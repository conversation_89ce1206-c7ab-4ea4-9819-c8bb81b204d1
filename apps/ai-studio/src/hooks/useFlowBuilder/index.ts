import { mockHistoryRuns } from '@/mockdata/historyRun';
import { mockVersions } from '@/mockdata/version';
import { IRun, IVersion } from '@/models';
import {
  ConnectionData,
  Flow,
  FlowNodeData,
  FlowNodeType,
  FlowTypeNode,
  PathValue,
} from '@/models/flow';
import { FlowApi } from '@/services/api/flow';
import { useDisclosure } from '@mantine/hooks';
import { nanoid } from 'nanoid';
import { Dispatch, SetStateAction, useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import useFlowActions from './useFlowActions';
import useFlowNodeAndEdgeGenerator from './useFlowNodeAndEdgeGenerator';
import {
  CatalogModalRef,
  DisabledNodesType,
} from '@/components/FlowBuilder/ModalPlacements/CatalogModal';
import set from 'lodash/set';
import get from 'lodash/get';

const DEFAULT_DISABLED_NODES_CATALOG: DisabledNodesType[] = ['new-trigger', 'looping', 'schedule'];
const DEFAULT_TRIGGER_NODE_TYPES = ['new-trigger', 'schedule-trigger', 'event-trigger', 'webhook-trigger', 'schedule'];

export const useFlowBuilder = () => {
  const navigate = useNavigate();
  const { workspaceId, flowId } = useParams();
  const [flow, setFlow] = useState<Flow | undefined>(undefined);
  const [loading, setLoading] = useState(true);
  const [openedCatalog, { open: openCatalog, close: closeCatalog }] = useDisclosure(false);
  const [historyRuns, setHistoryRuns] = useState<IRun[]>(mockHistoryRuns);
  const [openedRightPanel, { open: openRightPanel, close: closeRightPanel }] = useDisclosure(false);
  const [intendChangeNodeId, setIntendChangeNodeId] = useState<string | undefined>(undefined);
  const [currentSelectNodeId, setCurrentSelectNodeId] = useState<string | undefined>(undefined);
  const versionUtils = useVersioning();
  const { nodes, edges, setNodes } = useFlowNodeAndEdgeGenerator(flow?.triggers, flow?.nodes);
  const flowActionHandlers = useFLowActionHandler(setFlow, flow, closeRightPanel);
  const catalogModalRef = useRef<CatalogModalRef>(null);
  const isDebugging = false;

  const handleUpdateVirtualNodeByPath = useCallback(
    (virtualNodeId: string, path: string, value: any) => {
      setFlow(prev => {
        if (!prev) return prev;
        const virtualNode = nodes.find(node => node.id === virtualNodeId);
        if (!virtualNode) return prev;
        const parentNodeId = get(virtualNode, 'data.actualParentNodeId', '') as string;
        const parentNode = get(prev?.nodes, parentNodeId);
        if (!parentNode) return prev;

        const paths = get(parentNode, 'settings.paths', []) as any[];
        const pathIndex = paths.findIndex(path => path.next === virtualNode.data.nextNodeId);

        const updatedNode = { ...parentNode };
        const updatedPath = `settings.paths.${pathIndex}.${path}`;
        set(updatedNode, updatedPath, value);

        return {
          ...prev,
          nodes: { ...prev.nodes, [parentNodeId]: updatedNode },
        };
      });
    },
    [nodes]
  );

  const handleAddNewNodeWhenClickAddStep = useCallback(
    (connectionData: ConnectionData) => {
      closeRightPanel();
      setCurrentSelectNodeId(undefined);
      const newNodeId = flowActionHandlers.handleAddNewNode(connectionData);
      let disabledOptions: DisabledNodesType[] = DEFAULT_DISABLED_NODES_CATALOG;

      if (connectionData.directAddNodeFromTrigger) {
        disabledOptions = disabledOptions.filter(item => !['new-trigger', 'schedule'].includes(item));
      }
      const { isConnectFromLoopSubNodes } =
        flowActionHandlers.flowActions.checkAncestorIsLoopingNode(flow!, connectionData.parentId);

      if (!connectionData.directSubNodeLooping && !isConnectFromLoopSubNodes) {
        disabledOptions = disabledOptions.filter(item => item !== 'looping');
      }

      if (newNodeId) {
        openCatalog();
        setIntendChangeNodeId(newNodeId);
        catalogModalRef.current?.openWithDisabledOptions(disabledOptions);
      }
    },
    [openCatalog, flowActionHandlers.handleAddNewNode, closeRightPanel, flow]
  );

  const handleOpenCatalogForReplaceEmptyNode = useCallback(
    (nodeId: string) => {
      closeRightPanel();
      setCurrentSelectNodeId(undefined);
      setIntendChangeNodeId(nodeId);
      openCatalog();
      const { isConnectFromLoopSubNodes } =
        flowActionHandlers.flowActions.checkAncestorIsLoopingNode(flow!, nodeId);

      let disabledOptions: DisabledNodesType[] = DEFAULT_DISABLED_NODES_CATALOG;

      const directNodeFromTriggerNode = !!flow?.triggers?.[flow?.nodes?.[nodeId]?.prev ?? ''];
      if (directNodeFromTriggerNode) {
        disabledOptions = disabledOptions.filter(item => !['new-trigger', 'schedule'].includes(item));
      }

      if (!isConnectFromLoopSubNodes) {
        disabledOptions = disabledOptions.filter(item => item !== 'looping');
      }
      catalogModalRef.current?.openWithDisabledOptions(disabledOptions);
    },
    [openCatalog, closeRightPanel, flow]
  );

  const handleBack = useCallback(() => {
    if (workspaceId) {
      navigate(`/studio/${workspaceId}/flows`);
    } else {
      navigate('/studio');
    }
  }, [navigate, workspaceId]);

  const handleTitleChange = useCallback(
    (newTitle: string) => {
      if (flow && workspaceId) {
        const updatedFlow = { ...flow, name: newTitle };
        setFlow(updatedFlow);
        FlowApi.update(workspaceId, updatedFlow);
      }
    },
    [flow, workspaceId]
  );

  const handleUpdateDisplayName = useCallback(
    (nodeId: string, displayName: string) => {
      if (!workspaceId) return;

      setFlow(prev => {
        if (!prev) return prev;
        if (!prev.nodes?.[nodeId] && !prev.triggers?.[nodeId]) return prev;

        const updatedFlow = { ...prev };
        if (prev.nodes?.[nodeId]) {
          updatedFlow.nodes = {
            ...prev.nodes,
            [nodeId]: { ...prev.nodes[nodeId], displayName },
          };
        }
        if (prev.triggers?.[nodeId]) {
          updatedFlow.triggers = {
            ...prev.triggers,
            [nodeId]: { ...prev.triggers[nodeId], displayName },
          };
        }

        FlowApi.update(workspaceId, updatedFlow as Flow);

        return updatedFlow;
      });
    },
    [workspaceId]
  );

  const handlePublish = useCallback(async () => {
    // Implement publish functionality
    console.log('Publishing flow');
    if (flow) {
      await FlowApi.update(workspaceId ?? '', flow);
    }
  }, [flow, workspaceId]);

  const handleOpenVersions = useCallback(() => {
    // Implement versions functionality
    console.log('Opening versions');
  }, []);

  const handleOpenSeeMore = useCallback(() => {
    // Implement see more functionality
    console.log('Opening see more');
  }, []);

  const handleRun = useCallback(() => {
    // Implement run functionality
    console.log('Running flow');
  }, []);

  const handleOpenHistoryRuns = useCallback(() => {
    // Implement history runs functionality
    console.log('Opening history runs');
  }, []);

  const handleOpenVariables = useCallback(() => {
    // Implement variables functionality
    console.log('Opening variables');
  }, []);

  const handleOpenTemplate = useCallback(() => {
    // Implement template functionality
    console.log('Opening template');
  }, []);

  const handleCloseRightPanel = useCallback(() => {
    setCurrentSelectNodeId(undefined);
    closeRightPanel();
  }, [closeRightPanel]);

  const handleOpenCatalog = useCallback(() => {
    handleCloseRightPanel();
    openCatalog();
    catalogModalRef.current?.openWithDisabledOptions();
  }, [openCatalog, handleCloseRightPanel]);

  const handleOpenFormPanel = useCallback(
    (nodeId: string) => {
      setCurrentSelectNodeId(nodeId);
      closeCatalog();
      openRightPanel();
    },
    [openRightPanel, closeCatalog]
  );

  const handleOnSelectCatalog = useCallback(
    (item: { id: string; displayName: string; icon: string; name: FlowNodeType }) => {
      closeCatalog();
      if (!intendChangeNodeId) {
        return;
      }
      const intendChangeNode = flow?.nodes?.[intendChangeNodeId ?? ''];

      if (!intendChangeNode) return;
      const { node: parentNode, isLoopingNode } = flowActionHandlers.flowActions.getNodeInfo(
        flow!,
        intendChangeNode?.prev ?? ''
      );
      const connectionData: ConnectionData = {
        parentId: intendChangeNode?.prev ?? '',
        nextNodeId: intendChangeNode.next,
        directSubNodeLooping:
          isLoopingNode && intendChangeNode.id === parentNode?.settings?.nextChildID,
        directNextNodeLooping: isLoopingNode && intendChangeNode.id === parentNode?.next,
      };
      let newFlow;
      const NodeTypeWithNeedActions = ['path', 'trigger', 'loop', 'schedule'];

      if (!NodeTypeWithNeedActions.some(type => item?.id.includes(type))) {
        // just replace the type of intendChangeNode to type of item
        newFlow = flowActionHandlers.flowActions.replaceNodeType(flow, intendChangeNodeId, {
          name: item?.name,
          icon: item?.icon,
          displayName: item?.displayName,
        });
        newFlow && setFlow(newFlow);
        setIntendChangeNodeId(undefined);
        return;
      }

      newFlow = flowActionHandlers.flowActions.removeNode(
        flow,
        connectionData.parentId,
        intendChangeNodeId!
      );

      if (item?.id.includes('trigger') || item?.id.includes('schedule')) {
        newFlow = flowActionHandlers.flowActions.addNewTrigger(newFlow!, {
          name: item?.name,
          icon: item?.icon,
          displayName: item?.displayName,
        });
      }

      if (item?.id.includes('path')) {
        newFlow = flowActionHandlers.flowActions.addNewPath(newFlow!, connectionData);
      }

      if (item?.id.includes('loop')) {
        newFlow = flowActionHandlers.flowActions.addNewLooping(newFlow!, connectionData);
      }

      setIntendChangeNodeId(undefined);
      newFlow && setFlow(newFlow);
    },
    [closeCatalog, flow, flowActionHandlers.flowActions, intendChangeNodeId]
  );

  useEffect(() => {
    if (flow && window) {
      (window as any).globalFlow = flow;
    }
  }, [flow]);

  // Fetch flow details
  useEffect(() => {
    const fetchFlowDetails = async () => {
      if (workspaceId && flowId) {
        try {
          setLoading(true);
          const flowData = await FlowApi.getById(workspaceId, flowId);
          // We could handle empty flowData later
          // back to list or show a notification.
          setFlow(flowData);
        } catch (error) {
          console.error('Error fetching flow details:', error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchFlowDetails();
  }, [workspaceId, flowId]);

  return {
    flow,
    nodes,
    edges,
    loading,
    isDebugging,
    historyRuns,
    openedCatalog,
    ...versionUtils,
    catalogModalRef,
    openedRightPanel,
    flowActionHandlers,
    intendChangeNodeId,
    currentSelectNodeId,
    setNodes,
    handleRun,
    handleBack,
    closeCatalog,
    handlePublish,
    setHistoryRuns,
    handleOpenCatalog,
    handleTitleChange,
    handleOpenSeeMore,
    handleOpenTemplate,
    handleOpenVersions,
    handleOpenVariables,
    handleOpenFormPanel,
    handleCloseRightPanel,
    handleOpenHistoryRuns,
    handleOnSelectCatalog,
    handleUpdateDisplayName,
    handleUpdateVirtualNodeByPath,
    handleAddNewNodeWhenClickAddStep,
    handleOpenCatalogForReplaceEmptyNode,
  };
};

export const useVersioning = () => {
  const [versions, setVersions] = useState<IVersion[]>(mockVersions);

  const handleAddVersion = (version: Omit<IVersion, 'id'>) => {
    setVersions([{ ...version, id: nanoid() }, ...versions]);
  };

  const handleEditVersion = (version: IVersion) => {
    setVersions(versions.map(v => (v.id === version.id ? version : v)));
  };

  const handleDeleteVersion = (version: IVersion) => {
    setVersions(versions.filter(v => v.id !== version.id));
  };

  return {
    versions,
    handleAddVersion,
    handleEditVersion,
    handleDeleteVersion,
  };
};

export const useFLowActionHandler = (
  setFlow: Dispatch<SetStateAction<Flow | undefined>>,
  flow?: Flow,
  closeRightPanel?: () => void,
  setCurrentSelectNodeId?: Dispatch<SetStateAction<string | undefined>>,
) => {
  const flowActions = useFlowActions();

  const handleAddNewNode = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return undefined;
      const actionRes = flowActions.addNewNode(flow, connectionData);
      if (actionRes) {
        setFlow(actionRes.newFlow);
        return actionRes.newNodeId; // return new node id for later use
      }
      return undefined; // this for clarification.
    },
    [flow, flowActions, setFlow]
  );

  const handleAddNewPath = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return;
      const newFlow = flowActions.addNewPath(flow, connectionData);
      if (newFlow) setFlow(newFlow);
    },
    [flow, flowActions, setFlow]
  );

  const handleAddNewSubPath = useCallback(
    (connectionData: ConnectionData) => {
      if (!flow) return;
      const newFlow = flowActions.addNewSubPath(flow, connectionData);
      if (newFlow) setFlow(newFlow);
    },
    [flow, flowActions, setFlow]
  );

  const handleRemoveNode = useCallback(
    ({
      typeRemove,
      nodeId,
      parentId = '',
    }: {
      typeRemove: FlowTypeNode;
      nodeId: string;
      parentId?: string;
    }) => {
      if (!flow) return;
      let newFlow;
      switch (typeRemove) {
        case FlowTypeNode.TriggerNode:
          newFlow = flowActions.removeTriggerNode(flow, nodeId);
          break;
        case FlowTypeNode.Node:
          newFlow = flowActions.removeNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.Path:
          newFlow = flowActions.removePathNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.SubPath:
          newFlow = flowActions.removeSubPathNode(flow, parentId, nodeId);
          break;
        case FlowTypeNode.Looping:
          newFlow = flowActions.removeLoopingNode(flow, parentId, nodeId);
          break;
        default:
          break;
      }
      if (newFlow) setFlow(newFlow);
      closeRightPanel?.();
    },
    [flow, flowActions, setFlow, closeRightPanel]
  );

  const handleUpdateTrigger = useCallback((triggerId: string, trigger: Partial<FlowNodeData>) => {
    setFlow(prev => {
      if (!prev) return prev;
      return {
        ...prev,
        triggers: {
          ...prev.triggers,
          [triggerId]: { ...prev.triggers[triggerId], ...trigger },
        },
      };
    });
  }, []);

  const handleUpdateTriggerByPaths = useCallback(
    (triggerId: string, paths: { path: string; value: any }[]) => {
      setFlow(prev => {
        if (!prev) return prev;
        if (!prev.triggers?.[triggerId]) return prev;
        const updatedTrigger = { ...prev.triggers[triggerId] };
        paths.forEach(({ path, value }) => {
          set(updatedTrigger, path, value);
        });
        return {
          ...prev,
          triggers: {
            ...prev.triggers,
            [triggerId]: updatedTrigger,
          },
        };
      });
    },
    []
  );

  const handleUpdateNodeByPaths = useCallback((nodeId: string, paths: PathValue[]) => {
    setFlow(prev => {
      if (!prev) return prev;

      if (!prev.nodes?.[nodeId]) return prev;
      const updatedNode = { ...prev.nodes[nodeId] };
      paths.forEach(({ path, value }) => {
        set(updatedNode, path, value);
      });
      return {
        ...prev,
        nodes: { ...prev.nodes, [nodeId]: updatedNode },
      };
    });
  }, []);

  const handleUpdateNode = useCallback(async (nodeId: string, node: Partial<FlowNodeData> & { name: FlowNodeType }) => {
    setFlow(prev => {
      if (!prev) return prev;
      if (!prev.nodes?.[nodeId] && !prev.triggers?.[nodeId]) return prev;
      // if node is supposed to update and it is the same type, then no need to update it.
      if (prev.nodes?.[nodeId]?.name === node.name || prev.triggers?.[nodeId]?.name === node.name) return prev;

      // if node is one of trigger nodes, then only allow trigger node types to be updated.
      if (prev.triggers?.[nodeId]) {
        if (!DEFAULT_TRIGGER_NODE_TYPES.includes(node.name)) return prev;
        return {
          ...prev,
          triggers: {
            ...prev.triggers,
            [nodeId]: { ...prev.triggers[nodeId], ...node, settings: {} as any },
          },
        };
      }
      // else if node is a looping node, then we can update that node , but need to remove recursively all sub nodes.
      if (prev.nodes?.[nodeId]?.name === FlowNodeType.Looping) {
        const newFlow = flowActions.removeAllSubNodesLoopingNode(prev, nodeId);
        if (!newFlow) return prev;
        let updatedNode = { ...newFlow!.nodes?.[nodeId], ...node, settings: {} as any } as FlowNodeData;

        if (node.name === FlowNodeType.Path) {
          const currentNode = newFlow.nodes?.[nodeId];
          if (!currentNode) return prev;
          const parentId = currentNode?.prev ?? '';
          const nextNodeId = currentNode?.next ?? '';
          const newPathNode = flowActions.getNewBranchNode(nextNodeId, parentId);
          newPathNode.id = currentNode.id;
          updatedNode = { ...updatedNode, ...newPathNode }
          setCurrentSelectNodeId?.(undefined);
          closeRightPanel?.();
        }

        return {
          ...newFlow,
          nodes: { ...newFlow.nodes, [nodeId]: updatedNode },
        };
      }

      if (!prev.nodes?.[nodeId] || DEFAULT_TRIGGER_NODE_TYPES.includes(node.name)) {
        return prev;
      }

      // else if node is path node, let create a correct form of path node.
      if (node.name === FlowNodeType.Path) {
        const currentNode = prev.nodes?.[nodeId];
        if (!currentNode) return prev;
        const parentId = currentNode?.prev ?? '';
        const nextNodeId = currentNode?.next ?? '';
        const newPathNode = flowActions.getNewBranchNode(nextNodeId, parentId);
        newPathNode.id = currentNode.id;
        setCurrentSelectNodeId?.(undefined);
        closeRightPanel?.();
        return {
          ...prev,
          nodes: { ...prev.nodes, [nodeId]: newPathNode },
        };
      }

      return {
        ...prev,
        nodes: { ...prev.nodes, [nodeId]: { ...prev.nodes?.[nodeId], ...node, settings: {} as any } },
      };
    });
  }, [flowActions.removeAllSubNodesLoopingNode, flowActions.getNewBranchNode, closeRightPanel, setCurrentSelectNodeId]);

  return {
    flowActions,
    handleAddNewNode,
    handleAddNewPath,
    handleRemoveNode,
    handleAddNewSubPath,
    handleUpdateNode,
    handleUpdateTrigger,
    handleUpdateNodeByPaths,
    handleUpdateTriggerByPaths,
  };
};
