import { VersionAPI } from '@/services/api/version';
import { renderHook, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useListVersion } from '.';

vi.mock('@/services/api/version', () => ({
  VersionAPI: {
    getList: vi.fn(),
  },
}));

describe('useListVersion', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return empty data when workspaceId is not provided', async () => {
    renderHook(() =>
      useListVersion({
        workspaceId: undefined as any,
        cursor: '',
        limit: 20,
        resourceType: 'prompt',
        searchValue: '',
      })
    );

    expect(VersionAPI.getList).not.toHaveBeenCalled();
  });

  it('should fetch templates when workspaceId is provided', async () => {
    const mockResponse = {
      data: [{ id: '1', name: 'Test Template' }],
      nextCursor: 'next',
      prevCursor: 'prev',
      hasMore: true,
    };

    (VersionAPI.getList as any).mockResolvedValueOnce(mockResponse);

    const { result } = renderHook(() =>
      useListVersion({
        workspaceId: 'workspace-1',
        cursor: 'cursor-1',
        limit: 20,
        resourceType: 'prompt',
        searchValue: 'test',
        resourceId: 'resource-1',
      })
    );

    await waitFor(() => {
      expect(result.current.data).toEqual(mockResponse);
    });

    expect(VersionAPI.getList).toHaveBeenCalledWith(
      'workspace-1',
      20,
      'cursor-1',
      'prompt',
      {
        name: 'test',
      },
      'resource-1'
    );
  });

  it('should fetch templates when workspaceId is provided and searchValue is provided', async () => {
    const mockResponse = {
      data: [{ id: '1', name: 'Test Template' }],
      nextCursor: 'next',
      prevCursor: 'prev',
      hasMore: true,
    };

    (VersionAPI.getList as any).mockResolvedValueOnce(mockResponse);

    const { result } = renderHook(() =>
      useListVersion({
        workspaceId: 'workspace-1',
        cursor: 'cursor-1',
        limit: 20,
        resourceType: 'prompt',
        searchValue: 'test',
        resourceId: 'resource-1',
      })
    );

    await waitFor(() => {
      expect(result.current.data).toEqual(mockResponse);
    });
  });
});
