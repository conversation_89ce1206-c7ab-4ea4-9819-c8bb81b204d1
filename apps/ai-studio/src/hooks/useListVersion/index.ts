import useS<PERSON> from 'swr';
import { useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash';
import { VersionAPI } from '@/services/api/version';
import { ListQueryParams } from '@/models';

export const useListVersion = ({
  workspaceId,
  limit,
  cursor,
  searchValue,
  resourceType,
  resourceId,
}: ListQueryParams) => {
  const [debouncedSearchValue, setDebouncedSearchValue] = useState(searchValue);

  const debouncer = useMemo(() => debounce((val: string) => setDebouncedSearchValue(val), 500), []);

  useEffect(() => {
    debouncer(searchValue);
    return () => {
      debouncer.cancel();
    };
  }, [searchValue]);

  return useSWR(
    workspaceId
      ? [`${workspaceId}/versions`, workspaceId, limit, cursor, resourceType, debouncedSearchValue]
      : null,
    () => {
      return VersionAPI.getList(
        workspaceId || '',
        limit,
        cursor,
        resourceType,
        {
          name: debouncedSearchValue,
        },
        resourceId
      );
    },
    {
      revalidateOnFocus: false,
    }
  );
};
