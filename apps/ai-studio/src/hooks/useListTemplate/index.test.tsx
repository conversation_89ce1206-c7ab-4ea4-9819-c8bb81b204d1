import { Template<PERSON><PERSON> } from '@/services/api/template';
import { renderHook, waitFor } from '@testing-library/react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { useListTemplate } from '.';

vi.mock('@/services/api/template', () => ({
  TemplateAPI: {
    getList: vi.fn(),
  },
}));

describe('useListTemplate', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return empty data when workspaceId is not provided', async () => {
    renderHook(() =>
      useListTemplate({
        workspaceId: undefined as any,
        cursor: '',
        limit: 20,
        resourceType: 'prompt',
        searchValue: '',
        resourceId: 'resource-1',
      })
    );

    expect(TemplateAPI.getList).not.toHaveBeenCalled();
  });

  it('should fetch templates when workspaceId is provided', async () => {
    const mockResponse = {
      data: [{ id: '1', name: 'Test Template' }],
      nextCursor: 'next',
      prevCursor: 'prev',
      hasMore: true,
    };

    (TemplateAPI.getList as any).mockResolvedValueOnce(mockResponse);

    const { result } = renderHook(() =>
      useListTemplate({
        workspaceId: 'workspace-1',
        cursor: 'cursor-1',
        limit: 20,
        resourceType: 'prompt',
        searchValue: 'test',
        resourceId: 'resource-1',
      })
    );

    await waitFor(() => {
      expect(result.current.data).toEqual(mockResponse);
    });

    expect(TemplateAPI.getList).toHaveBeenCalledWith(
      'workspace-1',
      20,
      'cursor-1',
      'prompt',
      {
        name: 'test',
      },
      'resource-1'
    );
  });

  it('should fetch templates when workspaceId is provided and searchValue is provided', async () => {
    const mockResponse = {
      data: [{ id: '1', name: 'Test Template' }],
      nextCursor: 'next',
      prevCursor: 'prev',
      hasMore: true,
    };

    (TemplateAPI.getList as any).mockResolvedValueOnce(mockResponse);

    const { result } = renderHook(() =>
      useListTemplate({
        workspaceId: 'workspace-1',
        cursor: 'cursor-1',
        limit: 20,
        resourceType: 'prompt',
        searchValue: 'test',
        resourceId: 'resource-1',
      })
    );

    await waitFor(() => {
      expect(result.current.data).toEqual(mockResponse);
    });
  });
});
