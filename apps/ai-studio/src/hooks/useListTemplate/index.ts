import useS<PERSON> from 'swr';
import { useEffect, useMemo, useState } from 'react';
import { debounce } from 'lodash';

import { TemplateAPI } from '@/services/api/template';
import { ListQueryParams } from '@/models';

export const useListTemplate = ({
  workspaceId,
  limit,
  cursor,
  resourceType,
  searchValue,
  resourceId,
}: ListQueryParams) => {
  const [debouncedSearchValue, setDebouncedSearchValue] = useState(searchValue);

  const debouncer = useMemo(() => debounce((val: string) => setDebouncedSearchValue(val), 500), []);

  useEffect(() => {
    debouncer(searchValue);
    return () => {
      debouncer.cancel();
    };
  }, [searchValue]);

  return useSWR(
    workspaceId
      ? [
          `${workspaceId}/templates`,
          workspaceId,
          limit,
          cursor,
          debouncedSearchValue,
          resourceType,
          resourceId,
        ]
      : null,
    () => {
      return TemplateAPI.getList(
        workspaceId || '',
        limit,
        cursor,
        resourceType,
        {
          name: debouncedSearchValue,
        },
        resourceId
      );
    },
    {
      revalidateOnFocus: false,
    }
  );
};
