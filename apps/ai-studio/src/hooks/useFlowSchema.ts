import { nodes } from '@resola-ai/ui';
import { useMemo } from 'react';

const flowSchemaType = {
  'virtual-node-sub-path': 'path',
};

const TriggerNodes = ['new-trigger', 'schedule-trigger', 'event-trigger', 'webhook-trigger', 'schedule'];

type UseFlowSchemaOptions = {
  flowNodeType?: string;
};

const useFlowSchema = (options: UseFlowSchemaOptions) => {
  const { flowNodeType } = options;

  const schema = useMemo(() => {
    if (!flowNodeType) return {};

    const nodeType = flowSchemaType[flowNodeType] || flowNodeType;

    const selectedNode =
      nodes.find(node => node.name === nodeType) || nodes.find(node => node.name === 'new-trigger');

    return (selectedNode || {}) as Record<string, any>;
  }, [flowNodeType, nodes]);

  return {
    schema,
    isNodeTrigger: TriggerNodes.includes(schema.name),
  };
};

export default useFlowSchema;
