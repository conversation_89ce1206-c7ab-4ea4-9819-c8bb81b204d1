import { IStudioSuccessListResponse, IVersion, IVersionCreatePayload } from '@/models';
import { ResourceType } from '@/types';
import { axiosService } from '@resola-ai/services-shared';
import { executeRequest } from '../utils/helper';

export const VersionAPI = {
  getList: async (
    wsId: string,
    limit: number,
    cursor: string,
    resourceType: ResourceType,

    filters: {
      name: string;
    },
    resourceId?: string
  ) => {
    return executeRequest<IStudioSuccessListResponse<IVersion>>(() =>
      axiosService.instance.get(`${wsId}/versions`, {
        params: {
          limit,
          cursor,
          resourceType,
          resourceId,
          filters: JSON.stringify(filters),
        },
      })
    );
  },

  get: async (wsId: string, versionId: string) => {
    return executeRequest<IVersion>(() =>
      axiosService.instance.get(`${wsId}/versions/${versionId}`)
    );
  },

  create: async (wsId: string, version: IVersionCreatePayload) => {
    return executeRequest<IVersion>(() => axiosService.instance.post(`${wsId}/versions`, version));
  },

  update: async (wsId: string, version: IVersion) => {
    return executeRequest<IVersion>(() =>
      axiosService.instance.put(`${wsId}/versions/${version.id}`, version)
    );
  },

  restore: async (
    wsId: string,
    versionId: string,
    resourceType: ResourceType,
    resourceId: string
  ) => {
    return executeRequest<IVersion>(() =>
      axiosService.instance.post(`${wsId}/versions/${versionId}/restore`, {
        resourceType,
        resourceId,
        versionId,
      })
    );
  },
};
