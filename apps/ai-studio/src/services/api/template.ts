import { IStudioSuccessListResponse, ITemplate, ITemplateCreatePayload } from '@/models';
import { axiosService } from '@resola-ai/services-shared';
import { executeRequest } from '../utils/helper';
import { ResourceType } from '@/types';

export const TemplateAPI = {
  getList: async (
    wsId: string,
    limit: number,
    cursor: string,
    resourceType: ResourceType,
    filters: {
      name: string;
    },
    resourceId?: string
  ) => {
    return executeRequest<IStudioSuccessListResponse<ITemplate>>(() =>
      axiosService.instance.get(`${wsId}/templates`, {
        params: {
          limit,
          cursor,
          resourceType,
          filters: JSON.stringify(filters),
          resourceId,
        },
      })
    );
  },

  get: async (wsId: string, templateId: string) => {
    return executeRequest<ITemplate>(() =>
      axiosService.instance.get(`${wsId}/templates/${templateId}`)
    );
  },

  create: async (wsId: string, template: ITemplateCreatePayload) => {
    return executeRequest<ITemplate>(() =>
      axiosService.instance.post(`${wsId}/templates`, template)
    );
  },

  update: async (wsId: string, template: ITemplate) => {
    return executeRequest<ITemplate>(() =>
      axiosService.instance.put(`${wsId}/templates/${template.id}`, template)
    );
  },

  delete: async (wsId: string, templateId: string) => {
    return executeRequest<ITemplate>(() =>
      axiosService.instance.delete(`${wsId}/templates/${templateId}`)
    );
  },

  deploy: async (wsId: string, templateId: string) => {
    return executeRequest<ITemplate>(() =>
      axiosService.instance.post(`${wsId}/templates/${templateId}/deploy`)
    );
  },
};
