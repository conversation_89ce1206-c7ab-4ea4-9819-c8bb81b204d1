import { CredentialType, ProviderType, ICategory, ICredentialConfig, ICredential } from '@/models';
import { nanoid } from 'nanoid';

export const mockCategories: ICategory[] = [
  {
    id: 'all',
    name: 'All',
  },
  {
    id: 'Calendar',
    name: 'Calendar',
  },
  {
    id: 'Collaboration & Productivity',
    name: 'Collaboration & Productivity',
  },
  {
    id: 'Employer of Record',
    name: 'Employer of Record',
  },
  {
    id: 'Identity & Access',
    name: 'Identity & Access',
  },
  {
    id: 'Office Management',
    name: 'Office Management',
  },
  {
    id: 'Time Management',
    name: 'Time Management',
  },
  {
    id: 'Performance & Engagement',
    name: 'Performance & Engagement',
  },
];

export const mockCredentials: ICredential[] = Array(12)
  .fill(0)
  .map((_, index) => ({
    id: (index + 1).toString(),
    workspaceId: '1',
    name: `Credential ${index + 1}`,
    description: `Sample credential ${index + 1} for testing`,
    type: index % 3 === 2 ? CredentialType.OAUTH2 : CredentialType.API_KEY,
    provider:
      index % 3 === 0
        ? ProviderType.GOOGLE
        : index % 3 === 1
          ? ProviderType.SLACK
          : ProviderType.OPENAI,
    settings: {
      apiKey: `sk-${nanoid(10)}`,
      addTo: 'header',
      keyName: `X-API-Key-${nanoid(10)}`,
      token: `token-${nanoid(10)}`,
      grantType: 'client_credentials',
      clientId: `client-${nanoid(10)}`,
      clientSecret: `secret-${nanoid(10)}`,
      accessToken: `access-token-${nanoid(10)}`,
      refreshToken: `refresh-token-${nanoid(10)}`,
      tokenEndpoint: 'https://api.example.com/token',
      authorizationEndpoint: 'https://api.example.com/authorize',
      redirectUri: 'http://localhost:3000/callback',
      scope: 'read:user',
      username: `user${index + 1}`,
      password: `pass${nanoid(10)}`,
      customField: `custom-${nanoid(10)}`,
      tokenType: 'Bearer',
      tokenExpiresIn: 3600,
      tokenExpiresAt: new Date(Date.now() + 3600 * 1000).toISOString(),
      autoRefresh: false,
      additionalParameters: {
        statusCode: {
          key: 'statusCode',
          type: 'number',
          value: 200,
        },
        headers: {
          key: 'headers',
          type: 'keyvalue',
          value: {
            'Content-Type': {
              key: 'Content-Type',
              value: 'application/json',
            },
            Authorization: {
              key: 'Authorization',
              value: 'Bearer token',
            },
            field: {
              key: 'field',
              value: 'field value',
            },
          },
        },
        body: {
          key: 'body',
          type: 'keyvalue',
          value: {
            message: {
              key: 'message',
              type: 'text',
              value: 'Hello, world!',
            },
          },
        },
      },
    },
    metadata: {
      lastUsed: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toISOString(),
      isDefault: index === 0,
    },
    version: '1.0.0',
    createdAt: new Date(Date.now() - index * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - index * 12 * 60 * 60 * 1000),
  }));

// Common property types
const commonPropertyTypes = {
  username: {
    name: 'username',
    displayName: 'Username',
    type: 'text',
    required: true,
  },
  password: {
    name: 'password',
    displayName: 'Password',
    type: 'password',
    required: true,
  },
  token: {
    name: 'token',
    displayName: 'Token',
    type: 'password',
    required: true,
  },
};

// Helper to create auth property with description
const createAuthProperty = (base: any, authType: string) => ({
  ...base,
  description: `${base.displayName} for ${authType} Authentication`,
});

// Refactored credential configs
export const credentialConfigs: ICredentialConfig[] = [
  {
    name: 'basic_auth',
    displayName: 'Basic Auth',
    description: 'Username and password for Basic Authentication',
    properties: [
      createAuthProperty(commonPropertyTypes.username, 'Basic'),
      createAuthProperty(commonPropertyTypes.password, 'Basic'),
    ],
  },
  {
    name: 'digest_auth',
    displayName: 'Digest Auth',
    description: 'Username and password for Digest Authentication',
    properties: [
      createAuthProperty(commonPropertyTypes.username, 'Digest'),
      createAuthProperty(commonPropertyTypes.password, 'Digest'),
    ],
  },
  {
    name: 'api_key',
    displayName: 'API Key',
    description: 'API Key for authentication',
    properties: [
      {
        name: 'apiKey',
        displayName: 'API Key',
        description: 'The API key value',
        type: 'password',
        required: true,
      },
      {
        name: 'addTo',
        displayName: 'Add To',
        description: 'Where to add the API key',
        type: 'options',
        options: [
          { name: 'header', value: 'header' },
          { name: 'query', value: 'query' },
        ],
        default: 'header',
        required: true,
      },
      {
        name: 'keyName',
        displayName: 'Key Name',
        description: 'The name to use for the API key (header name or query parameter name)',
        type: 'text',
        default: 'X-API-Key',
        required: true,
      },
    ],
  },
  {
    name: 'bearer_token',
    displayName: 'Bearer Token',
    description: 'Bearer token for OAuth or JWT authentication',
    properties: [commonPropertyTypes.token],
  },
  {
    name: 'oauth2',
    displayName: 'OAuth 2.0',
    description: 'OAuth 2.0 credentials for authentication',
    properties: [
      {
        name: 'grantType',
        displayName: 'Grant Type',
        description: 'The OAuth 2.0 grant type to use for authentication',
        type: 'options',
        options: [
          { name: 'Client Credentials', value: 'client_credentials' },
          { name: 'Authorization Code', value: 'authorization_code' },
          { name: 'Password', value: 'password' },
          { name: 'Implicit', value: 'implicit' },
          { name: 'Refresh Token', value: 'refresh_token' },
        ],
        default: 'refresh_token',
        required: true,
      },
      {
        name: 'clientId',
        displayName: 'Client ID',
        description: 'OAuth 2.0 client ID',
        type: 'text',
        required: true,
      },
      {
        name: 'clientSecret',
        displayName: 'Client Secret',
        description: 'OAuth 2.0 client secret',
        type: 'password',
        required: true,
        visibleIf: {
          grantType: ['client_credentials', 'authorization_code', 'password', 'refresh_token'],
        },
      },
      {
        name: 'accessToken',
        displayName: 'Access Token',
        description: 'OAuth 2.0 access token (if you already have one)',
        type: 'password',
        required: false,
      },
      {
        name: 'refreshToken',
        displayName: 'Refresh Token',
        description: 'OAuth 2.0 refresh token (required for refresh_token grant type)',
        type: 'password',
        required: false,
        visibleIf: {
          grantType: ['authorization_code', 'password', 'refresh_token'],
        },
      },
      {
        name: 'tokenEndpoint',
        displayName: 'Token Endpoint',
        description: 'The endpoint URL for obtaining tokens',
        type: 'text',
        required: false,
        visibleIf: {
          grantType: ['client_credentials', 'authorization_code', 'password', 'refresh_token'],
        },
      },
      {
        name: 'authorizationEndpoint',
        displayName: 'Authorization Endpoint',
        description:
          'The endpoint URL for authorization (required for authorization_code and implicit grant types)',
        type: 'text',
        required: false,
        visibleIf: {
          grantType: ['authorization_code', 'implicit'],
        },
      },
      {
        name: 'redirectUri',
        displayName: 'Redirect URI',
        description:
          'The URI to redirect to after authorization (required for authorization_code and implicit grant types)',
        type: 'text',
        required: false,
        visibleIf: {
          grantType: ['authorization_code', 'implicit'],
        },
      },
      {
        name: 'scope',
        displayName: 'Scope',
        description: 'Space-separated list of scopes to request',
        type: 'text',
        required: false,
      },
      {
        name: 'username',
        displayName: 'Username',
        description: 'Username for password grant type',
        type: 'text',
        required: false,
        visibleIf: {
          grantType: ['password'],
        },
      },
      {
        name: 'password',
        displayName: 'Password',
        description: 'Password for password grant type',
        type: 'password',
        required: false,
        visibleIf: {
          grantType: ['password'],
        },
      },
      {
        name: 'tokenType',
        displayName: 'Token Type',
        description: 'The type of token to use in the Authorization header',
        type: 'options',
        options: [
          { name: 'Bearer', value: 'Bearer' },
          { name: 'MAC', value: 'MAC' },
          { name: 'Custom', value: 'custom' },
        ],
        default: 'Bearer',
        required: false,
      },
      {
        name: 'customTokenType',
        displayName: 'Custom Token Type',
        description: 'Custom token type to use in the Authorization header',
        type: 'text',
        required: false,
        visibleIf: {
          tokenType: ['custom'],
        },
      },
      {
        name: 'tokenExpiresIn',
        displayName: 'Token Expires In',
        description: 'The number of seconds until the token expires (for automatic refresh)',
        type: 'number',
        required: false,
      },
      {
        name: 'tokenExpiresAt',
        displayName: 'Token Expires At',
        description: 'The timestamp when the token expires (for automatic refresh)',
        type: 'text',
        required: false,
      },
      {
        name: 'autoRefresh',
        displayName: 'Auto Refresh',
        description: 'Automatically refresh the token when it expires',
        type: 'boolean',
        default: true,
        required: false,
        visibleIf: {
          grantType: ['authorization_code', 'password', 'refresh_token'],
        },
      },
      {
        name: 'additionalParameters',
        displayName: 'Additional Parameters',
        description: 'Additional parameters to include in the token request',
        type: 'keyvalue',
        default: {
          statusCode: {
            name: 'statusCode',
            displayName: 'Status Code',
            type: 'number',
            description: 'The HTTP status code of the response',
            default: 200,
          },
          headers: {
            name: 'headers',
            displayName: 'Response Headers',
            type: 'keyvalue',
            description: 'The headers from the response',
            default: {
              'Content-Type': {
                name: 'Content-Type',
                displayName: 'Content-Type',
                description: 'The content type of the response',
                type: 'text',
                default: 'application/json',
              },
              'X-API-Key': {
                name: 'X-API-Key',
                displayName: 'X-API-Key',
                description: 'The API key to use for the response',
                type: 'text',
                default: '1234567890',
              },
            },
          },
          body: {
            name: 'body',
            displayName: 'Response Body',
            type: 'keyvalue',
            description: 'The body of the response',
            default: {
              message: {
                name: 'message',
                displayName: 'Message',
                description: 'The message of the response',
                type: 'text',
                default: 'Hello, world!',
              },
            },
          },
        },
        required: false,
      },
    ],
  },
  {
    name: 'aws_sig_v4',
    displayName: 'AWS Signature v4',
    description: 'AWS Signature Version 4 authentication',
    properties: [
      {
        name: 'accessKey',
        displayName: 'Access Key',
        description: 'AWS access key ID',
        type: 'text',
        required: true,
      },
      {
        name: 'secretKey',
        displayName: 'Secret Key',
        description: 'AWS secret access key',
        type: 'password',
        required: true,
      },
      {
        name: 'region',
        displayName: 'Region',
        description: 'AWS region (e.g., us-east-1)',
        type: 'text',
        required: true,
        pattern: '^[a-z]+-[a-z]+-\\d+$',
      },
      {
        name: 'service',
        displayName: 'Service',
        description: 'AWS service name (e.g., s3, dynamodb)',
        type: 'text',
        required: true,
      },
    ],
  },
  {
    name: 'ntlm_auth',
    displayName: 'NTLM Auth',
    description: 'Windows NTLM Authentication',
    properties: [
      {
        name: 'username',
        displayName: 'Username',
        description: 'Username for NTLM Authentication',
        type: 'text',
        required: true,
      },
      {
        name: 'password',
        displayName: 'Password',
        description: 'Password for NTLM Authentication',
        type: 'password',
        required: true,
      },
      {
        name: 'domain',
        displayName: 'Domain',
        description: 'Windows domain for NTLM Authentication',
        type: 'text',
        required: false,
      },
      {
        name: 'workstation',
        displayName: 'Workstation',
        description: 'Workstation name for NTLM Authentication',
        type: 'text',
        required: false,
      },
    ],
  },
  {
    name: 'wsse_auth',
    displayName: 'WSSE Auth',
    description: 'WS-Security UsernameToken authentication',
    properties: [
      {
        name: 'username',
        displayName: 'Username',
        description: 'Username for WSSE Authentication',
        type: 'text',
        required: true,
      },
      {
        name: 'password',
        displayName: 'Password',
        description: 'Password for WSSE Authentication',
        type: 'password',
        required: true,
      },
    ],
  },
];

const getCredentialConfig = (name: string) => {
  return credentialConfigs.find(config => config.name === name);
};

const getCategories = (...indexs: number[]) => {
  return mockCategories.filter((_, index) => indexs.includes(index)).map(item => item.name);
};

export const mockTools: any[] = [
  {
    id: '1',
    name: 'OpenAI',
    displayName: 'OpenAI',
    description: 'OpenAI API',
    type: CredentialType.API_KEY,
    provider: ProviderType.OPENAI,
    categories: getCategories(1, 2, 3),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          apiKey: {
            name: 'apiKey',
            displayName: 'OpenAI API key',
            description: 'The OpenAI API key value',
            type: 'password',
            required: true,
            pattern: '^sk-[a-zA-Z0-9]+$',
          },
          baseURL: {
            name: 'baseURL',
            displayName: 'Base URL',
            description: 'The base URL for the OpenAI API',
            type: 'text',
            default: 'https://api.openai.com/v1',
          },
          organization: {
            name: 'organization',
            displayName: 'Organization',
            description: 'The organization for the OpenAI API',
            type: 'text',
          },
        },
      },
      oauth2: {
        ...getCredentialConfig('oauth2'),
      },
    },
  },
  {
    id: '2',
    name: 'Slack',
    displayName: 'Slack',
    description: 'Slack API',
    type: CredentialType.API_KEY,
    provider: ProviderType.SLACK,
    categories: getCategories(2, 3, 4),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          apiKey: {
            name: 'apiKey',
            displayName: 'Slack API key',
            description: 'The Slack API key value',
            type: 'password',
          },
        },
      },
    },
  },
  {
    id: '3',
    name: 'Google',
    displayName: 'Google',
    description: 'Google API',
    type: CredentialType.API_KEY,
    provider: ProviderType.GOOGLE,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          apiKey: {
            name: 'apiKey',
            displayName: 'Google API key',
            description: 'The Google API key value',
            type: 'password',
            required: true,
          },
          clientId: {
            name: 'clientId',
            displayName: 'Client ID',
            description: 'The client ID for the Google API',
            type: 'text',
          },
          clientSecret: {
            name: 'clientSecret',
            displayName: 'Client Secret',
            description: 'The client secret for the Google API',
            type: 'password',
          },
          refreshToken: {
            name: 'refreshToken',
            displayName: 'Refresh Token',
            description: 'The refresh token for the Google API',
            type: 'password',
          },
          accessToken: {
            name: 'accessToken',
            displayName: 'Access Token',
            description: 'The access token for the Google API',
            type: 'password',
          },
          expiresAt: {
            name: 'expiresAt',
            displayName: 'Expires At',
            description: 'The timestamp when the access token expires',
            type: 'text',
          },
          scopes: {
            name: 'scopes',
            displayName: 'Scopes',
            description: 'The scopes for the Google API',
            type: 'text',
          },
        },
      },
    },
  },
  {
    id: '4',
    name: 'AWS',
    displayName: 'AWS',
    description: 'AWS API',
    type: CredentialType.API_KEY,
    provider: ProviderType.AWS,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          accessKeyId: {
            name: 'accessKeyId',
            displayName: 'AWS access key ID',
            description: 'The access key ID for the AWS API',
            type: 'text',
          },
          secretAccessKey: {
            name: 'secretAccessKey',
            displayName: 'AWS secret access key',
            description: 'The secret access key for the AWS API',
            type: 'password',
          },
          sessionToken: {
            name: 'sessionToken',
            displayName: 'Session Token',
            description: 'The session token for the AWS API',
            type: 'password',
          },
          region: {
            name: 'region',
            displayName: 'Region',
            description: 'The region for the AWS API',
            type: 'text',
          },
        },
      },
    },
  },
  {
    id: '5',
    name: 'GitHub',
    displayName: 'GitHub',
    description: 'GitHub API',
    type: CredentialType.API_KEY,
    provider: ProviderType.GITHUB,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          personalAccessToken: {
            name: 'personalAccessToken',
            displayName: 'Personal Access Token',
            description: 'The personal access token for the GitHub API',
            type: 'password',
            pattern: '^(ghp_|github_pat_)[a-zA-Z0-9_]+$',
          },
          clientId: {
            name: 'clientId',
            displayName: 'Client ID',
            description: 'The client ID for the GitHub API',
            type: 'text',
          },
          clientSecret: {
            name: 'clientSecret',
            displayName: 'Client Secret',
            description: 'The client secret for the GitHub API',
            type: 'password',
          },
          refreshToken: {
            name: 'refreshToken',
            displayName: 'Refresh Token',
            description: 'The refresh token for the GitHub API',
            type: 'password',
          },
          accessToken: {
            name: 'accessToken',
            displayName: 'Access Token',
            description: 'The access token for the GitHub API',
            type: 'password',
          },
          expiresAt: {
            name: 'expiresAt',
            displayName: 'Expires At',
            description: 'The timestamp when the access token expires',
            type: 'text',
          },
          scopes: {
            name: 'scopes',
            displayName: 'Scopes',
            description: 'The scopes for the GitHub API',
            type: 'text',
          },
        },
      },
    },
  },
  {
    id: '6',
    name: 'Azure',
    displayName: 'Azure',
    description: 'Azure API',
    type: CredentialType.API_KEY,
    provider: ProviderType.AZURE,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          apiKey: {
            name: 'apiKey',
            displayName: 'Azure API key',
            description: 'The Azure API key value',
            type: 'password',
          },
          clientId: {
            name: 'clientId',
            displayName: 'Client ID',
            description: 'The client ID for the Azure API',
            type: 'text',
          },
          clientSecret: {
            name: 'clientSecret',
            displayName: 'Client Secret',
            description: 'The client secret for the Azure API',
            type: 'password',
          },
          tenantId: {
            name: 'tenantId',
            displayName: 'Tenant ID',
            description: 'The tenant ID for the Azure API',
            type: 'text',
          },
          resourceUrl: {
            name: 'resourceUrl',
            displayName: 'Resource URL',
            description: 'The resource URL for the Azure API',
            type: 'text',
          },
          subscriptionId: {
            name: 'subscriptionId',
            displayName: 'Subscription ID',
            description: 'The subscription ID for the Azure API',
            type: 'text',
          },
        },
      },
    },
  },
  {
    id: '7',
    name: 'Anthropic',
    displayName: 'Anthropic',
    description: 'Anthropic API',
    type: CredentialType.API_KEY,
    provider: ProviderType.ANTHROPIC,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      api_key: {
        name: 'api_key',
        displayName: 'API Key',
        description: 'API Key for authentication',
        properties: {
          apiKey: {
            name: 'apiKey',
            displayName: 'Anthropic API key',
            description: 'The Anthropic API key value',
            type: 'password',
            required: true,
            pattern: '^sk_ant_[a-zA-Z0-9]+$',
          },
          baseURL: {
            name: 'baseURL',
            displayName: 'Base URL',
            description: 'The base URL for the Anthropic API',
            type: 'text',
          },
        },
      },
    },
  },
  {
    id: '8',
    name: 'Custom',
    displayName: 'Custom',
    description: 'Custom API',
    type: CredentialType.API_KEY,
    provider: ProviderType.CUSTOM,
    categories: getCategories(1, 3, 4, 5),
    credentials: {
      properties: {
        api_key: {
          name: 'custom',
          displayName: 'Custom',
          description: 'Custom API',
          properties: {
            customField: {
              name: 'customField',
              displayName: 'Custom Field',
              description: 'The custom field for the Custom API',
              type: 'keyvalue',
              required: true,
            },
          },
        },
      },
    },
  },
];
