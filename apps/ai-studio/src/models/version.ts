import { ResourceType } from '@/types';

export interface IVersion {
  id: string;
  name: string;
  description?: string;
  updatedAt?: string;
  version: string;
  workspaceId: string;
  resourceType: ResourceType;
  resourceId?: string;
  resourceName?: string;
  snapshot?: {
    code?: string;
    settings?: {
      [key: string]: any;
    };
    trigger?: string;
    steps?: string[];
  };
  createdAt?: string;
}

export interface IVersionCreatePayload extends Omit<IVersion, 'id' | 'createdAt' | 'version'> {}
