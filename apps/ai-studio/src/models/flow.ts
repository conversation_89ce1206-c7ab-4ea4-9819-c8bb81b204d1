export const enum FlowNodeType {
  AddNode = 'vitual-node-add', /// For displaying purpose.
  AddPathNode = 'vitual-node-add-path', /// For displaying purpose.
  Branch = 'branch',
  Chatbot = 'chatbot',
  Code = 'code',
  DecaAiWidgets = 'deca-ai-widgets',
  DecaCrm = 'deca-crm',
  DecaKb = 'deca-kb',
  DecaLivechat = 'deca-livechat',
  DecaTables = 'deca-tables',
  EmptyNode = 'empty-node', /// For displaying purpose.
  EventTrigger = 'event-trigger',
  Filter = 'filter',
  Formatter = 'formatter',
  Function = 'function',
  Gmail = 'gmail',
  GoogleCalendar = 'google-calendar',
  GoogleDocs = 'google-docs',
  GoogleDrive = 'google-drive',
  GoogleSheets = 'google-sheets',
  GoogleSlides = 'google-slides',
  Hubspot = 'hubspot',
  Http = 'http',
  Looping = 'looping', /// For displaying purpose.
  LoopingFrame = 'looping-frame', /// For displaying purpose.
  OpenAI = 'openai',
  Pages = 'pages',
  Path = 'path',
  Schedule = 'schedule',
  ScheduleTrigger = 'schedule-trigger',
  Slack = 'slack',
  SubPathNode = 'virtual-node-sub-path', /// For displaying purpose.
  Wait = 'wait',
  WebhookTrigger = 'webhook-trigger',
  ZoomMeetings = 'zoom-meetings',
  NewTrigger = 'new-trigger',
}

export interface Flow {
  id: string;
  name: string;
  description: string;
  models?: string[];
  status: 'enabled' | 'disabled';
  nodes?: FlowNodes;
  triggers: FlowNodes;
  settings?: FlowSettings;
  metadata?: FlowMetadata;
  createdAt?: string;
  updatedAt?: string;
  workspaceId?: string;
  version?: string;
}

export interface FlowNodes {
  [key: string]: FlowNodeData;
}

export interface BaseFlowSetting {
  [key: string]: any;
}
export interface FlowWebhookTriggerSetting extends BaseFlowSetting {
  path: string;
  method: string;
  headers?: {
    [key: string]: string;
  };
}

export interface FlowExtractMessageSetting extends BaseFlowSetting {
  operation: string;
  field: string;
  from: string;
}

export interface FlowClassifyIntentSetting extends BaseFlowSetting {
  model: string;
  prompt: string;
  temperature: number;
  max_tokens: number;
}

export interface FlowRouteByIntentSetting extends BaseFlowSetting {
  paths: { id: string; name?: string; settings: BaseFlowSetting; next: string }[];
}

interface FlowGenerateResponse extends BaseFlowSetting {
  model: string;
  prompt: string;
  temperature: number;
  max_tokens: number;
}

interface FlowGenerateAccountResponseSetting extends FlowGenerateResponse {}

interface FlowGenerateBillingResponseSetting extends FlowGenerateResponse {}

interface FlowGenerateProductResponseSetting extends FlowGenerateResponse {}

interface FlowLoopSetting extends BaseFlowSetting {
  nextChildID: string;
}

export type FlowNodeSetting =
  | FlowWebhookTriggerSetting
  | FlowExtractMessageSetting
  | FlowClassifyIntentSetting
  | FlowRouteByIntentSetting
  | FlowGenerateAccountResponseSetting
  | FlowGenerateBillingResponseSetting
  | FlowGenerateProductResponseSetting
  | FlowLoopSetting;

export interface FlowNodeData {
  id: string;
  name: FlowNodeType;
  displayName: string;
  description?: string;
  settings: FlowNodeSetting;
  // loop?: string; // For looping node only, next node id belongs to looping node
  next?: string;
  prev?: string; // FE Store it for reference purpose.
  icon?: string;
  action?: string;
  trigger?: string;
}

export interface FlowSettings {
  errorHandling?: 'continue' | 'fail' | 'retry';
  timeout?: number;
  retryAttempts?: number;
  retryPolicy?: {
    maxAttempts?: number;
    backoffMultiplier?: number;
    initialDelay?: number;
  };
  concurrency?: {
    maxInstances?: number;
    policy?: 'queue' | 'reject' | 'replace';
  };
  timezone?: string;
}

export interface FlowMetadata {
  [key: string]: any;
  createdAt?: string;
  createdBy?: string;
  category?: string;
  priority?: string;
  tags?: string[];
}

export type FlowCreatePayload = Omit<
  Flow,
  'id' | 'createdAt' | 'updatedAt' | 'workspaceId' | 'version'
>;

export enum TypeEdge {
  Arrow = 'arrow',
  OnlyLine = 'only_line',
}

export type ConnectionData = {
  parentId: string;
  nextNodeId?: string;
  directSubNodeLooping?: boolean;
  directNextNodeLooping?: boolean;
  directAddNodeFromTrigger?: boolean;
  isNotInALoopingNode?: boolean;
};

export enum FlowTypeNode {
  TriggerNode = 'trigger-node',
  Node = 'node',
  Path = 'path',
  SubPath = 'sub-path',
  Looping = 'looping',
}

export type PathValue = { path: string; value: any };
