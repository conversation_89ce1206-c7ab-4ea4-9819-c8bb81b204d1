import AICard from '@/components/AICard';
import GridLayout from '@/components/GridLayout';
import { usePromptContext } from '@/contexts/PromptContext';
import { IPrompt } from '@/models/prompt';
import { useTranslate } from '@tolgee/react';
import { useNavigate, useParams } from 'react-router-dom';
interface PromptListProps {
  prompts: IPrompt[];
  isFullWidth?: boolean;
  onEditPrompt: (prompt: IPrompt) => void;
}

const PromptList = ({ prompts, isFullWidth = false, onEditPrompt }: PromptListProps) => {
  const { t } = useTranslate('prompt');
  const navigate = useNavigate();
  const { workspaceId } = useParams();
  const { duplicatePrompt, exportPrompt, handleDeletePrompt } = usePromptContext();

  return (
    <GridLayout isFullWidth={isFullWidth}>
      {prompts?.map(prompt => (
        <AICard
          isFullWidth={isFullWidth}
          key={prompt.id}
          title={prompt.name}
          description={prompt.description}
          aiModel={prompt.settings?.model || ' '}
          updatedAt={prompt.updatedAt}
          actions={{
            editLabel: t('editAction'),
            onDelete: () => {
              handleDeletePrompt(prompt);
            },
            onEdit: () => {
              onEditPrompt(prompt);
            },
            onDuplicate: () => {
              duplicatePrompt(prompt);
            },
            onExport: () => {
              exportPrompt(prompt.id);
            },
          }}
          onClick={() => {
            navigate(`/studio/${workspaceId}/prompts/${prompt.id}`);
          }}
        />
      ))}
    </GridLayout>
  );
};

export default PromptList;
