import AICard from '@/components/AICard';
import AIPagination from '@/components/AIPagination';
import GridLayout from '@/components/GridLayout';
import SearchBar from '@/components/SearchBar';
import { usePromptContext } from '@/contexts/PromptContext';
import { useListTemplate } from '@/hooks/useListTemplate';
import { mockPromptCategories } from '@/mockdata/prompt';
import { Box, Flex, rem, Text, Textarea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useState } from 'react';
import { useParams } from 'react-router-dom';

const useStyles = createStyles(theme => ({
  gridLayout: {
    marginTop: rem(24),
    marginBottom: rem(24),
  },
  searchBar: {
    maxWidth: rem(500),
  },
  textarea: {
    '& textarea': {
      backgroundColor: theme.colors.decaNavy[0],
    },
  },
}));
const PromptTemplate = () => {
  const { t } = useTranslate(['prompt', 'common']);
  const { classes } = useStyles();
  const {
    selectedCategory,
    setSelectedCategory,
    selectedTemplate,
    setSelectedTemplate,
    createPrompt,
  } = usePromptContext();
  const [searchValue, setSearchValue] = useState('');
  const { workspaceId } = useParams();
  const [limit, setLimit] = useState(10);
  const [cursor, setCursor] = useState('');

  const { data: templates } = useListTemplate({
    workspaceId: workspaceId || '',
    limit,
    cursor,
    searchValue,
    resourceType: 'prompt',
  });

  const handleApply = () => {
    createPrompt({
      name: selectedTemplate?.name ?? '',
      description: selectedTemplate?.description ?? '',
    });
  };

  return (
    <Box data-testid='prompt-template' mb={rem(-20)}>
      {selectedTemplate ? (
        <Box data-testid='prompt-template-selected'>
          <Flex justify='space-between'>
            <Text fz={20}>{selectedTemplate.name}</Text>
            <DecaButton variant='secondary' size='sm' onClick={handleApply}>
              {t('use')}
            </DecaButton>
          </Flex>
          <Text c={'decaGrey.5'} mt={rem(10)}>
            {selectedTemplate.description}
          </Text>
          <Box mt={rem(20)}>
            <Text fw={600} mb={rem(10)}>
              {t('instructions.title', { ns: 'common' })}
            </Text>
            <Textarea
              rows={10}
              value={'render system message'}
              readOnly
              className={classes.textarea}
            />
          </Box>
          <Box mt={rem(20)}>
            <Text fw={600} mb={rem(10)}>
              {t('userMessage.title', { ns: 'common' })}
            </Text>
            <Textarea
              rows={10}
              value={'render user message'}
              readOnly
              className={classes.textarea}
            />
          </Box>
        </Box>
      ) : (
        <>
          {selectedCategory ? (
            <Box data-testid='prompt-template-list-by-category'>
              <Text mb={rem(16)} fw={600}>
                {selectedCategory.name || ''}
              </Text>
              <SearchBar
                className={classes.searchBar}
                value={searchValue}
                placeholder={t('searchPromptTemplate')}
                onChange={val => setSearchValue(val)}
              />
              <GridLayout className={classes.gridLayout}>
                {templates?.data?.map(prompt => (
                  <AICard
                    key={prompt.id}
                    title={prompt.name}
                    description={prompt.description}
                    onClick={() => setSelectedTemplate(prompt)}
                  />
                ))}
              </GridLayout>
              <AIPagination
                key={cursor}
                limit={limit}
                onChangeLimit={setLimit}
                onCursorChange={setCursor}
                nextCursor={templates?.nextCursor}
                prevCursor={templates?.prevCursor}
                onNext={() => setCursor(templates?.nextCursor ?? '')}
                onPrevious={() => setCursor(templates?.prevCursor ?? '')}
                useUrlParams={false}
              />
            </Box>
          ) : (
            <Box data-testid='prompt-template-category-list'>
              <Text>{t('chooseTemplate')}</Text>
              <GridLayout className={classes.gridLayout}>
                {mockPromptCategories.map(templateCategory => (
                  <AICard
                    key={templateCategory.id}
                    title={templateCategory.name}
                    description={templateCategory.description}
                    onClick={() => setSelectedCategory(templateCategory)}
                  />
                ))}
              </GridLayout>
            </Box>
          )}
        </>
      )}
    </Box>
  );
};

export default PromptTemplate;
