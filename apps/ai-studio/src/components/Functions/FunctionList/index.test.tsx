import { FunctionContextProvider } from '@/contexts/Function/FunctionContext';
import { renderWithMantine } from '@/utils/test';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { AppContextProvider } from '@/contexts/AppContext';
import { mockFunctions } from '@/mockdata/functions';
import { IFunction } from '@/models/function';
import FunctionList from '.';

// Mock the function context
const mockUpdateFunction = vi.fn();
const mockCreateFunction = vi.fn();
const mockDeleteFunction = vi.fn();
const mockDuplicateFunction = vi.fn();
const mockExportFunction = vi.fn();

// Mock navigation functions
const mockNavigate = vi.fn();
const mockUseParams = vi.fn(() => ({ workspaceId: 'test-workspace-123' }));

// Mock the function context provider
vi.mock('@/contexts/Function/FunctionContext', () => ({
  FunctionContextProvider: ({ children }: { children: React.ReactNode }) => children,
  useFunctionContext: () => ({
    updateFunction: mockUpdateFunction,
    createFunction: mockCreateFunction,
    deleteFunction: mockDeleteFunction,
    duplicateFunction: mockDuplicateFunction,
    exportFunction: mockExportFunction,
  }),
}));

// Mock react-router-dom hooks
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useParams: () => mockUseParams(),
  };
});

describe('FunctionList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderWithProvider = (isFullWidth?: boolean) => {
    return renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <FunctionContextProvider>
            <FunctionList
              functions={mockFunctions}
              isFullWidth={isFullWidth}
              openedEditModal={false}
              isCreatingFunction={false}
              openEditModal={() => {}}
              closeEditModal={() => {}}
              editFunction={null}
              setEditFunction={() => {}}
            />
          </FunctionContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );
  };

  const renderWithEditModal = (
    isCreatingFunction = false,
    editFunction: IFunction | null = null,
    openedEditModal = true
  ) => {
    const mockOpenEditModal = vi.fn();
    const mockCloseEditModal = vi.fn();
    const mockSetEditFunction = vi.fn();

    return {
      ...renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={openedEditModal}
                isCreatingFunction={isCreatingFunction}
                openEditModal={mockOpenEditModal}
                closeEditModal={mockCloseEditModal}
                editFunction={editFunction}
                setEditFunction={mockSetEditFunction}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      ),
      mockOpenEditModal,
      mockCloseEditModal,
      mockSetEditFunction,
    };
  };

  it('renders function list correctly', () => {
    renderWithProvider();

    expect(screen.getByText('Function 1')).toBeInTheDocument();
    expect(screen.getByText('Function 2')).toBeInTheDocument();
  });

  it('renders function list correctly display list layout first render', () => {
    renderWithProvider();
    expect(screen.getByTestId('grid-layout')).toBeInTheDocument();
    expect(screen.getAllByTestId('aic-card')[0]).toBeInTheDocument();
  });

  it('renders function list correctly display grid layout first render', () => {
    renderWithProvider(true);
    expect(screen.getByTestId('grid-layout')).toBeInTheDocument();
    expect(screen.getAllByTestId('aic-card-full-width')[0]).toBeInTheDocument();
  });

  it('show menu items when menu button is clicked', async () => {
    renderWithProvider();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    expect(menuButton).toBeInTheDocument();

    const user = userEvent.setup();
    await user.click(menuButton);

    const editAction = await screen.findByText('editFunction');
    expect(editAction).toBeInTheDocument();
  });

  it('show delete modal when delete button is clicked', async () => {
    renderWithProvider();

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    // Instead of looking for 'deleteFunction', look for the function name in the modal
    // or use a more reliable selector like a test ID
    const deleteModal = await screen.findByText('Function 1');
    expect(deleteModal).toBeInTheDocument();
  });

  it('delete function successfully', async () => {
    renderWithProvider();

    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const deleteAction = await screen.findByText('action.delete');
    await user.click(deleteAction);

    const deleteModal = await screen.findByText('Function 1');
    expect(deleteModal).toBeInTheDocument();

    const confirmButton = await screen.findByTestId('confirm-modal');
    await user.click(confirmButton);

    await waitFor(() => {
      expect(screen.queryByText('deleteFunction')).toBeNull();
    });
  });

  it('renders empty state when no functions provided', () => {
    renderWithMantine(
      <BrowserRouter>
        <AppContextProvider>
          <FunctionContextProvider>null</FunctionContextProvider>
        </AppContextProvider>
      </BrowserRouter>
    );

    expect(screen.queryByTestId('grid-layout')).toBeNull();
  });

  it('check if fullwidth layout grid column is 1', () => {
    renderWithProvider(true);
    expect(screen.getAllByTestId('aic-card-full-width')[0]).toBeInTheDocument();
  });

  it('check duplicate function successfully', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const duplicateAction = await screen.findByText('action.duplicate');
    await user.click(duplicateAction);

    await waitFor(() => {
      expect(screen.queryByText('duplicateFunction')).toBeNull();
    });
  });

  it('check export function successfully', async () => {
    renderWithProvider();
    const user = userEvent.setup();

    const menuButton = screen.getAllByTestId('am-target-button')[0];
    await user.click(menuButton);

    const exportAction = await screen.findByText('action.export');
    await user.click(exportAction);

    await waitFor(() => {
      expect(screen.queryByText('exportFunction')).toBeNull();
    });
  });

  describe('Edit Modal Tests', () => {
    it('should call updateFunction when confirming update with edit function', async () => {
      const editFunction = mockFunctions[0];
      const { mockCloseEditModal } = renderWithEditModal(false, editFunction, true);

      const user = userEvent.setup();

      // Find the title input and update it
      const titleInput = screen.getByDisplayValue(editFunction.name);
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Function Name');

      // Find the description input and update it
      const descriptionInput = screen.getByDisplayValue(editFunction.description);
      await user.clear(descriptionInput);
      await user.type(descriptionInput, 'Updated description');

      // Find and click the confirm button
      const confirmButton = screen.getByTestId('confirm-edit-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUpdateFunction).toHaveBeenCalledWith({
          ...editFunction,
          name: 'Updated Function Name',
          description: 'Updated description',
        });
        expect(mockCloseEditModal).toHaveBeenCalled();
      });
    });

    it('should not call updateFunction when editFunction is null', async () => {
      const { mockCloseEditModal } = renderWithEditModal(false, null, true);

      const user = userEvent.setup();

      // Find and click the confirm button
      const confirmButton = screen.getByTestId('confirm-edit-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUpdateFunction).not.toHaveBeenCalled();
        expect(mockCloseEditModal).not.toHaveBeenCalled();
      });
    });

    it('should call createFunction when confirming create function', async () => {
      const { mockCloseEditModal } = renderWithEditModal(true, null, true);

      const user = userEvent.setup();

      // Find the title input and enter a new function name
      const titleInput = screen.getByTestId('title-input');
      await user.type(titleInput, 'New Function Name');

      // Find the description input and enter description
      const descriptionInput = screen.getByTestId('description-input');
      await user.type(descriptionInput, 'New function description');

      // Find and click the confirm button
      const confirmButton = screen.getByTestId('confirm-edit-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockCreateFunction).toHaveBeenCalledWith({
          name: 'New Function Name',
          description: 'New function description',
          code: expect.any(String), // SAMPLE_CODE
          settings: expect.any(Object), // INITIAL_SETTINGS
        });
        expect(mockCloseEditModal).toHaveBeenCalled();
      });
    });

    it('should handle empty description when creating function', async () => {
      const { mockCloseEditModal } = renderWithEditModal(true, null, true);

      const user = userEvent.setup();

      // Find the title input and enter a new function name
      const titleInput = screen.getByTestId('title-input');
      await user.type(titleInput, 'New Function Name');

      // Leave description empty
      // Find and click the confirm button
      const confirmButton = screen.getByTestId('confirm-edit-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockCreateFunction).toHaveBeenCalledWith({
          name: 'New Function Name',
          description: '',
          code: expect.any(String),
          settings: expect.any(Object),
        });
        expect(mockCloseEditModal).toHaveBeenCalled();
      });
    });

    it('should handle empty description when updating function', async () => {
      const editFunction = { ...mockFunctions[0], description: '' };
      const { mockCloseEditModal } = renderWithEditModal(false, editFunction, true);

      const user = userEvent.setup();

      // Find the title input and update it
      const titleInput = screen.getByDisplayValue(editFunction.name);
      await user.clear(titleInput);
      await user.type(titleInput, 'Updated Function Name');

      // Find and click the confirm button
      const confirmButton = screen.getByTestId('confirm-edit-modal');
      await user.click(confirmButton);

      await waitFor(() => {
        expect(mockUpdateFunction).toHaveBeenCalledWith({
          ...editFunction,
          name: 'Updated Function Name',
          description: '',
        });
        expect(mockCloseEditModal).toHaveBeenCalled();
      });
    });

    it('should initialize modal with correct values when editing function', () => {
      const editFunction = mockFunctions[0];
      renderWithEditModal(false, editFunction, true);

      // Check that the modal is initialized with the correct values
      const titleInput = screen.getByDisplayValue(editFunction.name);
      const descriptionInput = screen.getByDisplayValue(editFunction.description);

      expect(titleInput).toBeInTheDocument();
      expect(descriptionInput).toBeInTheDocument();
    });

    it('should initialize modal with empty values when creating function', () => {
      renderWithEditModal(true, null, true);

      // Check that the modal is initialized with empty values
      const titleInput = screen.getByTestId('title-input');
      const descriptionInput = screen.getByTestId('description-input');

      expect(titleInput).toHaveValue('');
      expect(descriptionInput).toHaveValue('');
    });

    it('should handle undefined description in edit function', () => {
      const editFunction = { ...mockFunctions[0], description: '' };
      renderWithEditModal(false, editFunction, true);

      // Should handle empty description gracefully
      const titleInput = screen.getByDisplayValue(editFunction.name);
      expect(titleInput).toBeInTheDocument();

      // Description should be empty string
      const descriptionInput = screen.getByDisplayValue('');
      expect(descriptionInput).toBeInTheDocument();
    });

    it('should handle empty name in edit function', () => {
      const editFunction = { ...mockFunctions[0], name: '' };
      renderWithEditModal(false, editFunction, true);

      // Should handle empty name gracefully
      const titleInput = screen.getByDisplayValue('');
      expect(titleInput).toBeInTheDocument();
    });
  });

  describe('Card Actions Tests', () => {
    it('should call setEditFunction and openEditModal when edit action is clicked', async () => {
      const mockSetEditFunction = vi.fn();
      const mockOpenEditModal = vi.fn();

      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={mockOpenEditModal}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={mockSetEditFunction}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the menu button of the first function
      const menuButton = screen.getAllByTestId('am-target-button')[0];
      await user.click(menuButton);

      // Click on the edit action
      const editAction = await screen.findByText('editFunction');
      await user.click(editAction);

      // Verify that setEditFunction was called with the correct function
      expect(mockSetEditFunction).toHaveBeenCalledWith(mockFunctions[0]);
      // Verify that openEditModal was called
      expect(mockOpenEditModal).toHaveBeenCalled();
    });

    it('should call setEditFunction with correct function when different function edit is clicked', async () => {
      const mockSetEditFunction = vi.fn();
      const mockOpenEditModal = vi.fn();

      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={mockOpenEditModal}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={mockSetEditFunction}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the menu button of the second function
      const menuButtons = screen.getAllByTestId('am-target-button');
      await user.click(menuButtons[1]);

      // Click on the edit action
      const editAction = await screen.findByText('editFunction');
      await user.click(editAction);

      // Verify that setEditFunction was called with the second function
      expect(mockSetEditFunction).toHaveBeenCalledWith(mockFunctions[1]);
      expect(mockOpenEditModal).toHaveBeenCalled();
    });
  });

  describe('Navigation Tests', () => {
    beforeEach(() => {
      mockNavigate.mockClear();
      mockUseParams.mockReturnValue({ workspaceId: 'test-workspace-123' });
    });

    it('should navigate to function detail page when card is clicked', async () => {
      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={() => {}}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={() => {}}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the first function card
      const functionCards = screen.getAllByTestId('aic-card');
      await user.click(functionCards[0]);

      // Verify navigation was called with correct path
      expect(mockNavigate).toHaveBeenCalledWith('/studio/test-workspace-123/functions/1');
    });

    it('should navigate to different function pages when different cards are clicked', async () => {
      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={() => {}}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={() => {}}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the second function card
      const functionCards = screen.getAllByTestId('aic-card');
      await user.click(functionCards[1]);

      // Verify navigation was called with correct path for second function
      expect(mockNavigate).toHaveBeenCalledWith('/studio/test-workspace-123/functions/2');
    });

    it('should navigate with different workspace id', async () => {
      mockUseParams.mockReturnValue({ workspaceId: 'different-workspace-456' });

      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={mockFunctions}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={() => {}}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={() => {}}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the first function card
      const functionCards = screen.getAllByTestId('aic-card');
      await user.click(functionCards[0]);

      // Verify navigation was called with different workspace id
      expect(mockNavigate).toHaveBeenCalledWith('/studio/different-workspace-456/functions/1');
    });

    it('should handle navigation when function id is undefined', async () => {
      const functionsWithUndefinedId = [{ ...mockFunctions[0], id: undefined }];

      renderWithMantine(
        <BrowserRouter>
          <AppContextProvider>
            <FunctionContextProvider>
              <FunctionList
                functions={functionsWithUndefinedId}
                openedEditModal={false}
                isCreatingFunction={false}
                openEditModal={() => {}}
                closeEditModal={() => {}}
                editFunction={null}
                setEditFunction={() => {}}
              />
            </FunctionContextProvider>
          </AppContextProvider>
        </BrowserRouter>
      );

      const user = userEvent.setup();

      // Click on the function card with undefined id
      const functionCard = screen.getByTestId('aic-card');
      await user.click(functionCard);

      // Verify navigation was called with undefined id
      expect(mockNavigate).toHaveBeenCalledWith('/studio/test-workspace-123/functions/undefined');
    });
  });
});
