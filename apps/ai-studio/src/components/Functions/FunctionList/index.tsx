import { memo, useCallback } from 'react';
import { useTranslate } from '@tolgee/react';
import { IFunction } from '@/models/function';
import { EditModal } from '@/components/Modals';
import { useFunctionContext } from '@/contexts/Function/FunctionContext';
import { useAppContext } from '@/contexts/AppContext';
import { useNavigate, useParams } from 'react-router-dom';
import { EditModalData } from '@/types';
import GridLayout from '@/components/GridLayout';
import AICard from '../../AICard';
import AIEmpty from '@/components/AIEmpty';
import { INITIAL_SETTINGS, SAMPLE_CODE } from '@/constants/function';

interface FunctionListProps {
  functions: IFunction[];
  isFullWidth?: boolean;
  openedEditModal: boolean;
  isCreatingFunction: boolean;
  openEditModal: () => void;
  closeEditModal: () => void;
  editFunction: IFunction | null;
  setEditFunction: (func: IFunction | null) => void;
}

const FunctionList = ({
  functions,
  isFullWidth = false,
  openedEditModal,
  openEditModal,
  closeEditModal,
  isCreatingFunction,
  editFunction,
  setEditFunction,
}: FunctionListProps) => {
  const navigate = useNavigate();
  const { t } = useTranslate('function');
  const { workspaceId } = useParams();
  const { openConfirmModal, closeConfirmModal } = useAppContext();
  const { updateFunction, duplicateFunction, exportFunction, deleteFunction, createFunction } =
    useFunctionContext();

  const handleDeleteFunction = useCallback(
    async (func: IFunction) => {
      openConfirmModal({
        title: t('deleteModalTitle'),
        name: func.name,
        onConfirm: async () => {
          await deleteFunction(func.id ?? '');
          closeConfirmModal();
        },
        onCancel: closeConfirmModal,
      });
    },
    [openConfirmModal, t, deleteFunction, closeConfirmModal]
  );

  const handleConfirmUpdateFunction = useCallback(
    async (data: EditModalData) => {
      if (!editFunction) return;

      await updateFunction({
        ...editFunction,
        name: data.title,
        description: data.description ?? '',
      });
      closeEditModal();
    },
    [editFunction, updateFunction, closeEditModal]
  );

  const handleConfirmCreateFunction = useCallback(
    async (data: EditModalData) => {
      await createFunction({
        name: data.title,
        description: data.description ?? '',
        code: SAMPLE_CODE,
        settings: INITIAL_SETTINGS,
      });
      closeEditModal();
    },
    [createFunction, closeEditModal]
  );

  return (
    <GridLayout isFullWidth={isFullWidth || functions.length === 0} data-testid='function-list'>
      {functions.length > 0 ? (
        functions.map(func => (
          <AICard
            isFullWidth={isFullWidth}
            key={func.id}
            title={func.name}
            description={func.description}
            updatedAt={func.updatedAt?.toString() ?? ''}
            actions={{
              editLabel: t('editFunction'),
              onDelete: () => {
                handleDeleteFunction(func);
              },
              onEdit: () => {
                setEditFunction(func);
                openEditModal();
              },
              onDuplicate: () => {
                duplicateFunction(func.id ?? '');
              },
              onExport: () => {
                exportFunction(func.id ?? '');
              },
            }}
            onClick={() => {
              navigate(`/studio/${workspaceId}/functions/${func.id}`);
            }}
          />
        ))
      ) : (
        <AIEmpty />
      )}
      <EditModal
        title={isCreatingFunction ? t('createFunctionLabel') : t('editNameDescriptionLabel')}
        opened={openedEditModal}
        onCancel={closeEditModal}
        onConfirm={isCreatingFunction ? handleConfirmCreateFunction : handleConfirmUpdateFunction}
        options={{
          titleLabel: t('functionName'),
        }}
        initialValues={
          editFunction
            ? {
                title: editFunction.name ?? '',
                description: editFunction.description ?? '',
              }
            : {
                title: '',
                description: '',
              }
        }
      />
    </GridLayout>
  );
};

export default memo(FunctionList);
