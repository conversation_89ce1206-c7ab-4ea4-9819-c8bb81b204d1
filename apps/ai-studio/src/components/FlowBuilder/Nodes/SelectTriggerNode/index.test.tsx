import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import SelectTriggerNode from './index';
import { FlowBuilderProvider } from '@/contexts/FlowBuilderContext';
import { useTranslate } from '@tolgee/react';

// Mock useTranslate
vi.mock('@tolgee/react', async () => {
  const actual = await vi.importActual('@tolgee/react');
  return {
    ...actual,
    useTranslate: vi.fn(),
  };
});

// Mock the FlowBuilderContext
vi.mock('@/contexts/FlowBuilderContext', () => ({
  useFlowBuilderContext: () => ({
    flowActionHandlers: {
      handleRemoveNode: vi.fn(),
    },
    handleOpenFormPanel: vi.fn(),
  }),
  FlowBuilderProvider: ({ children }) => <>{children}</>,
}));

// Mock the useFlowSchema hook
vi.mock('@/hooks/useFlowSchema', () => ({
  __esModule: true,
  default: () => ({
    schema: {
      displayName: 'Select a trigger',
      triggers: {},
      description: 'Select a trigger that starts your flow',
    },
  }),
}));

// Mock the MenuActionNode component
vi.mock('../../MenuActionNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ onEdit, onDelete, dataTestId }) => {
    return (
      <div data-testid={dataTestId || 'menu-action-node'}>
        <button data-testid='edit-button' data-action='edit' onClick={onEdit}>
          Edit
        </button>
        <button data-testid='delete-button' data-action='delete' onClick={onDelete}>
          Delete
        </button>
      </div>
    );
  }),
}));

// Mock the BaseSelectNode component
vi.mock('../BaseSelectNode', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({ title, description, dataTestId, menuContent }) => (
    <div data-testid={dataTestId}>
      <div data-testid='title'>{title}</div>
      <div data-testid='description'>{description}</div>
      <div data-testid='menu-content'>{menuContent}</div>
    </div>
  )),
}));

describe('SelectTriggerNode', () => {
  const nodeId = 'test-node-id';
  const mockOnEdit = vi.fn();
  const mockOnDelete = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    // Mock useTranslate
    (useTranslate as any).mockReturnValue({
      t: (key: string) => {
        const translations = {
          defaultNameActionNode: 'Select an action for your flow',
        };
        return translations[key] || key;
      },
    });
  });

  // Helper function to render the component with the FlowBuilderProvider
  const renderWithProvider = props => {
    return render(
      <FlowBuilderProvider>
        <SelectTriggerNode {...props} />
      </FlowBuilderProvider>
    );
  };

  it('should be defined', () => {
    expect(SelectTriggerNode).toBeDefined();
  });

  it('renders with correct props', () => {
    renderWithProvider({ id: nodeId, data: { orderedNumber: 1 } });

    expect(screen.getByTestId('text-trigger')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('Select a trigger');
    expect(screen.getByTestId('description')).toHaveTextContent(
      '1. Select an action for your flow'
    );
  });

  it('calls onEdit when edit button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId, data: { onEdit: mockOnEdit } });

    const editButton = screen.getByTestId('edit-button');
    await user.click(editButton);

    expect(mockOnEdit).toHaveBeenCalledTimes(1);
    expect(mockOnEdit).toHaveBeenCalledWith(nodeId);
  });

  it('calls onDelete when delete button is clicked', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId, data: { onDelete: mockOnDelete } });

    const deleteButton = screen.getByTestId('delete-button');
    await user.click(deleteButton);

    expect(mockOnDelete).toHaveBeenCalledTimes(0);
  });

  it('does not throw when callbacks are not provided', async () => {
    const user = userEvent.setup();

    renderWithProvider({ id: nodeId });

    const editButton = screen.getByTestId('edit-button');
    const deleteButton = screen.getByTestId('delete-button');

    // Should not throw errors when callbacks are not provided
    await expect(user.click(editButton)).resolves.not.toThrow();
    await expect(user.click(deleteButton)).resolves.not.toThrow();
  });
});
