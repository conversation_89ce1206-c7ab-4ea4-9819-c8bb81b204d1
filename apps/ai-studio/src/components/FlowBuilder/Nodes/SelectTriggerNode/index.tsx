import { useCallback, useMemo } from 'react';
import BaseSelectNode from '../BaseSelectNode';
import MenuActionNode from '../../MenuActionNode';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { FlowTypeNode, FlowNodeType } from '@/models/flow';
import useFlowSchema from '@/hooks/useFlowSchema';
import { useTranslate } from '@tolgee/react';

interface SelectTriggerNodeProps {
  id: string;
  data?: {
    onEdit?: (id: string) => void;
    onDelete?: (id: string) => void;
    orderedNumber?: number;
    [key: string]: any;
  };
  type: FlowNodeType;
}

export default function SelectTriggerNode({ id, data, type }: SelectTriggerNodeProps) {
  const { t } = useTranslate('flow');
  const { schema } = useFlowSchema({ flowNodeType: type });
  const { handleOpenFormPanel: handleOpenRightPanel, flowActionHandlers } = useFlowBuilderContext();

  const onEdit = useCallback(() => {
    data?.onEdit?.(id);
  }, [data, id]);

  const onDelete = useCallback(() => {
    flowActionHandlers?.handleRemoveNode({ nodeId: id, typeRemove: FlowTypeNode.TriggerNode });
  }, [flowActionHandlers, id]);

  const orderedNumber = data?.orderedNumber ?? 0;

  const title = useMemo(() => {
    return schema?.displayName;
  }, [schema?.displayName]);

  const displayName = useMemo(() => {
    const defaultName = t('defaultNameActionNode');
    if (data?.displayName === schema?.displayName) {
      return defaultName;
    }
    if (data?.displayName) {
      return data?.displayName;
    }
    return defaultName;
  }, [data?.displayName, schema?.displayName]);

  const description = useMemo(() => {
    return orderedNumber + '. ' + displayName;
  }, [orderedNumber, displayName]);

  return (
    <BaseSelectNode
      id={id}
      customIcon={schema?.icon}
      title={title}
      description={description}
      dataTestId='text-trigger'
      menuContent={<MenuActionNode onDelete={onDelete} onEdit={onEdit} />}
      openModalAction={() => handleOpenRightPanel(id)}
    />
  );
}
