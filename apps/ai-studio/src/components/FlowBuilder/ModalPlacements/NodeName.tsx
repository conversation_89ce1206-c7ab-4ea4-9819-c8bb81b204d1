import { ActionIcon, Flex, rem, Text } from '@mantine/core';
import { IconPencil } from '@tabler/icons-react';
import { useCallback, useEffect, useRef, useState } from 'react';

interface NodeNameProps {
  orderedNumber: number;
  displayName: string;
  onSave: (displayName: string) => void;
}

function NodeName({ orderedNumber, displayName: initialDisplayName, onSave }: NodeNameProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [displayName, setDisplayName] = useState(initialDisplayName);
  const ref = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setDisplayName(initialDisplayName);
  }, [initialDisplayName]);

  const handleEdit = useCallback(() => {
    setIsEditing(true);
    setTimeout(() => {
      ref.current?.select();
    }, 100);
  }, []);

  const handleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setDisplayName(event.target.value);
    },
    [setDisplayName]
  );

  const handleSave = useCallback(() => {
    setIsEditing(false);
    onSave(displayName);
  }, [displayName, onSave]);

  return (
    <Flex gap={4} align='center' flex={1}>
      <Text fw={500} size='lg'>
        {orderedNumber ? orderedNumber + '. ' : ''}
      </Text>
      {isEditing ? (
        <input
          ref={ref}
          value={displayName}
          onChange={handleChange}
          onBlur={handleSave}
          onKeyDown={e => {
            if (e.key === 'Enter') {
              handleSave();
            }
            if (e.key === 'Escape') {
              setDisplayName(initialDisplayName);
              setIsEditing(false);
            }
          }}
          style={{
            fontSize: rem(16),
            fontWeight: 500,
            border: 'none',
            outline: 'none',
            backgroundColor: 'transparent',
            width: '100%',
            padding: 0,
            margin: 0,
            color: 'inherit',
          }}
        />
      ) : (
        <Text fw={500} size='lg' onClick={handleEdit} sx={{ cursor: 'pointer' }}>
          {displayName}
        </Text>
      )}

      {!isEditing && (
        <ActionIcon variant='subtle' color='gray' size='sm' ml={4} onClick={handleEdit}>
          <IconPencil size={16} />
        </ActionIcon>
      )}
    </Flex>
  );
}

export default NodeName;
