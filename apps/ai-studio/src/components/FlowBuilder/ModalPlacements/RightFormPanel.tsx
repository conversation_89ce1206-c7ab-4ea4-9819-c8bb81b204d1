import { ActionIcon, Box, Group, Paper, Transition, rem, useMantineTheme } from '@mantine/core';
import { IconX } from '@tabler/icons-react';
import { SchemaEngineWithTolgee, SchemaFormInput } from '@resola-ai/ui/components/SchemaEngine';
import { useFlowBuilderContext } from '@/contexts/FlowBuilderContext';
import { useCallback, useLayoutEffect, useMemo, useRef, useState } from 'react';
import useFlowSchema from '@/hooks/useFlowSchema';
import { CredentialAPI } from '@/services/api/credential';
import { useParams } from 'react-router-dom';
import { ICredentialPayload } from '@resola-ai/ui/components/Credential/type';
import CatalogModal, { CatalogModalRef } from './CatalogModal';
import { FlowNodeType } from '@/models/flow';
import set from 'lodash/set';
import get from 'lodash/get';
import { useListCredential } from '@/hooks/useListCredential';
import { FlowDiagram } from '@/helpers/flowDiagram';
import NodeName from './NodeName';

// Define the Panel Width
const PANEL_WIDTH = rem(400); // Adjust as needed
const PANEL_Z_INDEX = 150;

// --- Side Panel Component ---
interface SidePanelProps {
  opened: boolean;
  onClose: () => void;
}

const getUpdatePath = (fieldName: string) => {
  let updatedPath = '';

  switch (fieldName) {
    case 'action': {
      updatedPath = 'action';
      break;
    }
    case 'trigger': {
      updatedPath = 'trigger';
      break;
    }
    case 'credential': {
      updatedPath = 'settings.credential.id';
      break;
    }
    case 'completedFormStep': {
      updatedPath = 'settings.ui.step';
      break;
    }
    default: {
      updatedPath = `settings.${fieldName}`;
    }
  }
  return updatedPath;
};

function RightFormPanel({ opened, onClose }: SidePanelProps) {
  const [activeStep, setActiveStep] = useState<number>(0);
  const {
    nodes,
    flow,
    edges,
    closeCatalog,
    flowActionHandlers,
    currentSelectNodeId,
    handleUpdateDisplayName,
    handleUpdateVirtualNodeByPath,
  } = useFlowBuilderContext();

  const previousNodes = useMemo(() => {
    if (!currentSelectNodeId || !nodes || !edges) {
      return [];
    }

    let previousNodes = FlowDiagram.getPreviousNodes(currentSelectNodeId, nodes, edges);
    const ignoreNodes = [
      FlowNodeType.AddNode,
      FlowNodeType.EmptyNode,
      FlowNodeType.SubPathNode,
      FlowNodeType.AddPathNode,
      FlowNodeType.LoopingFrame,
    ];
    previousNodes = previousNodes.filter(node => !ignoreNodes.includes(node.type as FlowNodeType));

    return previousNodes
      .sort((a, b) => (a.data?.orderedNumber as number) - (b.data?.orderedNumber as number))
      .map((node: any) => {
        const orderNumber = node.data?.orderedNumber ? `${node.data?.orderedNumber}. ` : '';
        const nodeName = node.data?.displayName || node.type || '';
        return {
          label: `${orderNumber}${nodeName}`,
          icon: node.data?.icon ?? '',
          options: [1, 2, 3, 4, 5].map(item => ({
            value: `${node.id}-${item}`,
            label: `${orderNumber}${nodeName}${item}`,
            icon: node.data?.icon ?? '',
          })),
        };
      });
  }, [currentSelectNodeId, nodes, edges]);

  const { workspaceId } = useParams();
  const catalogModalRef = useRef<CatalogModalRef>(null);
  const {
    data,
    isLoading: isCredentialsLoading,
    mutate: mutateCredentials,
  } = useListCredential({
    workspaceId,
  });
  const credentials = data?.data ?? [];

  const currentNode = useMemo(() => {
    if (!currentSelectNodeId) return null;
    return nodes?.find(node => node.id === currentSelectNodeId) ?? null;
  }, [nodes, currentSelectNodeId]);

  const { schema, isNodeTrigger } = useFlowSchema({ flowNodeType: currentNode?.type });
  const objectType = useMemo(() => {
    return schema?.triggers && Object.keys(schema.triggers).length > 0 ? 'trigger' : 'action';
  }, [schema?.triggers]);

  const currentNodeData = useMemo(() => {
    if (!currentNode) return {};
    const isVirtualNode = currentNode.type === FlowNodeType.SubPathNode;
    const itemObject = isNodeTrigger ? flow?.triggers : flow?.nodes;

    if (isVirtualNode) {
      const parentNodeId = get(currentNode, 'data.actualParentNodeId', '') as string;
      const parentNode = get(itemObject, parentNodeId);
      const paths = get(parentNode, 'settings.paths', []);
      const pathData = paths.find(path => path.next === currentNode.data.nextNodeId);
      return pathData;
    } else {
      const node = get(itemObject, currentNode.id);
      return node;
    }
  }, [currentNode, flow, isNodeTrigger]);

  const formValues = useMemo(() => {
    if (!currentNodeData) return {};

    const formData = {};

    const action = get(currentNodeData, 'action');
    const trigger = get(currentNodeData, 'trigger');
    const settings = get(currentNodeData, 'settings', {});
    const credentialId = get(settings, 'credential.id');

    set(formData, 'settings', settings);
    if (credentialId) {
      set(formData, 'settings.credential', credentialId);
    }
    if (action) {
      set(formData, 'action', action);
    }
    if (trigger) {
      set(formData, 'trigger', trigger);
    }
    return formData;
  }, [currentNodeData]) as SchemaFormInput;

  const completedFormStep = useMemo(() => {
    if (!currentNodeData) return -1;
    return get(currentNodeData, 'settings.ui.step', -1);
  }, [currentNodeData]);

  const theme = useMantineTheme();

  const createCredential = useCallback(
    async (credential: ICredentialPayload) => {
      if (!workspaceId) {
        return null;
      }

      try {
        const response = await CredentialAPI.create(workspaceId, credential);
        mutateCredentials();
        return response;
      } catch (error) {
        console.error(error);
        return null;
      }
    },
    [workspaceId, mutateCredentials]
  );

  const handleOpenAppCatalog = useCallback(() => {
    catalogModalRef.current?.openWithDisabledOptions();
  }, []);

  const handleOnSelectCatalog = useCallback(
    (item: { id: string; name: string; icon: string }) => {
      if (!currentSelectNodeId) return;

      setActiveStep(0);
      flowActionHandlers.handleUpdateNode(currentSelectNodeId, {
        name: item.name as FlowNodeType,
      });
    },
    [currentSelectNodeId, flowActionHandlers.handleUpdateNode]
  );

  const orderedNumber = useMemo(() => {
    const node = nodes?.find(node => node.id === currentSelectNodeId);
    return Number(node?.data?.orderedNumber ?? 0);
  }, [nodes, currentSelectNodeId]);

  const handleFormChange = useCallback(
    (formValues: SchemaFormInput, fieldName: string) => {
      if (!currentSelectNodeId) return;

      const value = formValues[fieldName];
      const updatedPath = getUpdatePath(fieldName);
      const isVirtualNode = currentNode?.type === FlowNodeType.SubPathNode;

      if (isVirtualNode) {
        handleUpdateVirtualNodeByPath(currentSelectNodeId, updatedPath, value);
        return;
      }
      if (FlowDiagram.shouldUpdateNodeName(currentNodeData, schema)) {
        const schemaField = `${fieldName}s`;
        const paths = [{ path: updatedPath, value }];
        const displayName = schema[schemaField]?.[value]?.displayName;
        ['action', 'trigger'].includes(fieldName) &&
          paths.push({ path: 'displayName', value: displayName });
        if (isNodeTrigger) {
          flowActionHandlers.handleUpdateTriggerByPaths(currentSelectNodeId, paths);
        } else {
          flowActionHandlers.handleUpdateNodeByPaths(currentSelectNodeId, paths);
        }
      }
    },
    [
      schema,
      objectType,
      currentNode?.type,
      currentSelectNodeId,
      handleUpdateVirtualNodeByPath,
      flowActionHandlers.handleUpdateNodeByPaths,
      flowActionHandlers.handleUpdateTriggerByPaths,
    ]
  );

  useLayoutEffect(() => {
    setActiveStep(0);
  }, [currentSelectNodeId]);

  const handleSaveDisplayName = useCallback(
    (displayName: string) => {
      if (!currentSelectNodeId) return;
      handleUpdateDisplayName(currentSelectNodeId, displayName);
    },
    [currentSelectNodeId, handleUpdateDisplayName]
  );

  const getDisplayName = useCallback(() => {
    if (currentNode?.data?.displayName) {
      return currentNode?.data?.displayName;
    }
    return schema?.displayName;
  }, [currentNode?.data?.displayName, schema?.displayName]);

  return (
    <>
      <Transition mounted={opened} transition='slide-left' duration={250} timingFunction='ease'>
        {styles => (
          <Paper
            shadow='lg' // Add shadow for separation
            radius={'md'} // No radius for edge-to-edge panel
            style={{
              ...styles, // Apply transition styles
              top: `calc(var(--app-shell-header-height) + ${rem(56)})`,
              right: rem(10),
              bottom: rem(10),
              zIndex: PANEL_Z_INDEX,
              display: 'flex',
              position: 'fixed',
              width: PANEL_WIDTH,
              flexDirection: 'column',
              borderLeft: `1px solid ${theme.colors.gray[3]}`,
            }}>
            {/* Panel Header */}
            <Group
              justify='space-between'
              p='md'
              style={{
                borderBottom: `1px solid ${theme.colors.gray[3]}`,
              }}>
              <Group gap='xs' flex={1}>
                <ActionIcon size='md' variant='transparent'>
                  {schema?.icon}
                </ActionIcon>
                <NodeName
                  orderedNumber={orderedNumber}
                  displayName={getDisplayName() ?? ''}
                  onSave={handleSaveDisplayName}
                />
              </Group>
              <ActionIcon variant='subtle' color='gray' onClick={onClose} aria-label='Close panel'>
                <IconX size={20} />
              </ActionIcon>
            </Group>

            <Box p='md' sx={{ flexGrow: 1, overflow: 'hidden' }}>
              <SchemaEngineWithTolgee
                schema={schema}
                form={formValues}
                activeStep={activeStep}
                key={currentSelectNodeId}
                credentials={credentials}
                previousNodes={previousNodes}
                completedStep={completedFormStep}
                isCredentialsLoading={isCredentialsLoading}
                createCredential={createCredential}
                onOpenAppCatalog={handleOpenAppCatalog}
                onFormChange={handleFormChange}
                onStepChange={setActiveStep}
                onClose={onClose}
              />
            </Box>
          </Paper>
        )}
      </Transition>

      <CatalogModal ref={catalogModalRef} onClose={closeCatalog} onSelect={handleOnSelectCatalog} />
    </>
  );
}

export default RightFormPanel;
