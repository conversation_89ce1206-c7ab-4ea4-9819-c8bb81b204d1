import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderWithMantine } from '@/utils/test';
import NodeName from './NodeName';

describe('NodeName', () => {
  const mockOnSave = vi.fn();
  const defaultProps = {
    orderedNumber: 1,
    displayName: 'Test Node',
    onSave: mockOnSave,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly with ordered number and display name', () => {
    renderWithMantine(<NodeName {...defaultProps} />);

    expect(screen.getByText('1. ')).toBeInTheDocument();
    expect(screen.getByText('Test Node')).toBeInTheDocument();
  });

  it('renders correctly without ordered number', () => {
    renderWithMantine(<NodeName {...defaultProps} orderedNumber={0} />);

    expect(screen.getByText('Test Node')).toBeInTheDocument();
    expect(screen.queryByText('0.')).not.toBeInTheDocument();
  });

  it('enters edit mode when clicked', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    await waitFor(() => {
      expect(screen.getByDisplayValue('Test Node')).toBeInTheDocument();
    });
  });

  it('saves changes when Enter key is pressed', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    const input = await screen.findByDisplayValue('Test Node');
    await user.clear(input);
    await user.type(input, 'Updated Node Name');
    await user.keyboard('{Enter}');

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith('Updated Node Name');
    });
  });

  it('saves changes when input loses focus', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    const input = await screen.findByDisplayValue('Test Node');
    await user.clear(input);
    await user.type(input, 'Updated Node Name');

    // Trigger blur by clicking outside
    await user.click(document.body);

    await waitFor(() => {
      expect(mockOnSave).toHaveBeenCalledWith('Updated Node Name');
    });
  });

  it('cancels edit when Escape key is pressed', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    const input = await screen.findByDisplayValue('Test Node');
    await user.clear(input);
    await user.type(input, 'Updated Node Name');
    await user.keyboard('{Escape}');

    await waitFor(() => {
      expect(screen.getByText('Test Node')).toBeInTheDocument();
    });

    expect(mockOnSave).not.toHaveBeenCalled();
  });

  it('updates display name when prop changes', () => {
    const { rerender } = renderWithMantine(<NodeName {...defaultProps} />);

    expect(screen.getByText('1. ')).toBeInTheDocument();
    expect(screen.getByText('Test Node')).toBeInTheDocument();

    rerender(<NodeName {...defaultProps} displayName='New Node Name' />);

    expect(screen.getByText('1. ')).toBeInTheDocument();
    expect(screen.getByText('New Node Name')).toBeInTheDocument();
  });

  it('handles empty display name', () => {
    renderWithMantine(<NodeName {...defaultProps} displayName='' />);

    expect(screen.getByText('1. ')).toBeInTheDocument();
    // Empty display name should render as empty text
    const container = screen.getByText('1. ').parentElement;
    expect(container).toBeInTheDocument();
  });

  it('handles special characters in display name', () => {
    const specialName = 'Node with @#$%^&*() characters';
    renderWithMantine(<NodeName {...defaultProps} displayName={specialName} />);

    expect(screen.getByText('1. ')).toBeInTheDocument();
    expect(screen.getByText(specialName)).toBeInTheDocument();
  });

  it('focuses and selects input text when entering edit mode', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    const input = await screen.findByDisplayValue('Test Node');

    // Wait for the setTimeout in the component to complete
    await waitFor(() => {
      expect(input).toBeInTheDocument();
    });
  });

  it('handles rapid edit mode toggling', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');

    // Click multiple times rapidly
    await user.click(textElement);
    await user.click(textElement);

    // Should still be in edit mode
    await waitFor(() => {
      expect(screen.getByDisplayValue('Test Node')).toBeInTheDocument();
    });
  });

  it('preserves original value when canceling edit', async () => {
    const user = userEvent.setup();
    renderWithMantine(<NodeName {...defaultProps} />);

    const textElement = screen.getByText('Test Node');
    await user.click(textElement);

    const input = await screen.findByDisplayValue('Test Node');
    await user.clear(input);
    await user.type(input, 'Temporary Change');
    await user.keyboard('{Escape}');

    await waitFor(() => {
      expect(screen.getByText('Test Node')).toBeInTheDocument();
    });

    // Click again to edit and verify original value is restored
    await user.click(screen.getByText('Test Node'));

    await waitFor(() => {
      expect(screen.getByDisplayValue('Test Node')).toBeInTheDocument();
    });
  });
});
