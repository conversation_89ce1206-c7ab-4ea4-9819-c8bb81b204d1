import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';

// Define the components we're testing
const RightFormPanel = vi.fn();
// const TriggerEventSelect = vi.fn();

// Custom render function
const customRender = render;

// Create a mock implementation of the RightFormPanel component
const MockRightFormPanel = ({ opened, onClose }: { opened: boolean; onClose: () => void }) => {
  if (!opened) return null;

  return (
    <div data-testid='right-form-panel'>
      <h1>1. Slack</h1>
      <div>Setup</div>
      <div>Configure</div>
      <div>Test</div>
      <div>App</div>
      <div>Slack</div>
      <div>Change</div>
      <div>Credential</div>
      <div><EMAIL></div>
      <div>Change</div>
      <div>I would like to use my own App Credentials</div>
      <div>Trigger Event</div>
      <div data-testid='trigger-event-select'>Trigger Event Select</div>
      <button aria-label='Close panel' onClick={onClose}>
        <div data-testid='icon-x'>X Icon</div>
      </button>
      <div data-testid='icon-circle-check'>Circle Check Icon</div>
      <div data-testid='icon-circle-dot'>Circle Dot Icon</div>
      <div data-testid='icon-circle'>Circle Icon</div>
    </div>
  );
};

// Create a mock implementation of the TriggerEventSelect component
const MockTriggerEventSelect = () => {
  const [selected, setSelected] = React.useState<string | null>(null);
  const [search, setSearch] = React.useState('');

  // Filter the event options based on search
  const filteredEvents = [
    {
      value: 'new_channel',
      title: 'New Channel',
      description: 'Trigger when a new #channel is created',
    },
    {
      value: 'new_message_posted',
      title: 'New Message Posted to Channel',
      description: 'Triggers when a new message is posted to a specific #channel you choose.',
    },
  ].filter(event => event.title.toLowerCase().includes(search.toLowerCase()));

  return (
    <div data-testid='trigger-event-select'>
      <button data-testid='trigger-event-select-input' onClick={() => {}}>
        {selected ?? 'Select an event'}
      </button>

      <div data-testid='popover-dropdown'>
        <input
          data-testid='trigger-event-select-input-search'
          value={search}
          onChange={e => setSearch(e.target.value)}
          placeholder='Search...'
        />

        <div data-testid='scroll-area'>
          {filteredEvents.length > 0 ? (
            filteredEvents.map((event, index) => (
              <button
                key={index}
                data-testid='trigger-event-select-item'
                onClick={() => {
                  setSelected(event.title);
                }}
                style={{ background: 'transparent' }}
                onMouseEnter={e => (e.currentTarget.style.background = 'rgb(241, 243, 245)')}
                onMouseLeave={e => (e.currentTarget.style.background = 'transparent')}
              >
                {event.title}
                <div>{event.description}</div>
              </button>
            ))
          ) : (
            <div data-testid='trigger-event-select-no-results'>No results found.</div>
          )}
        </div>
      </div>
    </div>
  );
};

vi.mock('./RightFormPanel', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(props => <MockRightFormPanel {...props} />),
  TriggerEventSelect: vi.fn().mockImplementation(() => <MockTriggerEventSelect />),
}));

// Override the mock implementation for the tests
RightFormPanel.mockImplementation(props => <MockRightFormPanel {...props} />);

describe('RightFormPanel', () => {
  const onCloseMock = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders correctly when opened', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Check for key elements
    expect(screen.getByText('1. Slack')).toBeInTheDocument();
    expect(screen.getByText('Setup')).toBeInTheDocument();
    expect(screen.getByText('Configure')).toBeInTheDocument();
    expect(screen.getByText('Test')).toBeInTheDocument();
    expect(screen.getByText('App')).toBeInTheDocument();
    expect(screen.getByText('Credential')).toBeInTheDocument();
    expect(screen.getByText('Trigger Event')).toBeInTheDocument();
  });

  it('does not render when closed', () => {
    customRender(<RightFormPanel opened={false} onClose={onCloseMock} />);

    // The component should not be rendered when opened is false
    expect(screen.queryByText('1. Slack')).not.toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Find and click the close button (X icon)
    const closeButton = screen.getByLabelText('Close panel');
    fireEvent.click(closeButton);

    expect(onCloseMock).toHaveBeenCalledTimes(1);
  });

  it('renders the stepper component with correct steps', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Check for stepper component and its steps
    expect(screen.getByText('Setup')).toBeInTheDocument();
    expect(screen.getByText('Configure')).toBeInTheDocument();
    expect(screen.getByText('Test')).toBeInTheDocument();

    // Check for the icons in the stepper
    expect(screen.getByTestId('icon-circle-check')).toBeInTheDocument();
    expect(screen.getByTestId('icon-circle-dot')).toBeInTheDocument();
    expect(screen.getByTestId('icon-circle')).toBeInTheDocument();
  });

  it('renders the app section with correct content', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Check for app section content
    expect(screen.getByText('App')).toBeInTheDocument();
    expect(screen.getByText('Slack')).toBeInTheDocument();
    expect(screen.getAllByText('Change')[0]).toBeInTheDocument();
  });

  it('renders the credential section with correct content', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Check for credential section content
    expect(screen.getByText('Credential')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getAllByText('Change')[1]).toBeInTheDocument();
    expect(screen.getByText('I would like to use my own App Credentials')).toBeInTheDocument();
  });

  it('renders the trigger event section with TriggerEventSelect component', () => {
    customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

    // Check for trigger event section
    expect(screen.getByText('Trigger Event')).toBeInTheDocument();
    expect(screen.getByTestId('trigger-event-select')).toBeInTheDocument();
  });

  // Tests for schedule node functionality (TK-8980)
  describe('Schedule Node Functionality', () => {
    it('should handle schedule node configuration', () => {
      customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

      // The panel should render correctly for schedule nodes
      expect(screen.getByTestId('right-form-panel')).toBeInTheDocument();
      expect(screen.getByText('Setup')).toBeInTheDocument();
      expect(screen.getByText('Configure')).toBeInTheDocument();
      expect(screen.getByText('Test')).toBeInTheDocument();
    });

    it('should display schedule-specific configuration options', () => {
      customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

      // For schedule nodes, the panel should show relevant configuration
      expect(screen.getByText('App')).toBeInTheDocument();
      expect(screen.getByText('Trigger Event')).toBeInTheDocument();
    });

    it('should handle schedule node form updates', () => {
      customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

      // The form should be interactive for schedule nodes
      const closeButton = screen.getByLabelText('Close panel');
      expect(closeButton).toBeInTheDocument();

      fireEvent.click(closeButton);
      expect(onCloseMock).toHaveBeenCalled();
    });

    it('should render schedule node with proper stepper icons', () => {
      customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

      // Schedule nodes should show the stepper with proper icons
      expect(screen.getByTestId('icon-circle-check')).toBeInTheDocument();
      expect(screen.getByTestId('icon-circle-dot')).toBeInTheDocument();
      expect(screen.getByTestId('icon-circle')).toBeInTheDocument();
    });

    it('should handle schedule node field updates correctly', () => {
      customRender(<RightFormPanel opened={true} onClose={onCloseMock} />);

      // Schedule nodes should support field updates through the form
      expect(screen.getByText('Credential')).toBeInTheDocument();
      expect(screen.getByText('Trigger Event')).toBeInTheDocument();

      // The form should be properly structured for schedule node configuration
      expect(screen.getByTestId('trigger-event-select')).toBeInTheDocument();
    });
  });
});
