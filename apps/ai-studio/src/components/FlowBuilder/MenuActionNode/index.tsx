import { ActionIcon, Menu } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconDots, IconPencil, IconTrash } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useState, useCallback, BaseSyntheticEvent } from 'react';

const useStyles = createStyles(theme => ({
  customDotsIcon: {
    borderRadius: 999,
    '&:hover': {
      backgroundColor: theme.colors.decaLight[1],
    },
  },
}));

const MenuActionNode = ({
  onEdit,
  onDelete,
  dataTestId = 'action-menu-node-container',
}: {
  onEdit: () => void;
  onDelete: () => void;
  dataTestId?: string;
}) => {
  const { classes } = useStyles();
  const { t } = useTranslate('flow');
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [menuOpened, setMenuOpened] = useState(false);

  const handleDelete = useCallback(
    (e: BaseSyntheticEvent) => {
      // Prevent the menu from closing on the first click
      if (!confirmDelete) {
        e.preventDefault();
        e.stopPropagation();
        setConfirmDelete(true);
        return false; // Try to prevent default behavior
      } else {
        e.preventDefault();
        e.stopPropagation();
        onDelete();
        setConfirmDelete(false);
      }
    },
    [confirmDelete, onDelete]
  );

  const handleEdit = useCallback(
    (e: BaseSyntheticEvent) => {
      e.preventDefault();
      e.stopPropagation();
      onEdit();
    },
    [onEdit]
  );

  const handleMenuClose = useCallback(() => {
    setMenuOpened(false);
    setConfirmDelete(false);
  }, []);

  const handleMenuTargetClick = useCallback((e: BaseSyntheticEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  return (
    <Menu
      width={250}
      withinPortal
      opened={menuOpened}
      closeOnEscape={true}
      position='bottom-start'
      data-testid={dataTestId}
      onChange={setMenuOpened}
      onClose={handleMenuClose}
      closeOnClickOutside={true}
      closeOnItemClick={confirmDelete}
    >
      <Menu.Target>
        <ActionIcon
          variant='subtle'
          className={classes.customDotsIcon}
          data-testid='trigger-menu-button'
          onClick={handleMenuTargetClick}
          size={20}
          color='decaGrey.4'
        >
          <IconDots size={16} />
        </ActionIcon>
      </Menu.Target>
      <Menu.Dropdown>
        <Menu.Item
          onClick={handleEdit}
          leftSection={<IconPencil size={14} />}
          data-testid='trigger-edit-option'
        >
          {t('edit')}
        </Menu.Item>
        <Menu.Item
          onClick={handleDelete}
          leftSection={<IconTrash size={14} />}
          color='var(--mantine-color-decaRed-5)'
          data-testid='trigger-delete-option'
          closeMenuOnClick={confirmDelete}
        >
          {confirmDelete ? t('reallyDelete') : t('delete')}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default MenuActionNode;
