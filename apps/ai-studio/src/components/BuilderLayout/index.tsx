import { showSuccessNotification } from '@/utils/notification';
import { ActionIcon, Box, Flex, rem, Text, TextInput, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import {
  IconArrowLeft,
  IconBookUpload,
  IconCheck,
  IconPencil,
  IconPlayerPlay,
  IconTemplate,
  IconX,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { useEffect, useState } from 'react';
import ActionsMenu, { ActionsMenuProps } from '../ActionsMenu';
import BuilderActionIcon from './BuilderActionIcon';

const useStyles = createStyles(theme => ({
  container: {
    backgroundColor: 'white',
    display: 'flex',
    flexDirection: 'column',
    position: 'relative',
    height: '100%',
  },
  header: {
    top: 0,
    height: rem(46),
    padding: `0 ${rem(20)}`,
    background: `linear-gradient(to right, ${theme.colors.decaViolet[0]}, ${theme.colors.decaBlue[0]}, ${theme.colors.decaBlue[0]})`,
  },
  main: {
    flex: 1,
    height: '100%',
  },
}));

type Props = {
  children: React.ReactNode;
  onBack: () => void;
  title: string;
  onTitleChange: (value: string) => void;
  onPublish: () => void;
  onRun?: () => void;
  openTemplate?: () => void;
  contentClassName?: string;
  versionControl?: React.ReactNode;
  historyRuns?: React.ReactNode;
  seeMoreActions?: ActionsMenuProps;
  variablesControl?: React.ReactNode;
};

const BuilderLayout = ({
  children,
  title: initialTitle,
  onBack,
  onTitleChange,
  onPublish,
  onRun,
  openTemplate,
  contentClassName,
  versionControl,
  historyRuns,
  seeMoreActions,
  variablesControl,
}: Props) => {
  const { t } = useTranslate('common');
  const { classes, cx } = useStyles();

  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState(initialTitle);

  useEffect(() => {
    setTitle(initialTitle);
  }, [initialTitle]);

  const handleNameChange = () => {
    if (title) {
      setIsEditing(false);
      onTitleChange(title);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleNameChange();
    }
    if (e.key === 'Escape') {
      setTitle(initialTitle);
      setIsEditing(false);
    }
  };

  return (
    <Box className={classes.container} data-testid='builder-layout'>
      <Flex className={classes.header} justify='space-between' align='center'>
        <Flex maw={'50%'} align='center' gap={rem(8)} style={{ cursor: 'pointer' }}>
          <ActionIcon
            onClick={onBack}
            variant='transparent'
            aria-label='go-back'
            data-testid='header-back-button'
          >
            <IconArrowLeft size={24} />
          </ActionIcon>

          {isEditing ? (
            <>
              <TextInput
                maxLength={80}
                w={rem(400)}
                autoFocus
                value={title}
                onChange={e => setTitle(e.target.value)}
                onKeyDown={handleKeyDown}
                data-testid='title-name-input'
              />
              <Tooltip label={t('builderLayout.apply')} position='top' withArrow openDelay={500}>
                <ActionIcon
                  aria-label='title-name-edit-button'
                  c={'decaGrey.9'}
                  variant='subtle'
                  onClick={handleNameChange}
                >
                  <IconCheck size={24} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label={t('builderLayout.cancel')} position='top' withArrow openDelay={500}>
                <ActionIcon
                  aria-label='title-name-edit-button'
                  c={'decaGrey.9'}
                  variant='subtle'
                  onClick={() => {
                    setIsEditing(false);
                    setTitle(initialTitle);
                  }}
                >
                  <IconX size={24} />
                </ActionIcon>
              </Tooltip>
            </>
          ) : (
            <Flex align='center' gap={rem(8)} maw={rem(500)}>
              <Text size='xl' fw={600} lineClamp={1} onClick={() => setIsEditing(true)}>
                {title}
              </Text>
              <ActionIcon
                aria-label='title-name-edit-button'
                c={'decaGrey.9'}
                variant='transparent'
                onClick={() => setIsEditing(!isEditing)}
              >
                <IconPencil size={22} />
              </ActionIcon>
            </Flex>
          )}
        </Flex>

        <Flex gap={rem(12)} align='center'>
          {openTemplate && (
            <BuilderActionIcon onClick={openTemplate}>
              <IconTemplate size={16} />
            </BuilderActionIcon>
          )}
          {versionControl}
          {historyRuns}
          {variablesControl}
          {onRun && (
            <DecaButton
              variant='secondary'
              leftSection={<IconPlayerPlay size={16} />}
              size='sm'
              onClick={onRun}
              data-testid='builder-layout-run-button'
            >
              {t('builderLayout.run')}
            </DecaButton>
          )}
          <DecaButton
            disabled={!title?.trim()}
            leftSection={<IconBookUpload size={16} />}
            size='sm'
            onClick={async () => {
              await onPublish();
              showSuccessNotification(t('builderLayout.published'));
            }}
            data-testid='builder-layout-publish-button'
          >
            {t('builderLayout.publish')}
          </DecaButton>
          {seeMoreActions && (
            <BuilderActionIcon>
              <ActionsMenu {...seeMoreActions} />
            </BuilderActionIcon>
          )}
        </Flex>
      </Flex>

      <Box className={cx(classes.main, contentClassName)} data-testid='builder-layout-content'>
        {children}
      </Box>
    </Box>
  );
};

export default BuilderLayout;
