import { VersionContextProvider, useVersionContext } from '@/contexts/VersionContext';
import { ResourceType } from '@/types';
import VersionControl from './VersionControl';
import { showSuccessNotification } from '@/utils/notification';
import { useTranslate } from '@tolgee/react';

interface VersionControlWrapperProps {
  resourceId: string;
  resourceType: ResourceType;
  snapshot?: Record<string, any>;
  onRestoreVersion?: (versionId: string) => void;
}

export const InnerVersionControl = ({
  resourceId,
  snapshot,
  onRestoreVersion,
}: VersionControlWrapperProps) => {
  const { versions, addVersion, restoreVersion, updateVersion, loadMore, setSearchValue } =
    useVersionContext();
  const { t } = useTranslate('common');

  if (!resourceId) return null;

  const handleAdd = async ({ name, description }: { name: string; description?: string }) => {
    await addVersion({ name, description, resourceId, snapshot: snapshot || {} });
  };

  const handleRestore = async (versionId: string, saveBeforeRestore?: boolean) => {
    if (saveBeforeRestore && snapshot) {
      await handleAdd({
        name: snapshot?.name,
        description: snapshot?.description,
      });
    }
    await restoreVersion(versionId);
    await onRestoreVersion?.(versionId);
    showSuccessNotification(
      t(
        saveBeforeRestore
          ? 'versionControl.saveAndRestoreSuccess'
          : 'versionControl.restoreSuccess',
        {
          name: snapshot?.name,
        }
      )
    );
  };

  return (
    <VersionControl
      data={versions?.data || []}
      onAdd={handleAdd}
      onRestore={handleRestore}
      onEdit={updateVersion}
      onLoadMore={loadMore}
      onSearch={setSearchValue}
    />
  );
};
// If you're not using VersionControlWrapper,
// make sure to wrap <VersionContextProvider> outside your component manually.
const VersionControlWrapper = ({
  resourceId,
  resourceType,
  snapshot,
}: VersionControlWrapperProps) => {
  if (!resourceId) return null;

  return (
    <VersionContextProvider resourceType={resourceType} resourceId={resourceId}>
      <InnerVersionControl
        resourceId={resourceId}
        snapshot={snapshot}
        resourceType={resourceType}
      />
    </VersionContextProvider>
  );
};

export default VersionControlWrapper;
