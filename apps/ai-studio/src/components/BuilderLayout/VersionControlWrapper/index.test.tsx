import * as VersionContext from '@/contexts/VersionContext';
import { IStudioSuccessListResponse, IVersion } from '@/models';
import { ResourceType } from '@/types';
import { renderWithMantine } from '@/utils/test';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { KeyedMutator } from 'swr';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import VersionControlWrapper, { InnerVersionControl } from './index';

// Mock the version context
vi.mock('@/contexts/VersionContext', () => ({
  VersionContextProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
  useVersionContext: vi.fn(),
}));

// Mock the notification utility
vi.mock('@/utils/notification', () => ({
  showSuccessNotification: vi.fn(),
}));

// Mock the translation hook
vi.mock('@tolgee/react', () => ({
  useTranslate: () => ({
    t: (key: string) => {
      const translations: Record<string, string> = {
        'versionControl.saveAndRestoreSuccess': 'Version saved and restored successfully',
        'versionControl.restoreSuccess': 'Version restored successfully',
      };
      return translations[key] || key;
    },
  }),
}));

describe('VersionControlWrapper', () => {
  const mockVersionContextValue = {
    versions: {
      data: [
        {
          id: '1',
          name: 'Test Version',
          description: 'Test Description',
          updatedAt: '2021-01-01',
          version: '1.0.0',
          workspaceId: '1',
          resourceType: 'prompt' as ResourceType,
        },
      ],
    } as IStudioSuccessListResponse<IVersion>,
    mutateVersions: vi.fn() as KeyedMutator<IStudioSuccessListResponse<IVersion>>,
    addVersion: vi.fn(),
    restoreVersion: vi.fn(),
    updateVersion: vi.fn(),
    loadMore: vi.fn(),
    setSearchValue: vi.fn(),
    setCursor: vi.fn(),
    setLimit: vi.fn(),
  };

  const defaultProps = {
    resourceId: '123',
    resourceType: 'prompt' as ResourceType,
  };

  beforeEach(() => {
    vi.mocked(VersionContext.useVersionContext).mockReturnValue(mockVersionContextValue);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders nothing when resourceId is not provided', () => {
    renderWithMantine(<VersionControlWrapper resourceId='' resourceType='prompt' />);
    expect(screen.queryByTestId('builder-action-popover')).not.toBeInTheDocument();
  });

  it('renders version control when resourceId is provided', () => {
    renderWithMantine(<VersionControlWrapper {...defaultProps} />);
    expect(screen.getByTestId('builder-action-popover')).toBeInTheDocument();
  });

  it('renders InnerVersionControl with snapshot and onRestoreVersion props', () => {
    const snapshot = { name: 'Snapshot Name', description: 'Snapshot Description' };
    const onRestoreVersion = vi.fn();

    renderWithMantine(
      <VersionControlWrapper
        {...defaultProps}
        snapshot={snapshot}
        onRestoreVersion={onRestoreVersion}
      />
    );

    expect(screen.getByTestId('builder-action-popover')).toBeInTheDocument();
  });
});

describe('InnerVersionControl', () => {
  const mockVersionContextValue = {
    versions: {
      data: [
        {
          id: '1',
          name: 'Test Version',
          description: 'Test Description',
          updatedAt: '2021-01-01',
          version: '1.0.0',
          workspaceId: '1',
          resourceType: 'prompt' as ResourceType,
          resourceId: '123',
        },
        {
          id: '2',
          name: 'Another Version',
          description: 'Another Description',
          updatedAt: '2021-01-02',
          version: '1.0.1',
          workspaceId: '1',
          resourceType: 'prompt' as ResourceType,
          resourceId: '123',
        },
      ],
    } as IStudioSuccessListResponse<IVersion>,
    mutateVersions: vi.fn() as KeyedMutator<IStudioSuccessListResponse<IVersion>>,
    addVersion: vi.fn(),
    restoreVersion: vi.fn(),
    updateVersion: vi.fn(),
    loadMore: vi.fn(),
    setSearchValue: vi.fn(),
    setCursor: vi.fn(),
    setLimit: vi.fn(),
  };

  const defaultProps = {
    resourceId: '123',
    resourceType: 'prompt' as ResourceType,
  };

  beforeEach(() => {
    vi.mocked(VersionContext.useVersionContext).mockReturnValue(mockVersionContextValue);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders nothing when resourceId is not provided', () => {
    renderWithMantine(<InnerVersionControl resourceId='' resourceType='prompt' />);
    expect(screen.queryByTestId('builder-action-popover')).not.toBeInTheDocument();
  });

  it('renders version control when resourceId is provided', () => {
    renderWithMantine(<InnerVersionControl {...defaultProps} />);
    expect(screen.getByTestId('builder-action-popover')).toBeInTheDocument();
  });

  it('handles restore version functionality', async () => {
    const restoreVersion = vi.fn().mockResolvedValue(undefined);
    const onRestoreVersion = vi.fn();

    vi.mocked(VersionContext.useVersionContext).mockReturnValue({
      ...mockVersionContextValue,
      restoreVersion,
    });

    renderWithMantine(
      <InnerVersionControl {...defaultProps} onRestoreVersion={onRestoreVersion} />
    );

    // Open the popover
    const popoverTrigger = screen.getByTestId('builder-action-popover');
    userEvent.click(popoverTrigger);

    // //wait for the popover to open
    await waitFor(() => {
      expect(screen.getByTestId('builder-action-popover-dropdown')).toBeInTheDocument();
      expect(screen.getAllByTestId('version-item')).toHaveLength(2);
      expect(screen.getAllByTestId('version-item-menu')).toHaveLength(2);
    });

    // click menu trigger
    const menuTrigger = screen.getAllByTestId('version-item-menu')[0];
    userEvent.click(menuTrigger);

    //wait for the menu to open
    await waitFor(() => {
      expect(screen.getByTestId('version-item-menu-dropdown')).toBeInTheDocument();
    });

    // Find and click restore button for the first version
    const restoreButtons = screen.getByTestId('version-item-menu-restore');
    userEvent.click(restoreButtons);

    await waitFor(() => {
      expect(screen.getByText('versionControl.restoreThisVersion')).toBeInTheDocument();
      expect(screen.getByText('versionControl.restore')).toBeInTheDocument();
    });

    //click restore button text buton restore this version
    const restoreButton = screen.getByRole('button', { name: 'versionControl.restore' });
    userEvent.click(restoreButton);

    await waitFor(() => {
      expect(restoreVersion).toHaveBeenCalledWith('1');
      expect(onRestoreVersion).toHaveBeenCalledWith('1');
    });
  });

  it('handles empty versions data', () => {
    vi.mocked(VersionContext.useVersionContext).mockReturnValue({
      ...mockVersionContextValue,
      versions: { data: [] },
    });

    renderWithMantine(<InnerVersionControl {...defaultProps} />);

    // Open the popover
    const popoverTrigger = screen.getByTestId('builder-action-popover');
    userEvent.click(popoverTrigger);

    // Should still render the popover but with no version items
    expect(screen.getByTestId('builder-action-popover')).toBeInTheDocument();
    expect(screen.queryByText('Test Version')).not.toBeInTheDocument();
  });

  it('handles undefined versions data', () => {
    vi.mocked(VersionContext.useVersionContext).mockReturnValue({
      ...mockVersionContextValue,
      versions: undefined,
    });

    renderWithMantine(<InnerVersionControl {...defaultProps} />);

    // Open the popover
    const popoverTrigger = screen.getByTestId('builder-action-popover');
    userEvent.click(popoverTrigger);

    // Should still render the popover but with no version items
    expect(screen.getByTestId('builder-action-popover')).toBeInTheDocument();
    expect(screen.queryByText('Test Version')).not.toBeInTheDocument();
  });
});
