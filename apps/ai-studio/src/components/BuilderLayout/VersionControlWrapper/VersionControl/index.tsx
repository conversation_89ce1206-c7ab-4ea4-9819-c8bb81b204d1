import { IVersion } from '@/models/version';
import { Box, rem, ScrollArea, Text, TextInput } from '@mantine/core';
import { IconSearch, IconVersions } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import ActionPopover from '../../ActionPopover';
import BuilderActionIcon from '../../BuilderActionIcon';
import VersionItem from './VersionItem';
import { useCallback, useState } from 'react';
import EditModal from '@/components/Modals/EditModal';
import { useDisclosure } from '@mantine/hooks';
import { EditModalData } from '@/types';
import { DecaButton, Modal } from '@resola-ai/ui';

type VersionControlProps = {
  data: IVersion[];
  onAdd?: ({ name, description }: { name: string; description?: string }) => void;
  onEdit?: (v: IVersion) => void;
  onRestore?: (versionId: string, saveBeforeRestore?: boolean) => void;
  onLoadMore?: () => void;
  onSearch?: (value: string) => void;
};

const VersionControl = ({
  data,
  onAdd,
  onEdit,
  onRestore,
  onLoadMore,
  onSearch,
}: VersionControlProps) => {
  const { t } = useTranslate('common');
  const [search, setSearch] = useState('');
  const [currentVersion, setCurrentVersion] = useState<IVersion | null>(null);
  const [openedEditModal, { open: openEditModal, close: closeEditModal }] = useDisclosure(false);
  const [isCreating, setIsCreating] = useState(false);
  const [restoreConfirmModal, { open: openRestoreConfirmModal, close: closeRestoreConfirmModal }] =
    useDisclosure(false);

  const onEditVersion = useCallback(
    (version: IVersion) => {
      setIsCreating(false);
      setCurrentVersion(version);
      openEditModal();
    },
    [openEditModal]
  );

  const handleRestore = useCallback(
    async (saveBeforeRestore?: boolean) => {
      if (currentVersion) {
        await onRestore?.(currentVersion.id, saveBeforeRestore);
      }
      closeRestoreConfirmModal();
    },
    [currentVersion, onRestore, closeRestoreConfirmModal]
  );

  const handleConfirm = useCallback(
    async (data: EditModalData) => {
      const { title, description } = data;
      if (isCreating) {
        await onAdd?.({ name: title, description });
      } else {
        currentVersion && (await onEdit?.({ ...currentVersion, name: title, description }));
      }
      closeEditModal();
      setCurrentVersion(null);
      setIsCreating(false);
    },
    [isCreating, currentVersion, onAdd, onEdit, closeEditModal]
  );

  return (
    <>
      <ActionPopover
        title={t('versionControl.title')}
        target={
          <BuilderActionIcon>
            <IconVersions size={16} />
          </BuilderActionIcon>
        }
        onAdd={() => {
          setIsCreating(true);
          openEditModal();
        }}
      >
        <TextInput
          data-testid='version-control-search'
          mt='md'
          placeholder={t('versionControl.searchVersion')}
          size='xs'
          mb='xs'
          leftSection={<IconSearch size={14} />}
          onChange={e => {
            setSearch(e.target.value);
            onSearch?.(e.target.value);
          }}
          value={search}
        />
        <ScrollArea h={`calc(100vh - ${rem(230)})`} type='never' onBottomReached={onLoadMore}>
          <Box>
            {data.map(version => (
              <VersionItem
                key={version.id}
                version={version}
                onEdit={onEditVersion}
                onRestore={() => {
                  setCurrentVersion(version);
                  openRestoreConfirmModal();
                }}
              />
            ))}
          </Box>
        </ScrollArea>
      </ActionPopover>
      <EditModal
        data-testid='version-control-modal'
        opened={openedEditModal}
        onCancel={closeEditModal}
        onConfirm={handleConfirm}
        title={isCreating ? t('versionControl.createVersion') : t('versionControl.editVersion')}
        options={{
          titleLabel: t('versionControl.name'),
          titlePlaceholder: t('versionControl.titlePlaceholder'),
          descriptionPlaceholder: t('versionControl.descriptionPlaceholder'),
        }}
        initialValues={
          isCreating
            ? {
                title: '',
                description: '',
              }
            : currentVersion
              ? {
                  title: currentVersion.name,
                  description: currentVersion.description,
                }
              : undefined
        }
      />
      <Modal
        zIndex={1000}
        centered
        opened={restoreConfirmModal}
        onClose={() => {
          closeRestoreConfirmModal();
          setCurrentVersion(null);
        }}
        title={t('versionControl.restoreThisVersion')}
        okText={t('versionControl.restore')}
        cancelText={t('versionControl.cancel')}
        onOk={() => handleRestore(false)}
        onCancel={closeRestoreConfirmModal}
        leftButton={
          <DecaButton variant='secondary' onClick={() => handleRestore(true)}>
            {t('versionControl.saveAndRestore')}
          </DecaButton>
        }
      >
        <Text>{t('versionControl.restoreVersionDescription')}</Text>
      </Modal>
    </>
  );
};

export default VersionControl;
