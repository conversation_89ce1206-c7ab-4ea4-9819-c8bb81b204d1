import { IVersion } from '@/models';
import { ActionIcon, Flex, Menu, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconClockHour3, IconDots, IconPencil, IconReload } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import dayjs from 'dayjs';
type Props = {
  version: IVersion;
  onEdit?: (v: IVersion) => void;
  onRestore?: (v: IVersion) => void;
};

const useStyles = createStyles(theme => ({
  versionItem: {
    borderRadius: theme.radius.sm,

    '&:hover': {
      backgroundColor: theme.colors.decaLight[0],
    },
  },
}));

const VersionItem = ({ version, onEdit, onRestore }: Props) => {
  const { t } = useTranslate('common');
  const { classes } = useStyles();
  return (
    <Flex
      p='xs'
      align='center'
      justify='space-between'
      className={classes.versionItem}
      data-testid='version-item'
    >
      <Flex align='center' gap={rem(8)}>
        <IconClockHour3 size={18} />
        <Flex direction='column' mt={2}>
          <Text size='sm' c='decaGrey.6' fw={600} fz={rem(14)}>
            {version.name}
          </Text>
          <Text size='xs' c='dimmed' fz={rem(12)}>
            {dayjs(version.updatedAt).format('YYYY/MM/DD HH:mm')}
          </Text>
        </Flex>
      </Flex>

      <Menu position='bottom-end' withinPortal data-testid='version-item-menu'>
        <Menu.Target>
          <ActionIcon
            variant='subtle'
            size='sm'
            c='decaGrey.6'
            data-testid='version-item-menu-trigger'
          >
            <IconDots size={20} />
          </ActionIcon>
        </Menu.Target>
        <Menu.Dropdown data-testid='version-item-menu-dropdown' w={rem(240)}>
          <Menu.Item
            c='decaGrey.6'
            h={rem(36)}
            onClick={() => onEdit?.(version)}
            data-testid='version-item-menu-edit'
            leftSection={<IconPencil size={20} />}
          >
            <Text fz={rem(14)} c='decaGrey.9'>
              {t('versionControl.rename')}
            </Text>
          </Menu.Item>
          <Menu.Item
            c='decaGrey.6'
            h={rem(36)}
            onClick={() => onRestore?.(version)}
            data-testid='version-item-menu-restore'
            leftSection={<IconReload size={20} />}
          >
            <Text fz={rem(14)} c='decaGrey.9'>
              {t('versionControl.restoreThisVersion')}
            </Text>
          </Menu.Item>
        </Menu.Dropdown>
      </Menu>
    </Flex>
  );
};

export default VersionItem;
