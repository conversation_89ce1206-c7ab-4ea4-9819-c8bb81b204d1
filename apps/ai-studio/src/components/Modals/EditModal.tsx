import { Flex, Group, ModalOverlayProps, Text, rem, Box, ModalProps } from '@mantine/core';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';
import { useForm } from 'react-hook-form';
import { TextInput, Textarea } from 'react-hook-form-mantine';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import Modal from './Modal';
import { useModalStyles } from './useModalStyles';
import React from 'react';
import { EditModalData } from '@/types';

const editFormSchema = (t: (key: string) => string) =>
  z.object({
    title: z.string().min(1, { message: t('common.error.required') }),
    description: z.string().optional(),
  });

export interface EditModalOptions {
  className?: string;
  modalSize?: string;
  zIndex?: number;
  overlayProps?: ModalOverlayProps;
  titleLabel?: string;
  titleMaxLength?: number;
  descriptionMaxLength?: number;
  descriptionLabel?: string;
  titlePlaceholder?: string;
  descriptionPlaceholder?: string;
  validate?: {
    title?: (value: string) => string | undefined;
    description?: (value: string) => string | undefined;
  };
  inputInline?: boolean;
  labelWidth?: number;
  closeOnClickOutside?: boolean;
}

export interface EditModalProps {
  opened: boolean;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: (value: EditModalData) => Promise<void>;
  onCancel: () => void;
  options?: EditModalOptions;
  initialValues?: EditModalData;
  ['data-testid']?: string;
}

const EditModal: React.FC<EditModalProps> = ({
  opened,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  options,
  initialValues,
  ...rest
}) => {
  const { t } = useTranslate('home');
  const { cx, classes } = useModalStyles({ size: 'lg' } as ModalProps);

  const {
    className,
    modalSize = 'lg',
    zIndex = 1000,
    overlayProps = {
      opacity: 0.5,
    },
    titleLabel,
    descriptionLabel,
    titlePlaceholder,
    descriptionPlaceholder,
    validate,
    closeOnClickOutside = true,
    titleMaxLength = 80,
    descriptionMaxLength = 200,
  } = options ?? {};

  const form = useForm<EditModalData>({
    resolver: zodResolver(editFormSchema(t)),
    defaultValues: {
      title: initialValues?.title ?? '',
      description: initialValues?.description ?? '',
    },
    mode: 'onChange',
  });

  React.useEffect(() => {
    if (opened) {
      setTimeout(() => {
        form.setFocus('title');
      }, 100);
    }
  }, [opened, form]);

  React.useEffect(() => {
    form.reset({
      title: initialValues?.title ?? '',
      description: initialValues?.description ?? '',
    });
  }, [initialValues, form]);

  const handleSubmit = form.handleSubmit(async data => {
    try {
      let hasErrors = false;

      if (validate?.title) {
        const titleError = validate.title(data.title);
        if (titleError) {
          form.setError('title', { message: titleError });
          hasErrors = true;
        }
      }

      if (validate?.description && data.description) {
        const descriptionError = validate.description(data.description);
        if (descriptionError) {
          form.setError('description', { message: descriptionError });
          hasErrors = true;
        }
      }

      if (hasErrors) {
        return;
      }

      await onConfirm(data);
    } catch (error) {
      if (error instanceof Error) {
        form.setError('root', { message: error.message });
      }
    }
  });

  const handleCancel = () => {
    form.reset();
    onCancel();
  };

  return (
    <Modal
      title={title}
      opened={opened}
      zIndex={zIndex}
      size={modalSize}
      onClose={handleCancel}
      overlayProps={overlayProps}
      closeOnClickOutside={closeOnClickOutside}
      className={cx(className, classes.editModal)}
      data-testid={rest?.['data-testid'] || 'edit-modal'}
    >
      <form onSubmit={handleSubmit}>
        <Flex direction='column' justify='center' gap={rem(16)}>
          {content && <Text className={classes.content}>{content}</Text>}
          {form.formState.errors.root && (
            <Text className={classes.error}>{form.formState.errors.root.message}</Text>
          )}
          <Flex direction={'column'} gap={rem(16)}>
            <Box className={classes.inputWrapper}>
              <Text className={classes.label}>
                {titleLabel ?? t('common.title')}
                <span className={classes.required}>*</span>
              </Text>
              <div>
                <TextInput
                  maxLength={titleMaxLength}
                  data-testid='title-input'
                  control={form.control}
                  name='title'
                  placeholder={titlePlaceholder}
                  radius='sm'
                  error={form.formState.errors.title?.message}
                />
              </div>
            </Box>
            <div className={classes.inputWrapper}>
              <Text className={classes.label}>{descriptionLabel ?? t('common.description')}</Text>
              <div>
                <Textarea
                  maxLength={descriptionMaxLength}
                  data-testid='description-input'
                  control={form.control}
                  name='description'
                  placeholder={descriptionPlaceholder}
                  radius='sm'
                  minRows={3}
                  autosize
                  maxRows={6}
                  error={form.formState.errors.description?.message}
                />
              </div>
            </div>
          </Flex>
        </Flex>
        <Group gap={rem(10)} justify='flex-end' mt={rem(30)}>
          <DecaButton
            data-testid='cancel-edit-modal'
            size='md'
            className={classes.button}
            variant='neutral'
            onClick={handleCancel}
            radius='sm'
          >
            {cancelText ?? t('common.button.cancel')}
          </DecaButton>
          <DecaButton
            data-testid='confirm-edit-modal'
            size='md'
            className={classes.button}
            variant='primary'
            type='submit'
            radius='sm'
            loading={form.formState.isSubmitting}
            disabled={!form.formState.isValid || form.formState.isSubmitting}
          >
            {confirmText ?? t('common.button.save')}
          </DecaButton>
        </Group>
      </form>
    </Modal>
  );
};

export default EditModal;
