import { describe, it, expect, vi } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { renderWithMantine, mockLibraries, MantineWrapper } from '@/utils/test';
import EditModal from './EditModal';
import userEvent from '@testing-library/user-event';

// Mock useModalStyles
vi.mock('./useModalStyles', () => ({
  useModalStyles: () => ({
    cx: (...args: any[]) => args.join(' '),
    classes: {
      editModal: 'editModal',
      content: 'content',
      error: 'error',
      label: 'label',
      required: 'required',
      inputWrapper: 'inputWrapper',
      button: 'button',
    },
  }),
}));

mockLibraries();

describe('EditModal', () => {
  const mockOnConfirm = vi.fn();
  const mockOnCancel = vi.fn();

  const defaultProps = {
    opened: true,
    onConfirm: mockOnConfirm,
    onCancel: mockOnCancel,
    initialValues: {
      title: 'Initial Title',
      description: 'Initial Description',
    },
    options: {
      titleLabel: 'Edit Name & Description',
      descriptionLabel: 'Description',
      titlePlaceholder: 'Enter agent name',
      descriptionPlaceholder: 'Enter agent description',
    },
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders basic modal with default props', () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    expect(screen.getByText('Edit Name & Description')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Initial Title')).toBeInTheDocument();
    expect(screen.getByTestId('description-input')).toBeInTheDocument();
    expect(screen.getByText('common.button.save')).toBeInTheDocument();
    expect(screen.getByText('common.button.cancel')).toBeInTheDocument();
  });

  it('renders with custom title and button text', () => {
    renderWithMantine(
      <EditModal
        {...defaultProps}
        title='Custom Title'
        confirmText='Custom Save'
        cancelText='Custom Cancel'
      />
    );

    expect(screen.getByText('Custom Title')).toBeInTheDocument();
    expect(screen.getByText('Custom Save')).toBeInTheDocument();
    expect(screen.getByText('Custom Cancel')).toBeInTheDocument();
  });

  it('handles title input change', async () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    const titleInput = screen.getByDisplayValue('Initial Title');
    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'New Title');

    expect(screen.getByDisplayValue('New Title')).toBeInTheDocument();
  });

  it('handles description input change', async () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    const descriptionInput = screen.getByTestId('description-input');
    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, 'New Description');

    expect(screen.getByDisplayValue('New Description')).toBeInTheDocument();
  });

  it('shows error when submitting with empty title', async () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    const titleInput = screen.getByDisplayValue('Initial Title');
    await userEvent.clear(titleInput);

    const saveButton = screen.getByText('common.button.save');
    await userEvent.click(saveButton);

    expect(await screen.findByText('common.error.required')).toBeInTheDocument();
    expect(mockOnConfirm).not.toHaveBeenCalled();
  });

  it('calls onConfirm with valid input', async () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    const titleInput = screen.getByDisplayValue('Initial Title');
    const descriptionInput = screen.getByTestId('description-input');

    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'New Title');
    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, 'New Description');

    const saveButton = screen.getByText('common.button.save');
    await userEvent.click(saveButton);

    expect(mockOnConfirm).toHaveBeenCalledWith({
      title: 'New Title',
      description: 'New Description',
    });
  });

  it('calls onCancel and resets form when cancel is clicked', async () => {
    renderWithMantine(<EditModal {...defaultProps} />);

    const titleInput = screen.getByDisplayValue('Initial Title');
    const descriptionInput = screen.getByTestId('description-input');

    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'New Title');
    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, 'New Description');

    const cancelButton = screen.getByText('common.button.cancel');
    await userEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();

    // Form should be reset to initial values
    expect(screen.getByDisplayValue('Initial Title')).toBeInTheDocument();
    expect(screen.getByTestId('description-input')).toBeInTheDocument();
  });

  it('handles custom validation', async () => {
    const customValidation = {
      title: (value: string) => (value.length < 5 ? 'Title too short' : undefined),
      description: (value: string) => (value.length < 10 ? 'Description too short' : undefined),
    };

    renderWithMantine(
      <EditModal
        {...defaultProps}
        options={{
          validate: customValidation,
        }}
      />
    );

    const titleInput = screen.getByDisplayValue('Initial Title');
    const descriptionInput = screen.getByTestId('description-input');

    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'HI');
    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, 'Short');

    const saveButton = screen.getByText('common.button.save');
    await userEvent.click(saveButton);

    expect(await screen.findByText('Description too short')).toBeInTheDocument();
    expect(await screen.findByText('Title too short')).toBeInTheDocument();
    expect(mockOnConfirm).not.toHaveBeenCalled();
  });

  it('renders with custom modal options', () => {
    renderWithMantine(
      <EditModal
        {...defaultProps}
        options={{
          modalSize: 'lg',
          titleLabel: 'Custom Title Label',
          descriptionLabel: 'Custom Description Label',
          titlePlaceholder: 'Custom Title Placeholder',
          descriptionPlaceholder: 'Custom Description Placeholder',
        }}
      />
    );

    expect(screen.getByText('Custom Title Label')).toBeInTheDocument();
    expect(screen.getByText('Custom Description Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Custom Title Placeholder')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Custom Description Placeholder')).toBeInTheDocument();
  });

  it('shows error only for description validation when title is valid', async () => {
    const customValidation = {
      description: (value: string) => (value.length < 10 ? 'Description too short' : undefined),
    };

    renderWithMantine(
      <EditModal
        {...defaultProps}
        options={{
          validate: customValidation,
        }}
      />
    );

    const titleInput = screen.getByDisplayValue('Initial Title');
    const descriptionInput = screen.getByTestId('description-input');

    await userEvent.clear(titleInput);
    await userEvent.type(titleInput, 'Valid Title');
    await userEvent.clear(descriptionInput);
    await userEvent.type(descriptionInput, 'Short');

    const saveButton = screen.getByText('common.button.save');
    await userEvent.click(saveButton);

    expect(screen.queryByText('Title too short')).not.toBeInTheDocument();
    expect(await screen.findByText('Description too short')).toBeInTheDocument();
    expect(mockOnConfirm).not.toHaveBeenCalled();
  });

  it('handles async validation errors from onConfirm', async () => {
    const errorMessage = 'Server validation failed';
    const mockOnConfirmWithError = vi.fn().mockRejectedValue(new Error(errorMessage));

    renderWithMantine(<EditModal {...defaultProps} onConfirm={mockOnConfirmWithError} />);

    const saveButton = screen.getByText('common.button.save');
    await userEvent.click(saveButton);

    expect(await screen.findByText(errorMessage)).toBeInTheDocument();
  });

  it('disables submit button while submitting', async () => {
    const mockOnConfirmWithDelay = vi
      .fn()
      .mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

    renderWithMantine(<EditModal {...defaultProps} onConfirm={mockOnConfirmWithDelay} />);

    const saveButton = screen.getByText('common.button.save').parentElement?.parentElement;
    if (!saveButton) {
      throw new Error('Save button not found');
    }

    await userEvent.click(saveButton);
    expect(saveButton).toHaveAttribute('data-disabled');
    await waitFor(() => {
      expect(saveButton).not.toHaveAttribute('data-disabled');
    });
  });

  it('updates form values when initialValues prop changes', async () => {
    const { rerender } = renderWithMantine(<EditModal {...defaultProps} />);

    // Verify initial values
    expect(screen.getByDisplayValue('Initial Title')).toBeInTheDocument();
    expect(screen.getByTestId('description-input')).toBeInTheDocument();

    // Update initialValues prop
    const newInitialValues = {
      title: 'Updated Title',
      description: 'Updated Description',
    };

    // Rerender with the wrapper
    rerender(
      <MantineWrapper>
        <EditModal {...defaultProps} initialValues={newInitialValues} />
      </MantineWrapper>
    );

    // Verify form values are updated
    await waitFor(() => {
      expect(screen.getByDisplayValue('Updated Title')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Updated Description')).toBeInTheDocument();
    });
  });
});
