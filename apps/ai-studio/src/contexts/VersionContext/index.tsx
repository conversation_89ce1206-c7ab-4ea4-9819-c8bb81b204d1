import React, { createContext, useContext, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useListVersion } from '@/hooks/useListVersion';
import { VersionAPI } from '@/services/api/version';
import { ResourceType } from '@/types';
import { IVersion } from '@/models/version';
import { useHandleApiError } from '@/hooks/useHandleApiError';

const useVersion = (resourceType: ResourceType, resourceId: string) => {
  const { workspaceId } = useParams();
  const [cursor, setCursor] = useState('');
  const [limit, setLimit] = useState(20);
  const [searchValue, setSearchValue] = useState('');
  const { handleApiError } = useHandleApiError();

  const { data: versions, mutate: mutateVersions } = useListVersion({
    workspaceId: workspaceId || '',
    cursor,
    limit,
    searchValue,
    resourceType,
    resourceId,
  });

  const addVersion = async ({
    name,
    description,
    resourceId,
    snapshot,
  }: {
    name: string;
    description?: string;
    resourceId: string;
    snapshot?: Record<string, any>;
  }) => {
    if (!workspaceId) return;
    try {
      await VersionAPI.create(workspaceId, {
        name,
        description,
        resourceId,
        resourceType,
        workspaceId,
        snapshot,
      });

      await mutateVersions();
    } catch (error) {
      handleApiError(error);
    }
  };

  const updateVersion = async (version: IVersion) => {
    if (!workspaceId) return;
    try {
      await VersionAPI.update(workspaceId, version);
      await mutateVersions();
    } catch (error) {
      handleApiError(error);
    }
  };

  const restoreVersion = async (versionId: string) => {
    if (!workspaceId) return;
    try {
      await VersionAPI.restore(workspaceId, versionId, resourceType, resourceId);
      await mutateVersions();
    } catch (error) {
      handleApiError(error);
    }
  };

  const loadMore = async () => {
    if (!workspaceId) return;
    if (versions?.hasMore) {
      setCursor(versions.nextCursor || '');
    }
  };

  return {
    versions,
    mutateVersions,
    addVersion,
    restoreVersion,
    updateVersion,
    loadMore,
    setCursor,
    setLimit,
    setSearchValue,
  };
};

export type VersionContextType = ReturnType<typeof useVersion>;

const VersionContext = createContext<VersionContextType | null>(null);

export const VersionContextProvider = ({
  children,
  resourceType,
  resourceId,
}: {
  children: React.ReactNode;
  resourceType: ResourceType;
  resourceId: string;
}) => {
  const value = useVersion(resourceType, resourceId);

  return <VersionContext.Provider value={value}>{children}</VersionContext.Provider>;
};

export const useVersionContext = () => {
  const value = useContext(VersionContext);
  if (!value) {
    throw new Error('useVersionContext must be used inside VersionContextProvider');
  }
  return value;
};
