import { useListVersion } from '@/hooks/useListVersion';
import { IVersion } from '@/models';
import { VersionAPI } from '@/services/api/version';
import { ResourceType } from '@/types';
import { act, renderHook } from '@testing-library/react';
import React from 'react';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { VersionContextProvider, useVersionContext } from '.';

// Mock react-router-dom
vi.mock('react-router-dom', () => ({
  useParams: () => ({
    workspaceId: 'test-workspace',
  }),
}));

// Mock useListVersion hook
vi.mock('@/hooks/useListVersion', () => ({
  useListVersion: vi.fn(),
}));

// Mock Version API
vi.mock('@/services/api/version', () => ({
  VersionAPI: {
    create: vi.fn(),
    update: vi.fn(),
    restore: vi.fn(),
  },
}));

// Mock React's useState
const mockSetCursor = vi.fn();

vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn(initial => [initial, mockSetCursor]),
  };
});

const mockVersion: IVersion = {
  id: 'test-version',
  name: 'Test Version',
  workspaceId: 'test-workspace',
  description: 'Test Description',
  version: '1',
  resourceType: 'prompt' as ResourceType,
  resourceId: 'test-resource',
};

const mockVersionList = {
  data: [mockVersion],
  nextCursor: 'next-cursor',
  prevCursor: 'prev-cursor',
  hasMore: true,
};

describe('VersionContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useListVersion as any).mockReturnValue({
      data: mockVersionList,
      mutate: vi.fn(),
    });
    vi.mocked(VersionAPI.create).mockResolvedValue(mockVersion);
    vi.mocked(VersionAPI.update).mockResolvedValue(mockVersion);
    vi.mocked(VersionAPI.restore).mockResolvedValue(mockVersion);
  });

  it('should provide context with default values', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <VersionContextProvider resourceType='prompt' resourceId='test-resource'>
        {children}
      </VersionContextProvider>
    );

    const { result } = renderHook(() => useVersionContext(), { wrapper });

    expect(result.current.versions).toEqual(mockVersionList);
    expect(typeof result.current.addVersion).toBe('function');
    expect(typeof result.current.updateVersion).toBe('function');
    expect(typeof result.current.restoreVersion).toBe('function');
    expect(typeof result.current.loadMore).toBe('function');
    expect(typeof result.current.setCursor).toBe('function');
    expect(typeof result.current.setLimit).toBe('function');
    expect(typeof result.current.setSearchValue).toBe('function');
  });

  it('should add version successfully', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <VersionContextProvider resourceType='prompt' resourceId='test-resource'>
        {children}
      </VersionContextProvider>
    );

    const { result } = renderHook(() => useVersionContext(), { wrapper });

    await act(async () => {
      await result.current.addVersion({
        name: 'New Version',
        description: 'New Description',
        resourceId: 'test-resource',
      });
    });

    expect(VersionAPI.create).toHaveBeenCalledWith('test-workspace', {
      name: 'New Version',
      description: 'New Description',
      resourceId: 'test-resource',
      resourceType: 'prompt',
      workspaceId: 'test-workspace',
    });
  });

  it('should update version successfully', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <VersionContextProvider resourceType='prompt' resourceId='test-resource'>
        {children}
      </VersionContextProvider>
    );

    const { result } = renderHook(() => useVersionContext(), { wrapper });

    await act(async () => {
      await result.current.updateVersion(mockVersion);
    });

    expect(VersionAPI.update).toHaveBeenCalledWith('test-workspace', mockVersion);
  });

  it('should load more versions when hasMore is true', async () => {
    const wrapper = ({ children }: { children: React.ReactNode }) => (
      <VersionContextProvider resourceType='prompt' resourceId='test-resource'>
        {children}
      </VersionContextProvider>
    );

    const { result } = renderHook(() => useVersionContext(), { wrapper });

    await act(async () => {
      await result.current.loadMore();
    });

    expect(result.current.versions?.hasMore).toBe(true);
    expect(mockSetCursor).toHaveBeenCalledWith(mockVersionList.nextCursor);
  });

  it('should throw error when using context outside provider', () => {
    expect(() => {
      renderHook(() => useVersionContext());
    }).toThrow('useVersionContext must be used inside VersionContextProvider');
  });
});
