import { useCallback, useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { createContextHook } from '@resola-ai/utils';
import { IFunction, IInputParameterSetting, IOutputParameterSetting } from '@/models/function';
import { useParams } from 'react-router-dom';
import useSWR from 'swr';
import { FunctionAPI } from '@/services/api/functions';
import { SAMPLE_CODE, INITIAL_SETTINGS } from '@/constants/function';

type FormValues = {
  name: string;
  description: string;
  code: string;
  settings: {
    input: IInputParameterSetting[];
    output: IOutputParameterSetting;
  };
};

const defaultValues: FormValues = {
  name: '',
  description: '',
  code: '',
  settings: INITIAL_SETTINGS,
};

const useFunctionBuilder = () => {
  const { workspaceId, functionId } = useParams();
  const form = useForm<FormValues>({
    defaultValues,
  });
  const [isExpandOutputExample, setIsExpandOutputExample] = useState<boolean>(false);
  const [showInputParametersPanel, setShowInputParametersPanel] = useState<boolean>(false);
  const [showExecuteResults, setShowExecuteResults] = useState<boolean>(false);
  const [showAIGenerate, setShowAIGenerate] = useState<boolean>(false);

  const functionKey = useMemo(
    () => (workspaceId ? ['functions', workspaceId, functionId] : null),
    [workspaceId, functionId]
  );

  const { data: currentFunction, mutate: mutateCurrentFunction } = useSWR<IFunction | null>(
    functionKey,
    async () => {
      if (!workspaceId || !functionId) {
        return null;
      }
      return FunctionAPI.getById(workspaceId, functionId);
    },
    {
      revalidateOnFocus: false,
    }
  );

  useEffect(() => {
    if (currentFunction) {
      form.reset({
        name: currentFunction.name ?? '',
        description: currentFunction.description ?? '',
        code: currentFunction.code ?? SAMPLE_CODE,
        settings: currentFunction.settings ?? INITIAL_SETTINGS,
      });
    }
  }, [currentFunction, form, functionId]);

  const handlePanelState = useCallback(
    (
      inputParameters: boolean,
      executeResults: boolean,
      isExpandOutput: boolean,
      showAIGenerate: boolean
    ) => {
      setShowInputParametersPanel(inputParameters);
      setShowExecuteResults(executeResults);
      setIsExpandOutputExample(isExpandOutput);
      setShowAIGenerate(showAIGenerate);
    },
    []
  );

  return {
    form,
    isExpandOutputExample,
    setIsExpandOutputExample,
    showInputParametersPanel,
    setShowInputParametersPanel,
    showExecuteResults,
    setShowExecuteResults,
    currentFunction,
    mutateCurrentFunction,
    showAIGenerate,
    setShowAIGenerate,
    handlePanelState,
  };
};

export type FunctionBuilderContextType = ReturnType<typeof useFunctionBuilder>;

export const [FunctionBuilderContextProvider, useFunctionBuilderContext] = createContextHook(
  useFunctionBuilder,
  'FunctionBuilderContext'
);
