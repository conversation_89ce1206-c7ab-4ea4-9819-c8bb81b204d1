import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useParams } from 'react-router-dom';
import { FunctionAPI } from '@/services/api/functions';
import { useFunctionBuilderContext, FunctionBuilderContextProvider } from './index';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useParams: vi.fn(),
}));

vi.mock('@/services/api/functions', () => ({
  FunctionAPI: {
    getById: vi.fn(),
  },
}));

// Mock SWR
const mockSWR = vi.fn();
vi.mock('swr', () => ({
  default: (key: string[], fetcher: () => Promise<any>) => mockSWR(key, fetcher),
}));

const wrapper = ({ children }: { children: React.ReactNode }) => (
  <FunctionBuilderContextProvider>{children}</FunctionBuilderContextProvider>
);

describe('FunctionBuilderContext', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockSWR.mockImplementation((key: string[]) => {
      if (!key) return { data: null, mutate: vi.fn() };
      return {
        data: null,
        mutate: vi.fn(),
        error: new Error('API Error'),
      };
    });
  });

  it('should fetch and set function data when functionId is provided', async () => {
    const mockFunction = {
      id: '123',
      name: 'Test Function',
      description: 'Test Description',
      code: 'test code',
      settings: {
        input: [],
        output: {
          type: 'json_object',
          properties: {
            processed: {
              type: 'string',
            },
          },
        },
      },
    };

    vi.mocked(useParams).mockReturnValue({ workspaceId: '123', functionId: '123' });
    vi.mocked(FunctionAPI.getById).mockResolvedValue(mockFunction);

    // Override SWR mock for this test
    mockSWR.mockImplementation(() => ({
      data: mockFunction,
      mutate: vi.fn(),
    }));

    const { result } = renderHook(() => useFunctionBuilderContext(), { wrapper });

    await act(async () => {
      await new Promise(resolve => setTimeout(resolve, 0));
    });

    expect(result.current.form.getValues()).toEqual({
      name: mockFunction.name,
      description: mockFunction.description,
      code: mockFunction.code,
      settings: mockFunction.settings,
    });
  });

  it('should handle panel state changes correctly', () => {
    vi.mocked(useParams).mockReturnValue({ workspaceId: '123', functionId: undefined });

    const { result } = renderHook(() => useFunctionBuilderContext(), { wrapper });

    act(() => {
      result.current.handlePanelState(true, true, true, true);
    });

    expect(result.current.showInputParametersPanel).toBe(true);
    expect(result.current.showExecuteResults).toBe(true);
    expect(result.current.isExpandOutputExample).toBe(true);
    expect(result.current.showAIGenerate).toBe(true);

    act(() => {
      result.current.handlePanelState(false, false, false, false);
    });

    expect(result.current.showInputParametersPanel).toBe(false);
    expect(result.current.showExecuteResults).toBe(false);
    expect(result.current.isExpandOutputExample).toBe(false);
    expect(result.current.showAIGenerate).toBe(false);
  });

  it('should provide form control methods', () => {
    vi.mocked(useParams).mockReturnValue({ workspaceId: '123', functionId: undefined });

    const { result } = renderHook(() => useFunctionBuilderContext(), { wrapper });

    expect(result.current.form).toBeDefined();
    expect(typeof result.current.form.setValue).toBe('function');
    expect(typeof result.current.form.getValues).toBe('function');
    expect(typeof result.current.form.reset).toBe('function');
  });
});
