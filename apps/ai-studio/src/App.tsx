import { useEffect } from 'react';

import { Route, Routes } from 'react-router-dom';
import { useTolgee } from '@tolgee/react';
import { useSetDefaultLang } from '@resola-ai/ui/hooks';
import { ErrorPage } from '@resola-ai/ui/pages';

import {
  Agents,
  Workspace,
  Functions,
  FlowList,
  FlowBuilder,
  Credentials,
  FunctionBuilder,
  AgentBuilder,
  PromptBuilder,
  Prompts,
  Storage,
} from './pages';
import { HomeLayout, FeaturesLayout } from '@/layouts';

const App = () => {
  const lang = useSetDefaultLang();
  const tolgee = useTolgee();

  useEffect(() => {
    if (tolgee.getLanguage() !== lang) {
      tolgee.changeLanguage(lang);
    }
  }, [lang, tolgee]);

  return (
    <Routes>
      <Route path='/studio'>
        <Route path='' element={<HomeLayout />}>
          <Route path='' element={<Workspace />} />
          <Route path='settings' element={<></>} />
          <Route path='trash' element={<></>} />
        </Route>
        <Route path=':workspaceId' element={<FeaturesLayout noPadding />}>
          <Route path='agents/:agentId' element={<AgentBuilder />} />
          <Route path='flows/:flowId' element={<FlowBuilder />} />
          <Route path='prompts/:promptId' element={<PromptBuilder />} />
          <Route path='functions/:functionId' element={<FunctionBuilder />} />
        </Route>
        <Route path=':workspaceId' element={<FeaturesLayout />}>
          <Route path='agents' element={<Agents />} />
          <Route path='functions' element={<Functions />} />
          <Route path='flows' element={<FlowList />} />
          <Route path='prompts' element={<Prompts />} />
          <Route path='credentials' element={<Credentials />} />
          <Route path='storage' element={<Storage />} />
          <Route path='*' element={<></>} />
        </Route>
        <Route path='unauthorized' element={<ErrorPage appName={tolgee.t('aiStudio')} />} />
      </Route>
    </Routes>
  );
};

export default App;
