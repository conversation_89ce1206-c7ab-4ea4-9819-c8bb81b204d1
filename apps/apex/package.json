{"name": "apex", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --fix", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@mantine/core": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/hooks": "7.17.7", "@resola-ai/models": "workspace:^", "@resola-ai/services-shared": "workspace:^", "json-beautify": "^1.1.1", "jwt-decode": "^3.1.2", "path": "^0.12.7", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "6.19.0", "use-query-params": "2.2.1"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "@vitest/coverage-v8": "^2.1.9", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest-environment-jsdom": "^29.7.0", "typescript": "5.6.3", "vite": "5.4.19", "vite-tsconfig-paths": "^4.2.0", "vitest": "2.1.9"}}