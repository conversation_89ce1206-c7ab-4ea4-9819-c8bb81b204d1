import { BreadcrumbElement as BreadcrumbElementUI } from '@resola-ai/ui/components/PageBuilder';
import { useSearchParams } from 'next/navigation';
import { useEffect, useMemo, useState } from 'react';
import { CategoryTypes } from '@resola-ai/ui/types/pageBuilder';

const BreadcrumbElement = (props: Record<string, any>) => {
  const { categories, categoryListSlug, articleDetailSlug, ...rest } = props;
  const searchParams = useSearchParams();
  const articleId = searchParams.get('faq_article_id');
  const categoryId = searchParams.get('faq_category_id');
  const subCategoryId = searchParams.get('faq_sub_category_id');
  const elementId = searchParams.get('element_id');
  const searchUrl = searchParams.get('faq_search_url');
  const searchPageName = searchParams.get('faq_search_page_name');
  const searchItemName = searchParams.get('faq_search_item_name');
  const categoriesData = categories?.find(category => category.elementId === elementId)?.categories ?? categories?.[0]?.categories;

  const getUrl = (parentCategoryId, currentCategoryId, currentArticleId, categoryType) => {
    if (categoryType === CategoryTypes.Category) {
      return categoryListSlug ? `${categoryListSlug}?element_id=${elementId}&faq_category_id=${currentCategoryId}` : '#';
    } else if (categoryType === CategoryTypes.SubCategory) {
      return categoryListSlug ? `${categoryListSlug}?element_id=${elementId}&faq_category_id=${parentCategoryId}&faq_sub_category_id=${currentCategoryId}` : '#';
    } else {
      return articleDetailSlug ? `${articleDetailSlug}?faq_article_id=${currentArticleId}&faq_base_id=${currentCategoryId}&element_id=${elementId}` : '#';
    }
  }

  const breadcrumbMap = useMemo(() => {
    const map = new Map();
    if (!categoriesData) return map;
    categoriesData?.forEach(category => {
      const addArticlesToMap = (category: Record<string, any>, breadcrumbs: { label: string, url: string }[] = [], categoryType: string = CategoryTypes.Category, parentCategory: any = null) => {
        let urlCategory = '';
        let urlArticle = '';
        if (category.subType === 'article') {
          category.data?.forEach(article => {
            urlCategory = getUrl(parentCategory?.id, category?.id, article?.value, categoryType);
            urlArticle = getUrl(parentCategory?.id, category?.id, article?.value, null);
            map.set(article.value, {
              ...article,
              breadcrumbs: [...breadcrumbs, { label: category.name, url: urlCategory }, { label: article.label, url: urlArticle }]
            });
          });
        } else if (category.subType === 'category') {
          category.data?.forEach((subCategory: any) => {
            urlCategory = getUrl(parentCategory?.id, category?.id, null, categoryType);
            addArticlesToMap(subCategory, [...breadcrumbs, { label: category.name, url: urlCategory }], CategoryTypes.SubCategory, category);
          });
        }
        map.set(category.id, {
          ...category,
          breadcrumbs: [...breadcrumbs, { label: category.name, url: urlCategory }]
        });
      };
      addArticlesToMap(category);
    });

    return map;
  }, [categoriesData]);

  const selectedArticle = breadcrumbMap.get(articleId);
  const selectedCategory = breadcrumbMap.get(subCategoryId || categoryId);
  const searchResultBreadcrumbs = searchUrl && articleId ? [{ label: searchPageName, url: searchUrl }, { label: searchItemName, url: location.href }] : null

  return <BreadcrumbElementUI {...rest} breadcrumbs={selectedArticle?.breadcrumbs || selectedCategory?.breadcrumbs || searchResultBreadcrumbs || []} />;
};

export default BreadcrumbElement;
