import { CategoryTreeElement as CategoryTreeElementUI } from '@resola-ai/ui/components/PageBuilder';
import { isEmpty } from 'lodash';
import { useRouter } from 'next/router';

const CategoryTreeElement = (props: Record<string, any>) => {
  const router = useRouter();
  const { categories = [], padding, width, articleDetailSlug, styles, showRightDivider, dividerColor } = props;
  const {
    element_id,
    faq_article_id = '',
    faq_category_id = '',
    faq_sub_category_id = '',
  } = router.query;
  const selectedCategories = categories.find(category => category.elementId === element_id) ?? categories?.[0] ?? null;

  if (isEmpty(selectedCategories)) return null;

  return (
    <CategoryTreeElementUI
      padding={padding}
      width={width}
      categories={selectedCategories?.categories}
      selectedElement={element_id as string}
      selectedArticle={faq_article_id as string}
      selectedCategory={faq_category_id as string}
      selectedSubCategory={faq_sub_category_id as string}
      articleDetailSlug={articleDetailSlug}
      categoryListSlug={router.query.slug as string}
      styles={styles}
      showRightDivider={showRightDivider}
      dividerColor={dividerColor}
    />
  );
};

export default CategoryTreeElement;
