{"name": "management", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --fix --report-unused-disable-directives", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@resola-ai/schema": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/serialize": "1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@tabler/icons-react": "3.17.0", "auth0": "4.0.1", "axios": "^1.8.2", "dayjs": "^1.11.12", "debug": "^4.3.4", "i18next": "23.10.0", "lodash": "^4.17.21", "moment": "^2.30.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-i18next": "14.0.1", "react-infinite-scroller": "^1.2.6", "react-router-dom": "^6.15.0", "@resola-ai/services-shared": "workspace:*", "swr": "^2.2.2", "ts-results-es": "4.1.0-alpha.1", "zod": "^3.24.1", "zod-i18n-map": "^2.20.0", "@resola-ai/models": "workspace:*"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@types/debug": "^4.1.9", "@types/lodash": "^4.14.199", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react-swc": "^3.3.2", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "husky": "^8.0.3", "typescript": "5.6.3", "vite": "5.4.19", "vite-tsconfig-paths": "^4.2.0", "vitest": "2.1.9", "@vitest/coverage-v8": "^2.1.9", "jest-environment-jsdom": "^29.7.0"}, "lint-staged": {"*.{js,css,ts,tsx,jsx}": ["prettier --write", "eslint --fix"]}}