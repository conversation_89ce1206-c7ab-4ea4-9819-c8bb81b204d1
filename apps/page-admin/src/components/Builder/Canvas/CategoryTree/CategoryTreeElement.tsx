import { ElementWrapper } from '@/components/Builder/Canvas/ElementWrapper';
import CategoryTreeSettings from '../../Settings/CategoryTreeSettings/CategoryTreeSettings';
import { PageBuilderElement } from '@/types/enum';
import useCategories from '@/hooks/useCategories';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { useResponsiveNode } from '@/hooks';
import { DefaultElementProps } from '@/constants';
import { useAppSelector } from '@/store/hooks';
import { CategoryTreeElement as CategoryTreeElementUI } from '@resola-ai/ui/components/PageBuilder';

const CategoryTreeElement = () => {
  const { width, backgroundColor, padding, showRightDivider, dividerColor } = useResponsiveNode(
    node => ({
      width: node.data.props.width,
      backgroundColor: node.data.props.backgroundColor,
      padding: node.data.props.padding,
      showRightDivider: node.data.props.showRightDivider,
      dividerColor: node.data.props.dividerColor,
    })
  );
  const { getThemeColor } = useCurrentTheme();
  const { categories, selectedCategory, selectedSubCategory } = useCategories();
  const accordionStyles = {
    root: {
      backgroundColor: getThemeColor(backgroundColor, 'background'),
    },
    item: {
      borderColor: getThemeColor(dividerColor, 'border'),
    },
    chevron: {
      color: getThemeColor('text'),
    },
    label: {
      color: getThemeColor('foreground'),
    },
    control: {
      '&:hover': {
        backgroundColor: getThemeColor('secondary'),
      },
    },
  };

  const { builderPreviewMode } = useAppSelector(state => state.headerNavigation);
  const isDesktopView = builderPreviewMode === 'desktop';

  return (
    <ElementWrapper
      style={{
        display: isDesktopView ? 'flex' : 'none',
      }}
    >
      <CategoryTreeElementUI
        padding={padding}
        width={width || 'auto'}
        categories={categories || []}
        selectedElement={''}
        selectedCategory={selectedCategory?.id || ''}
        selectedSubCategory={selectedSubCategory?.id || ''}
        styles={accordionStyles}
        showRightDivider={showRightDivider}
        dividerColor={getThemeColor(dividerColor, 'border')}
      />
    </ElementWrapper>
  );
};

CategoryTreeElement.craft = {
  props: DefaultElementProps[PageBuilderElement.CategoryTreeElement],
  related: {
    settings: CategoryTreeSettings,
  },
};

export default CategoryTreeElement;
