import { ICON_CONFIG, SIZE_MAP } from '@/constants/builder';
import { ActionIcon, rem, useMantineTheme } from '@mantine/core';
import { IconArrowRight } from '@tabler/icons-react';

import { Group, Text } from '@mantine/core';
import { IconArticle, IconCurrencyQuetzal } from '@tabler/icons-react';
import { useSearchResultProps } from '@/hooks/useSearchResultProps';
import { Link } from 'react-router-dom';

type IllustrationType = keyof typeof ICON_CONFIG;

const getIconSize = (type: IllustrationType, size: number) => {
  return ICON_CONFIG[type].sizes[SIZE_MAP[size as keyof typeof SIZE_MAP] || 'S'];
};

const SearchResultItem = ({ result }: { result: any }) => {
  const theme = useMantineTheme();

  const { iconSize, iconColor, iconBgColor, categoryType, illustrationType } =
    useSearchResultProps();

  const actualIconSize = getIconSize(illustrationType as IllustrationType, iconSize);

  return (
    <Group
      p={categoryType === 'box' ? rem(20) : `${rem(20)} 0`}
      w={'100%'}
      justify='space-between'
      sx={{
        borderBottom: `1px solid ${theme.colors.decaLight[2]}`,
        '&:last-of-type': {
          borderBottom: 'none',
        },
      }}
    >
      <Group>
        {illustrationType === 'icon' ? (
          <IconArticle size={actualIconSize} color={iconColor} style={{ flexShrink: 0 }} />
        ) : (
          <ActionIcon
            size={actualIconSize}
            sx={{ backgroundColor: iconBgColor, borderRadius: theme.radius.xl }}
          >
            <IconCurrencyQuetzal color={iconColor} style={{ flexShrink: 0 }} />
          </ActionIcon>
        )}
        <Link to={result?.url || ''} style={{ textDecoration: 'none' }}>
          <Text c='decaGrey.8' fw={500}>
            {result.title}
          </Text>
        </Link>
      </Group>
      <Link to={result?.url || ''} style={{ textDecoration: 'none' }}>
        <IconArrowRight color={theme.colors.decaGrey[6]} size={20} style={{ flexShrink: 0 }} />
      </Link>
    </Group>
  );
};

export default SearchResultItem;
