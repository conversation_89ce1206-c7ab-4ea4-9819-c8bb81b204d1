import CreateUpdatePageModal from '@/components/PageModal/CreateUpdatePageModal';
import { PagesAPI } from '@/services/api';
import { Page } from '@/types';
import { PageType } from '@/types/enum';
import { Box, Divider, Flex, Menu, Text, useMantineTheme } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconDots,
  IconFile,
  IconFolderFilled,
  IconHome,
  IconList,
  IconPlus,
  IconSearch,
  IconTemplate,
} from '@tabler/icons-react';
import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import DeletePageModal from './DeletePageModal';
import classes from './PagesMenu.module.css';
import { useAppDispatch } from '@/store/hooks';
import { setSelectedPage as setSelectedPageAction } from '@/store/action/builder';
import { PAGE_QUERY_PARAM } from '@/constants/builder';
import { useTranslate } from '@tolgee/react';
import useSave from '@/hooks/useAutoSave';

interface PagesMenuProps {
  setOutsideRefs: (refs: (HTMLDivElement | null)[]) => void;
  pages: Page[] | undefined;
  fetchPages: () => void;
}

const PagesMenu = ({ setOutsideRefs, pages, fetchPages }: PagesMenuProps) => {
  const { t } = useTranslate('builder');
  const theme = useMantineTheme();
  const dispatch = useAppDispatch();
  const [searchParams, setSearchParams] = useSearchParams();

  const [opened, { toggle, close, open }] = useDisclosure(false);
  const [deleteOpened, { close: closeDelete, open: openDelete }] = useDisclosure(false);
  const { siteId } = useParams();
  const [selectedPage, setSelectedPage] = useState<Page>();
  const [confirmDeletePage, setConfirmDeletePage] = useState<Page>();

  const [modal, setModal] = useState<HTMLDivElement | null>(null);
  const [deleteModal, setDeleteModal] = useState<HTMLDivElement | null>(null);
  const [dropdown, setDropdown] = useState<HTMLDivElement | null>(null);
  const { forceSave } = useSave({ isAutoSave: false });

  useEffect(() => {
    setOutsideRefs([modal, deleteModal, dropdown]);
  }, [modal, deleteModal, dropdown]);

  useEffect(() => {
    if (!opened && selectedPage) {
      open();
    }
  }, [selectedPage]);

  useEffect(() => {
    if (!deleteOpened && confirmDeletePage) {
      openDelete();
    }
  }, [confirmDeletePage]);

  useEffect(() => {
    if (!opened && selectedPage) {
      setSelectedPage(undefined);
    }
  }, [opened]);

  useEffect(() => {
    if (!deleteOpened && confirmDeletePage) {
      setConfirmDeletePage(undefined);
    }
  }, [deleteOpened]);

  const updatePage = async (logicalId, updatedData) => {
    if (!siteId) return;
    await PagesAPI.updatePage(siteId, logicalId, updatedData);
    if (updatedData?.is_home_page) {
      const currentHomePage = pages?.find(page => page.is_home_page);
      if (currentHomePage?.logical_id) {
        await PagesAPI.updatePage(siteId, currentHomePage.logical_id, { is_home_page: false });
      }
    }
    fetchPages();
  };

  const deletePage = async logicalId => {
    if (!siteId) return;
    await PagesAPI.deletePage(siteId, logicalId);
    closeDelete();
    fetchPages();
  };

  const getPageIcon = (page: Page) => {
    switch (page.type) {
      case PageType.Custom:
        return page.is_home_page ? <IconHome size={14} /> : <IconFile size={14} />;
      case PageType.FaqSearchResult:
        return <IconSearch size={14} color={theme.colors.decaBlue[6]} />;
      case PageType.FaqCategoryList:
        return <IconFolderFilled size={14} color={theme.colors.decaBlue[6]} />;
      case PageType.FaqAnswer:
      case PageType.FaqArticleDetail:
        return <IconTemplate size={14} />;
      case PageType.QuestionList:
        return <IconList size={14} color={theme.colors.decaBlue[6]} />;
      default:
        return <IconFile size={14} />;
    }
  };

  const onSelectPage = (page: Page) => {
    const _searchParams = new URLSearchParams(searchParams);
    _searchParams.set(PAGE_QUERY_PARAM, page.logical_id);
    setSearchParams(_searchParams);
    dispatch(setSelectedPageAction(page));
  };

  return (
    <Box w={260}>
      <Flex px='sm' py='xs' align='center' justify='space-between'>
        <Text fw={500}>{t('pagesMenuTitle')}</Text>
        <Box className={classes.addNew}>
          <IconPlus size={16} onClick={toggle} cursor='pointer' />
        </Box>
      </Flex>
      <Divider />
      <Box>
        {pages?.map(page => (
          <Flex
            key={page.id}
            p='md'
            align='center'
            justify='space-between'
            className={`${classes.pageItem} ${page.logical_id === searchParams.get('pageId') && classes.selectedPage}`}
            onClick={() => {
              forceSave();
              onSelectPage(page);
            }}
          >
            {getPageIcon(page)}
            <Text ml='md' flex={1}>
              {page.name}
            </Text>
            <Menu shadow='md' width={200}>
              <Menu.Target>
                <IconDots size={16} cursor='pointer' color={theme.colors.decaGrey[6]} />
              </Menu.Target>
              <Menu.Dropdown ref={setDropdown}>
                {!page.is_home_page && (
                  <Menu.Item onClick={() => updatePage(page.logical_id, { is_home_page: true })}>
                    {t('setAsHomePage')}
                  </Menu.Item>
                )}
                <Menu.Item onClick={() => setSelectedPage(page)} disabled={page.is_home_page}>
                  {t('settings')}
                </Menu.Item>
                {/* TODO: Temporary hide duplicate
                <Menu.Item onClick={() => {}} disabled={!page.is_deletable}>
                  {t('duplicate')}
                </Menu.Item> */}
                {/* TODO: Temporary hide hide/unhide
                <Menu.Item
                  onClick={() => updatePage(page.logical_id, { is_hidden: !page.is_hidden })}
                  disabled={page.is_home_page}
                >
                  {t(page.is_hidden ? 'unhidePage' : 'hidePage')}
                </Menu.Item>
                 */}
                <Menu.Item
                  color={theme.colors.decaRed[6]}
                  onClick={() => setConfirmDeletePage(page)}
                  disabled={!page.is_deletable || page.is_home_page}
                >
                  {t('removePage')}
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Flex>
        ))}
      </Box>
      <CreateUpdatePageModal
        setModalRef={setModal}
        isOpen={opened}
        onClose={close}
        mutatePages={fetchPages}
        selectedPage={selectedPage}
      />
      <DeletePageModal
        setDeleteModalRef={setDeleteModal}
        pageLogicalId={confirmDeletePage?.logical_id}
        isOpen={deleteOpened}
        onClose={closeDelete}
        onDelete={deletePage}
      />
    </Box>
  );
};

export default PagesMenu;
