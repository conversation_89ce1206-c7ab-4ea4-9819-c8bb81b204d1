import { Divider, Flex, rem, Text, Tabs } from '@mantine/core';
import ThemesTab from './ThemesTab';
import classes from './StylingMenu.module.css';
import DesignTab from './DesignTab';
import { useThemeSelection } from '@/hooks/theme/useThemeSelection';
import { useSearchParams } from 'react-router-dom';
import { SETTING_QUERY_PARAM, TAB_SETTINGS_QUERY_PARAM } from '@/constants';
import { useEffect, useState } from 'react';
import { useTranslate } from '@tolgee/react';
import ThemeColorSettings from './ThemeColorSettings';
import ThemeTypographySettings from './ThemeTypographySettings';
import ThemeButtonSettings from './ThemeButtonSettings';

const StylingMenu = () => {
  const { t } = useTranslate('builder');
  const { clearSelectedTheme } = useThemeSelection();
  const [searchParams, setSearchParams] = useSearchParams();
  const tabFromQuery = searchParams.get(TAB_SETTINGS_QUERY_PARAM) || 'theme';
  const [currentTab, setCurrentTab] = useState<string | null>(tabFromQuery);
  const [showColorSettings, setShowColorSettings] = useState(false);
  const [showTypographySettings, setShowTypographySettings] = useState(false);
  const [showButtonSettings, setShowButtonSettings] = useState(false);
  const settingFromQuery = searchParams.get(SETTING_QUERY_PARAM);
  const showTabs = !showColorSettings && !showTypographySettings && !showButtonSettings;

  useEffect(() => {
    if (settingFromQuery === 'color') {
      setShowColorSettings(true);
    }
  }, [settingFromQuery]);

  useEffect(() => {
    if (tabFromQuery !== currentTab) {
      setCurrentTab(tabFromQuery);
    }
  }, [tabFromQuery]);

  const handleTabChange = (value: string | null) => {
    if (value === 'design') {
      clearSelectedTheme();
    }
    setCurrentTab(value);
  };

  const handleBack = () => {
    searchParams.delete(SETTING_QUERY_PARAM);
    searchParams.delete(TAB_SETTINGS_QUERY_PARAM);
    setSearchParams(searchParams);
    setShowColorSettings(false);
  };

  return (
    <Flex h='100%' w={rem(260)} direction='column'>
      <Flex p='sm' tt='uppercase'>
        <Text fw={500}>{t('styleMenuTitle')}</Text>
      </Flex>
      <Divider />
      {showColorSettings && <ThemeColorSettings onBack={handleBack} />}
      {showTypographySettings && <ThemeTypographySettings onBack={() => setShowTypographySettings(false)} />}
      {showButtonSettings && <ThemeButtonSettings onBack={() => setShowButtonSettings(false)} />}
      {showTabs && (
        <Tabs value={currentTab} className={classes.tabRoot} onChange={handleTabChange}>
          <Tabs.List className={classes.tabsList}>
            <Tabs.Tab value='theme' className={classes.tab}>
            {t('theme')}
          </Tabs.Tab>
          <Tabs.Tab value='design' className={classes.tab}>
            {t('design')}
          </Tabs.Tab>
        </Tabs.List>
        <Tabs.Panel value='theme' className={classes.tabPanel}>
          <ThemesTab />
        </Tabs.Panel>
        <Tabs.Panel value='design' className={classes.tabPanel}>
          <DesignTab
            setShowColorSettings={setShowColorSettings}
            setShowTypographySettings={setShowTypographySettings}
            setShowButtonSettings={setShowButtonSettings}
          />
          </Tabs.Panel>
        </Tabs>
      )}
    </Flex>
  );
};

export default StylingMenu;
