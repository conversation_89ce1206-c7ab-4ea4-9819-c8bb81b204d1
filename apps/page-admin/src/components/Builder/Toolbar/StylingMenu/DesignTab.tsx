import { Stack, Box, Text, Flex, Divider, Tooltip } from '@mantine/core';
import ColorsBar from './ColorsBar';
import DesignPreviewCard from './DesignCardPreview';
import { DecaButton } from '@resola-ai/ui';
import classes from './StylingMenu.module.css';
import { useState } from 'react';
import { showNotificationToast } from '@/utils/notification';
import { ThemeAPI } from '@/services/api';
import { useUserInfor } from '@/hooks';
import PublishThemeButton from './PublishThemeButton';
import { useCurrentTheme } from '@/hooks/theme/useCurrentTheme';
import { useTranslate } from '@tolgee/react';
import { useThemes } from '@/hooks/theme/useThemes';

const MAX_THEMES = 5;

interface DesignTabProps {
  setShowColorSettings: (show: boolean) => void;
  setShowTypographySettings: (show: boolean) => void;
  setShowButtonSettings: (show: boolean) => void;
}

const DesignTab = ({
  setShowColorSettings,
  setShowTypographySettings,
  setShowButtonSettings,
}: DesignTabProps) => {
  const { t } = useTranslate('builder');
  const { isStudioUser } = useUserInfor();
  const { themes, mutate: mutateThemes } = useThemes();
  const { theme: selectedTheme } = useCurrentTheme();

  const [isSaving, setIsSaving] = useState(false);
  const [initialTheme] = useState(selectedTheme);
  const isThemeChanged = JSON.stringify(initialTheme) !== JSON.stringify(selectedTheme);
  const reachedMaxThemes = themes?.length >= MAX_THEMES;
  const disabledSaveButton = reachedMaxThemes || !isThemeChanged;
  const saveButtonTooltip = reachedMaxThemes
    ? t('saveAsThemeTooltipMessage', {
        maxThemes: 5,
      })
    : t('youHaveSavedThisTheme');

  const handleSaveAsTheme = async () => {
    try {
      const { button, typography, colors } = selectedTheme;
      const newTheme = { button, typography, colors };
      setIsSaving(true);
      await ThemeAPI.create({
        content: newTheme,
      });
      mutateThemes();
      showNotificationToast({
        message: t('saveAsThemeSuccessMessage', {
          themeName: 'My Theme',
        }),
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Flex direction='column' justify='space-between' h='100%'>
      <Stack p='sm' gap='lg' className={classes.tabContent}>
        <Text c='decaNavy.5' fz='sm' fw={700} tt='uppercase'>
          {t('styles')}
        </Text>
        <DesignPreviewCard title={t('color')} onClick={() => setShowColorSettings(true)}>
          <ColorsBar
            colors={{
              primary: '#FF4B55',
              background: '#FFFFFF',
              secondary: '#666666',
              foreground: '#000000',
              text: '#000000',
              border: '#EEEEF1',
              accent: '#FF4B55',
            }}
          />
        </DesignPreviewCard>

        <DesignPreviewCard title={t('typography')} onClick={() => setShowTypographySettings(true)}>
          <Stack gap='xs'>
            <Text size='xl' fw={700}>
              M PLUS 2
            </Text>
            <Text size='xs' c='dimmed'>
              M PLUS 2
            </Text>
          </Stack>
        </DesignPreviewCard>

        <DesignPreviewCard title={t('button')} onClick={() => setShowButtonSettings(true)}>
          <DecaButton size='md' bg='decaMono.0' variant='primary' radius='xl'>
            {t('buttonText')}
          </DecaButton>
        </DesignPreviewCard>
      </Stack>
      <Divider />
      <Box p='md'>
        {isStudioUser ? (
          <PublishThemeButton />
        ) : disabledSaveButton ? (
          <Tooltip label={saveButtonTooltip}>
            <DecaButton
              w='100%'
              variant='neutral'
              size='sm'
              loading={isSaving}
              disabled={disabledSaveButton}
              onClick={handleSaveAsTheme}
            >
              {t('saveAsTheme')}
            </DecaButton>
          </Tooltip>
        ) : (
          <DecaButton
            w='100%'
            variant='neutral'
            size='sm'
            loading={isSaving}
            disabled={disabledSaveButton}
            onClick={handleSaveAsTheme}
          >
            {t('saveAsTheme')}
          </DecaButton>
        )}
      </Box>
    </Flex>
  );
};

export default DesignTab;
