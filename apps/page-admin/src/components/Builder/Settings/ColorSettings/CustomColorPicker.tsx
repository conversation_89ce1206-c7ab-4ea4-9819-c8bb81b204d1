import { Box, Group, Text, ColorPicker, Flex } from '@mantine/core';
import { IconArrowLeft } from '@tabler/icons-react';
import { useMantineTheme } from '@mantine/core';
import { useTranslate } from '@tolgee/react';

interface CustomColorPickerProps {
  value: string;
  onChange: (color: string) => void;
  onChangeEnd?: (color: string) => void;
  onBack: () => void;
}

export const CustomColorPicker = ({
  value,
  onChange,
  onChangeEnd,
  onBack,
}: CustomColorPickerProps) => {
  const theme = useMantineTheme();
  const { t } = useTranslate(['common', 'builder']);

  return (
    <Box onMouseDown={e => e.stopPropagation()}>
      <Group
        align='center'
        gap='xs'
        mb='sm'
        onClick={onBack}
        style={{ display: 'inline-flex', cursor: 'pointer' }}
      >
        <IconArrowLeft size={16} color={theme.colors.decaBlue[5]} />
        <Text c='decaBlue.5' fw={500}>
          {t('back', { ns: 'common' })}
        </Text>
      </Group>
      <Flex mb='sm' align='center' justify='space-between'>
        <Text fw={500}>{t('pickColor', { ns: 'builder' })}</Text>
      </Flex>
      <ColorPicker
        fullWidth
        format='rgba'
        value={value}
        onChangeEnd={onChangeEnd}
        onChange={onChange}
      />
    </Box>
  );
};
