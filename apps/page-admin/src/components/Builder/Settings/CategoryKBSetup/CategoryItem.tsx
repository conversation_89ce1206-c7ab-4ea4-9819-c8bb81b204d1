import { useFormContext } from '@/contexts/FormContext';
import { Category } from '@/types';
import { ArticleType } from '@/types/enum';
import { Box, Flex, Group, Text, rem, useMantineTheme } from '@mantine/core';
import { IconChevronRight, IconFolder } from '@tabler/icons-react';
import { clsx } from 'clsx';
import { useMemo } from 'react';
import { useTranslate } from '@tolgee/react';
import classes from './CategoryKBSetup.module.css';
interface CategoryItemProps {
  onClick: () => void;
  index: number;
}

const CategoryItem = ({ onClick, index }: CategoryItemProps) => {
  const theme = useMantineTheme();
  const { t } = useTranslate('builder');
  const form = useFormContext();

  const category = form.getValues().categories[index];

  const articlesCountText = useMemo(() => {
    const categoriesCount = category.data?.length;
    if (category.subType === ArticleType.Article) {
      return categoriesCount ? `${t('articlesCount', { count: categoriesCount })}` : t('noContent');
    }
    const articlesCount = category.data.reduce(
      (acc: number, item: Category) => acc + item.data.length,
      0
    );

    if (articlesCount) {
      return `${t('categoriesCount', { count: categoriesCount })} • ${t('articlesCount', { count: articlesCount })}`;
    }

    return categoriesCount ? `${t('categoriesCount', { count: categoriesCount })}` : t('noContent');
  }, [category.data, category.subType]);

  return (
    <Box
      component='div'
      className={clsx('cursor-pointer', classes.box)}
      onClick={onClick}
      p={rem(12)}
    >
      <Flex align='center' gap={rem(12)}>
        <Flex direction='column' gap={rem(4)} flex={1}>
          <Group gap='xs'>
            <IconFolder size={16} color={theme.colors.decaBlue[7]} className={classes.noShrink} />
            <Text>{category.name || t('sampleCategory')}</Text>
          </Group>

          <Flex>
            <Text c={'decaGrey.7'}>{articlesCountText}</Text>
          </Flex>
        </Flex>

        <IconChevronRight size={24} color={theme.colors.decaGrey[3]} className={classes.noShrink} />
      </Flex>
    </Box>
  );
};

export default CategoryItem;
