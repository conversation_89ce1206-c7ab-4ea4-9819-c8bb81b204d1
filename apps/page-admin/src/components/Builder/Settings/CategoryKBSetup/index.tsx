import { createTypedF<PERSON>, FormProvider } from '@/contexts/FormContext';
import { useResponsiveNode } from '@/hooks';
import { CategoryFormValues } from '@/types';
import { ArticleType } from '@/types/enum';
import {
  ActionIcon,
  Box,
  Checkbox,
  Divider,
  Flex,
  Group,
  rem,
  ScrollArea,
  Stack,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { zodResolver } from '@mantine/form';
import { useDebouncedCallback, useDisclosure } from '@mantine/hooks';
import { cleanBadMarkdownContent } from '@resola-ai/ui/utils';
import { IconPlus } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { debounce, uniqueId } from 'lodash';
import { useRef } from 'react';
import { ulid } from 'ulid';
import { z } from 'zod';
import DeleteButton from '../DeleteButton';
import CategoryItem from './CategoryItem';
import CategoryItemDetail from './CategoryItemDetail';

const MAX_CATEGORIES = 15;

const { useForm } = createTypedForm<CategoryFormValues>();

const SetupSettings = () => {
  const theme = useMantineTheme();
  const { t } = useTranslate('builder');

  const [opened, { open, close }] = useDisclosure(false);
  const selectedIndexRef = useRef<number>(0);

  const {
    actions: { setProp },
    categories,
    isUpdating,
    syncArticleToSearch,
  } = useResponsiveNode(node => ({
    categories: node.data.props.categories,
    isUpdating: node.data.props.isUpdating,
    syncArticleToSearch: node.data.props.syncArticleToSearch,
  }));

  const schema = z.object({
    categories: z.array(
      z.object({
        name: z.string().min(1, { message: t('requiredError') }),
      })
    ),
  });

  const form = useForm({
    mode: 'uncontrolled',
    name: 'category-kb-setup-form',
    validateInputOnChange: true,
    initialValues: {
      categories: categories.map(category => ({
        ...category,
        name: cleanBadMarkdownContent(category.name),
      })),
    },
    validate: zodResolver(schema),

    onValuesChange: values => {
      debounce(() => {
        const newCategories = structuredClone(values.categories);
        setProp(props => (props.categories = newCategories));
        setProp(props => (props.isUpdating = false));
      }, 600)();
    },
  });

  const formValues = form.values as CategoryFormValues;

  const handleClickOnCategory = (index: number) => {
    selectedIndexRef.current = index;
    open();
  };

  const handleAddCategory = useDebouncedCallback(() => {
    form.insertListItem('categories', {
      name: cleanBadMarkdownContent(t('categoryName')),
      description: '',
      type: ArticleType.Article,
      subType: ArticleType.Article,
      data: [],
      id: ulid(),
    });
  }, 300);

  return (
    <Box
      h='100%'
      pos='relative'
      w='100%'
      style={{ display: 'flex', justifyContent: 'center', margin: 'auto' }}
    >
      <FormProvider form={form}>
        <Flex
          direction='column'
          gap='0'
          h='100%'
          w='100%'
          style={{ display: opened ? 'none' : 'flex' }}
        >
          <Group justify='space-between' p={rem(16)}>
            <Text fw={500} fz={rem(14)}>
              {t('categoriesAndArticles')}
            </Text>
            <ActionIcon
              variant='transparent'
              loading={isUpdating}
              disabled={formValues.categories.length >= MAX_CATEGORIES}
            >
              <IconPlus
                size={16}
                color={theme.colors.decaNavy[4]}
                className='cursor-pointer'
                onClick={() => {
                  setProp(props => (props.isUpdating = true));

                  handleAddCategory();
                }}
              />
            </ActionIcon>
          </Group>

          <ScrollArea flex={1}>
            <Stack px={rem(16)}>
              <Checkbox
                label={t('syncArticleToSearch')}
                checked={syncArticleToSearch ?? true}
                onChange={event =>
                  setProp(props => (props.syncArticleToSearch = event.currentTarget.checked))
                }
              />
              <Divider />

              {formValues.categories.map((_, index) => (
                <CategoryItem
                  key={`category-item-${uniqueId()}`}
                  onClick={() => handleClickOnCategory(index)}
                  index={index}
                />
              ))}
            </Stack>
          </ScrollArea>

          <Flex px={rem(16)} py={rem(8)}>
            <DeleteButton />
          </Flex>
        </Flex>

        <CategoryItemDetail
          opened={opened}
          onClose={() => {
            close();
            selectedIndexRef.current = 0;
          }}
          index={selectedIndexRef.current}
        />
      </FormProvider>
    </Box>
  );
};

export default SetupSettings;
