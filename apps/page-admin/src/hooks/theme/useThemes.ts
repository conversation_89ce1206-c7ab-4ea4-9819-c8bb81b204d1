import { ThemeAPI } from '@/services/api';
import { ThemeItem, ThemeSource } from '@/types';
import { useMemo } from 'react';
import useSWR from 'swr';
import { useUserInfor } from '../useUserInfor';
import { useTranslate } from '@tolgee/react';

const THEMES_KEY = 'themes';

export const useThemes = () => {
  const { data: themes = [], mutate } = useSWR<ThemeItem[]>(THEMES_KEY, () =>
    ThemeAPI.getThemes({ source: ThemeSource.WORKSPACE })
  );
  const { isStudioUser } = useUserInfor();
  const { t } = useTranslate('builder');

  const themesById = useMemo(() => {
    return themes.reduce(
      (acc, currentTheme) => {
        acc[currentTheme.id] = currentTheme;
        return acc;
      },
      {} as Record<string, ThemeItem>
    );
  }, [themes]);

  const themesByCategory = useMemo(() => {
    return themes.reduce(
      (acc, theme) => {
        const categoryId = theme.cat_id;
        if (!acc[categoryId]) {
          acc[categoryId] = {
            name: isStudioUser ? theme.cat_name : t('savedThemes'),
            themes: [],
          };
        }
        acc[categoryId].themes.push(theme);
        return acc;
      },
      {} as Record<string, { name: string; themes: ThemeItem[] }>
    );
  }, [themes]);

  return {
    themes,
    themesById,
    themesByCategory,
    mutate,
    isLoading: !themes,
  };
};
