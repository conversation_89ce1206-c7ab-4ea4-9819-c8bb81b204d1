import { useState, useEffect, useRef, useCallback } from 'react';
import { useEditor } from '@craftjs/core';
import { useParams, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store/hooks';
import { PagesAPI } from '@/services/api';
import { convertObjectPropertyCase, ConvertToType } from '@/utils/apiHelper';
import { debounce } from 'lodash';
import usePages from './usePages';
import { datadogService } from '@resola-ai/services-shared';

interface UseAutoSaveOptions {
  debounceTime?: number;
  onSaveSuccess?: () => void;
  onSaveFail?: (error: any) => void;
  isAutoSave?: boolean;
}

export default function useSave(options: UseAutoSaveOptions = {}) {
  const { debounceTime = 2000, onSaveSuccess, onSaveFail, isAutoSave = true } = options;
  const { siteId } = useParams();
  const { mutate } = usePages({ siteId: siteId as string });

  const { query } = useEditor(state => ({
    json: state.nodes,
  }));

  const location = useLocation();
  const selectedPage = useAppSelector(state => state.builderReducer.selectedPage);
  const [isAutoSaving, setIsAutoSaving] = useState(false);
  const [lastSavedAt, setLastSavedAt] = useState<Date | null>(null);
  const contentRef = useRef<string>('');
  const previousLocationRef = useRef<string>(location.pathname);

  const save = async () => {
    if (!selectedPage || !siteId) return;

    try {
      const content = JSON.parse(query.serialize());
      const payload = {
        name: selectedPage.name,
        content: Object.entries(content).reduce((acc, [key, value]) => {
          acc[key] = convertObjectPropertyCase(value, ConvertToType.SnakeCase);
          return acc;
        }, {}),
      };

      await PagesAPI.updatePage(siteId, selectedPage.logical_id, payload);
      setLastSavedAt(new Date());
      onSaveSuccess?.();
    } catch (error) {
      datadogService.addError('DECA_PAGE_SAVE_ERROR', {
        error: error,
        page: selectedPage,
        siteId: siteId,
      });
      onSaveFail?.(error);
    }
  };

  // Create a debounced save function using lodash
  const debouncedSave = debounce(async () => {
    const currentContent = query.serialize();
    if (currentContent === contentRef.current) return;

    contentRef.current = currentContent;
    await save();
    setIsAutoSaving(false);
  }, debounceTime);

  // Track editor state changes and trigger debounced save
  useEffect(() => {
    if (!isAutoSave) return;
    if (!selectedPage || !siteId) return;
    setIsAutoSaving(true);
    debouncedSave();

    // Clean up the debounced function on unmount
    return () => {
      setIsAutoSaving(false);
      debouncedSave.cancel();
    };
  }, [query, query.serialize(), isAutoSave]);

  // Force save method for manual triggering
  const forceSave = useCallback(() => {
    debouncedSave.cancel();
    return save().then(() => {
      mutate();
      setIsAutoSaving(false);
    });
  }, [debouncedSave, save]);

  // Handle URL changes and navigation
  useEffect(() => {
    // Save on location change if the path is different
    if (previousLocationRef.current !== location.pathname) {
      if (selectedPage && siteId) {
        forceSave();
      }
      previousLocationRef.current = location.pathname;
    }

    // Add event listener for history changes (forward, back buttons)
    const handlePopState = () => {
      if (selectedPage && siteId) {
        forceSave();
      }
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [location, selectedPage, siteId, forceSave]);

  return {
    isAutoSaving,
    lastSavedAt,
    forceSave,
  };
}
