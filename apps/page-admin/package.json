{"name": "page-admin", "private": true, "version": "1.2.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint-staged-check": "lint-staged", "lint:eslint:fix": "eslint . --fix", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "update:translate": "tolgee pull  -ak %VITE_TOLGEE_KEY% --path ./src/locales --delimiter=\n", "coverage": "vitest --run --coverage", "release": "standard-version -t pages-admin@", "release:minor": "standard-version -t pages-admin@ --release-as minor", "release:patch": "standard-version -t pages-admin@ --release-as patch", "release:major": "standard-version -t pages-admin@ --release-as major"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@craftjs/core": "^0.2.11", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "^1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@reduxjs/toolkit": "^2.2.5", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tolgee/format-icu": "^5.32.0", "@tolgee/react": "^5.33.2", "@tolgee/ui": "^4.9.2", "@tolgee/web": "^5.29.2", "axios": "^1.8.2", "clsx": "2.1.1", "dayjs": "^1.11.10", "dotenv": "16.3.1", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "i18next-icu": "^2.3.0", "lodash": "^4.17.21", "nanoid": "^5.0.9", "prettier": "^3.2.1", "query-string": "^9.1.1", "react": "^18.2.0", "react-contenteditable": "^3.3.7", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-i18next": "14.0.1", "react-mentions": "^4.4.10", "react-redux": "^9.1.2", "react-router-dom": "6.21.3", "sass": "^1.77.4", "standard-version": "^9.5.0", "swr": "^2.2.5", "tools": "link:@tolgee/web/tools", "ulid": "^2.3.0", "vite-tsconfig-paths": "^4.2.0", "zod": "^3.24.1"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@sentry/react": "^7.81.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@tolgee/cli": "^2.4.1", "@types/lodash": "^4.14.199", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@types/sanitize-html": "^2.11.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "husky": "^8.0.3", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vite": "5.4.19", "vite-plugin-circular-dependency": "^0.4.1", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx, jsx}": ["prettier --write", "eslint --fix"]}}