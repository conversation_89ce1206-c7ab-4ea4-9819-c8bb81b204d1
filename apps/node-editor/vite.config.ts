import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react-swc';
import tsconfigPaths from 'vite-tsconfig-paths';
import circularDependency from 'vite-plugin-circular-dependency';
import path from 'path';
import dotenv from 'dotenv';

const getCdnPrefix = () => {
  if (process.env.AWS_PULL_REQUEST_ID) {
    console.log('[getCdnPrefix] load .env.preview file');
    const previewEnvironmentVariables = dotenv.configDotenv({
      path: '.env.preview',
    });
    return previewEnvironmentVariables.parsed!.VITE_CDN_PREFIX;
  } else {
    // console.log('[getCdnPrefix] use default env');
    return process.env.VITE_CDN_PREFIX ?? undefined;
  }
};

const cdnPrefix = getCdnPrefix();

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  const CDN_URL = cdnPrefix ?? '/';
  const BASE_PATH = env.VITE_BASE_PATH ?? '/';
  return {
    plugins: [react(), tsconfigPaths(), circularDependency()],
    base: BASE_PATH,
    experimental: {
      renderBuiltUrl(
        filename: string,
        {
          hostId,
          type,
        }: { hostId: string; hostType: 'js' | 'css' | 'html'; type: 'public' | 'asset' }
      ) {
        if (type === 'public') {
          return CDN_URL + filename;
        } else if (path.extname(hostId) === '.js') {
          return CDN_URL + filename;
        } else {
          return CDN_URL + filename;
        }
      },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: './setupTest.js',
      coverage: {
        enabled: true,
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        reportsDirectory: './coverage',
        exclude: [
          'node_modules/**',
          'dist/**',
          'test/**',
          '**/*.d.ts',
          '**/*.test.ts',
          '**/*.test.tsx',
          '**/*.spec.ts',
          '**/*.spec.tsx',
          'vite.config.ts',
          '.eslintrc.cjs',
          'src/mockdata/**',
          'src/models/**',
          'src/types/**',
          'src/services/api/*.ts',
          'src/main.tsx',
        ],
      },
    },
  };
});
