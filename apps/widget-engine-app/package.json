{"name": "widget-engine-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint-staged-check": "lint-staged", "lint:eslint:fix": "eslint . --fix", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "release": "standard-version -t widget-engine-app@", "release:minor": "standard-version -t widget-engine-app@ --release-as minor", "release:patch": "standard-version -t widget-engine-app@ --release-as patch", "release:major": "standard-version -t widget-engine-app@ --release-as major"}, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/serialize": "1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@resola-ai/models": "workspace:^", "@resola-ai/services-shared": "workspace:^", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@resola-ai/widget-engine": "workspace:*", "@uiw/react-codemirror": "^4.22.0", "dotenv": "16.3.1", "framer-motion": "^10.18.0", "jsoneditor": "9.10.3", "jsoneditor-react": "3.1.2", "react": "^18.2.0", "react-dom": "^18.2.0", "vite-tsconfig-paths": "^4.2.0"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@sentry/react": "^7.81.0", "@swc/cli": "0.6.0", "@swc/core": "1.10.14", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.199", "@types/node": "^17.0.45", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "husky": "^8.0.3", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vite": "5.4.19", "vite-plugin-circular-dependency": "^0.4.1", "vitest": "2.1.9", "jest-environment-jsdom": "^29.7.0"}, "lint-staged": {"*.{js,ts,tsx, jsx}": ["prettier --write", "eslint --fix"]}}