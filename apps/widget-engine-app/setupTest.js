/**setupTest.js */
import { expect } from 'vitest';
import * as matchers from '@testing-library/jest-dom/matchers';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };
});

expect.extend(matchers);

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});
