import { Tolgee, FormatSimple } from '@tolgee/react';
import { InContextTools } from '@tolgee/web/tools';
import { FALLBACK_LANGUAGE } from '@resola-ai/ui/constants';
import { DEFAULT_LANGUAGE } from '@resola-ai/shared-constants';

import { AppConfig } from '@/configs';
import enHome from '@/locales/home/<USER>';
import enLanguage from '@/locales/language/en.json';
import jaHome from '@/locales/home/<USER>';
import jaLanguage from '@/locales/language/ja.json';

const tolgee = Tolgee()
  .use(AppConfig.TOLGEE_TOOLS_ENABLED ? InContextTools() : undefined)
  .use(FormatSimple())
  .init({
    language: DEFAULT_LANGUAGE,
    fallbackLanguage: FALLBACK_LANGUAGE,
    defaultNs: 'home',
    apiUrl: AppConfig.TOLGEE_URL,
    apiKey: AppConfig.TOLGEE_KEY,

    staticData: {
      'en:home': enHome,
      'ja:home': jaHome,
      'en:language': enLanguage,
      'ja:language': jaLanguage,
    },
    onFormatError: (error) => {
      console.error('AI-Widgets Tolgee translate error', error);
      return error;
    },
  });

export { tolgee };
