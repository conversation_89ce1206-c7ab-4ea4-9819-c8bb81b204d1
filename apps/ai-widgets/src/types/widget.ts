import type {
  ReplyAssistantSetting,
  SummarizationSetting,
  TranslationSetting,
} from '@/schemas/setting';
import type { Control, FieldErrors } from 'react-hook-form';

export type WidgetType =
  | 'KB_SEARCH'
  | 'PROOFREADING'
  | 'REPLY_ASSISTANT'
  | 'TONE_ADJUSTMENT'
  | 'SUMMARIZATION'
  | 'TRANSLATION'
  | 'EXPLANATION'
  | 'BROWSER_EXTENSION';

export enum WidgetTypeEnum {
  KB_SEARCH = 'kbSearch',
  PROOFREADING = 'proofreading',
  REPLY_ASSISTANT = 'replyAssistant',
  TONE_ADJUSTMENT = 'toneAdjustment',
  SUMMARIZATION = 'summarization',
  TRANSLATION = 'translation',
  EXPLANATION = 'explanation',
  BROWSER_EXTENSION = 'browserExtension',
}

export const ALL_WIDGET_TYPES = Object.keys(WidgetTypeEnum);

export const STANDARD_WIDGET_TYPES = ALL_WIDGET_TYPES.filter(
  (type) => !type.includes('BROWSER_EXTENSION')
);

export const CUSTOM_WIDGET_TYPES = ALL_WIDGET_TYPES.filter((type) =>
  type.includes('BROWSER_EXTENSION')
);

export type WidgetFilterValue = 'all' | 'standard' | 'custom';
export type WidgetInstanceStatus = 'public' | 'private';

export type WidgetTypeInfo = {
  [key in WidgetType]: {
    title?: string;
    description?: string;
    isUsingAI?: boolean;
    orgWhitelistIds?: string[];
  };
};

export type WidgetCompilation = {
  compilationId: string;
  compilationName: string;
  compilationDescription: string;
  manifestUrl: string;
  metadata: Record<string, any>;
};

export type Widget = {
  id?: string;
  name: string;
  description: string;
  type: WidgetType;
  created: Date;
  updated: Date;
  settings?: Record<string, any>;
  isCustom?: boolean;
  widget?: {
    compilations: WidgetCompilation[];
    settings: {
      [key: string]: any;
      isAI: boolean;
      maxInstances: number;
      type: 'singleton' | 'multiple';
    };
  };
};

export type WidgetRule = {
  name: string;
  enabled: boolean;
};

export type WidgetInstance = Omit<Widget, 'type'> & {
  orgId: string;
  widgetId: string;
  widgetType: WidgetType;
  widget: {
    id: string;
    name: string;
    description: string;
  };
  status: WidgetInstanceStatus;
};

export type WidgetPayload = Pick<WidgetInstance, 'name' | 'settings' | 'status'> & {
  description?: string;
};

export interface WidgetInstanceFieldProps {
  control:
    | Control<WidgetInstance>
    | Control<ReplyAssistantSetting>
    | Control<SummarizationSetting>
    | Control<TranslationSetting>;
  errors:
    | FieldErrors<WidgetInstance>
    | FieldErrors<ReplyAssistantSetting>
    | FieldErrors<SummarizationSetting>
    | FieldErrors<TranslationSetting>;
}
