import type { TranslationLanguage } from '@/types/translation';
import { axiosService, logger } from '@resola-ai/services-shared';

export const TranslationAPI = {
  getLanguages: async (data: {
    searchTerm?: string;
    type: 'default-languages' | 'more-languages';
  }) => {
    const { type, ...params } = data;
    const searchParams = new URLSearchParams();
    const url = `/translation/${type}`;

    for (const [key, value] of Object.entries(params)) {
      if (!value) continue;

      searchParams.set(key, value);
    }

    try {
      const response = await axiosService.instance.get<TranslationLanguage[]>(
        `${url}?${searchParams.toString()}`
      );

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
