import { KB_BASES_SHORT_PATH, KB_FOLDERS_SHORT_PATH, ROOT_PATH } from '@/constants/kb';
import type { Folder, Kb, KBDocument, SearchCombineResult } from '@/types';
import type {
  ISuccessListNextPreviousResponse,
  ISuccessListResponseWithoutPagination,
  ISuccessResponse,
} from '@resola-ai/models';
import { axiosService, logger } from '@resola-ai/services-shared';
import { kbService } from '@/services';

export const KbsAPI = {
  getFolders: async (parentDirId: string, depth = 2, take?: number) => {
    try {
      const response = await kbService.instance.get<
        Pick<ISuccessListResponseWithoutPagination<Folder>, 'data'>
      >(KB_FOLDERS_SHORT_PATH, {
        params: {
          parentDirId,
          depth,
          take,
        },
      });

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getFolder: async (folderId: string) => {
    try {
      const response = await kbService.instance.get<Pick<ISuccessResponse<Folder>, 'data'>>(
        `${KB_FOLDERS_SHORT_PATH}${folderId}`
      );

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  searchFolders: async (query: string) => {
    try {
      const response = await axiosService.instance.post<
        Pick<ISuccessListResponseWithoutPagination<Folder>, 'data'>
      >('/knowledgebases/folders/search', { query, dirIds: [] });

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  combineSearch: async (query: string) => {
    try {
      const response = await kbService.instance.post<
        Pick<ISuccessListResponseWithoutPagination<SearchCombineResult>, 'data'>
      >('/search', {
        query,
        entities: ['folder', 'base', 'document', 'article'],
      });

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getList: async (
    parentDirId = '',
    limit = 20,
    cursor = '',
    direction = '',
    kbIds: string[] = []
  ) => {
    try {
      const queryParams = new URLSearchParams({
        parentDirId: parentDirId || ROOT_PATH,
        take: limit.toString(),
        cursor,
        direction,
      });

      kbIds.forEach((id) => queryParams.append('ids', id));

      const response = await kbService.instance.get<
        Omit<ISuccessListNextPreviousResponse<Kb>, 'status'>
      >(`${KB_BASES_SHORT_PATH}?${queryParams.toString()}`);

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getKbById: async (kbId: string) => {
    try {
      const response = await kbService.instance.get<ISuccessResponse<Kb>>(
        `${KB_BASES_SHORT_PATH}${kbId}`
      );

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getDocuments: async (parentDirId: string, limit = 20, cursor = '', direction = 'backward') => {
    try {
      const response = await kbService.instance.get<ISuccessListNextPreviousResponse<KBDocument>>(
        '/documents',
        {
          params: {
            parentDirId: parentDirId || ROOT_PATH,
            limit: limit.toString(),
            cursor,
            direction,
          },
        }
      );

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getDocument: async (documentId: string) => {
    try {
      const response = await kbService.instance.get<ISuccessResponse<KBDocument>>(
        `/documents/${documentId}`,
        {
          params: {
            resolveBreadcrumb: true,
          },
        }
      );

      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
