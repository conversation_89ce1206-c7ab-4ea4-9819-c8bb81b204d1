import { axiosService, executeRequest } from '@resola-ai/services-shared';

import type { Workspace } from '@/types/workspace';

export const WorkspaceAPI = {
  getDefaultWorkspace: async () => {
    return executeRequest<Workspace>(() => axiosService.instance.get('/workspaces/default'));
  },
  getWorkspace: async (id: string) => {
    return executeRequest<Workspace>(() => axiosService.instance.get(`/workspaces/${id}`));
  },
  updateWorkspace: async (id: string, data: Partial<Workspace>) => {
    return executeRequest<Workspace>(() => axiosService.instance.put(`/workspaces/${id}`, data));
  },
  createWorkspace: async (data: Partial<Workspace>) => {
    return executeRequest<Workspace>(() => axiosService.instance.post('/workspaces', data));
  },
  getAllWorkspaces: async () => {
    return executeRequest<Workspace[]>(() => axiosService.instance.get('/workspaces'));
  },
};
