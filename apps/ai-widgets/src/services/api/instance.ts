import { axiosService, executeRequest } from '@resola-ai/services-shared';
import { HTTP_ERROR_STATUS } from '@resola-ai/ui/constants';

import type { WidgetInstance, WidgetPayload } from '@/types/widget';
import type { AxiosError } from 'axios';

export const WidgetInstanceAPI = {
  getList: async (wsId: string) => {
    return executeRequest<WidgetInstance[]>(() =>
      axiosService.instance.get(`/${wsId}/widgetInstance`)
    );
  },
  remove: async (wsId: string, id: string) => {
    return executeRequest<WidgetInstance>(() =>
      axiosService.instance.delete(`/${wsId}/widgetInstance/${id}`)
    );
  },
  create: async (wsId: string, widgetId: string, payload: WidgetPayload) => {
    return executeRequest<WidgetInstance>(
      () => axiosService.instance.post(`/${wsId}/widgetInstance/${widgetId}`, payload),
      (error) => {
        if ((error as AxiosError).response?.status === HTTP_ERROR_STATUS.CONFLICT) {
          throw new Error('Duplicated');
        }

        if ((error as AxiosError).response?.status === HTTP_ERROR_STATUS.FORBIDDEN) {
          throw new Error('Limited');
        }
      }
    );
  },
  updateSettings: async (wsId: string, id: string, payload: Record<string, any>) => {
    return executeRequest<WidgetInstance>(
      () =>
        axiosService.instance.put<WidgetInstance>(`/${wsId}/widgetInstance/${id}/settings`, {
          settings: payload,
        }),
      (error) => {
        if ((error as AxiosError).response?.status === HTTP_ERROR_STATUS.CONFLICT) {
          throw new Error('Duplicated');
        }
      }
    );
  },
  update: async (wsId: string, id: string, payload: Omit<WidgetPayload, 'settings'>) => {
    return executeRequest<WidgetInstance>(() =>
      axiosService.instance.put<WidgetInstance>(`/${wsId}/widgetInstance/${id}`, payload)
    );
  },
  syncCompilation: async (wsId: string, id: string) => {
    return executeRequest<WidgetInstance>(() =>
      axiosService.instance.post<WidgetInstance>(`/${wsId}/widgetInstance/${id}/sync`)
    );
  },
};
