import { axiosService, logger } from '@resola-ai/services-shared';
import type { Widget } from '@/types/widget';

export const WidgetAPI = {
  getList: async () => {
    try {
      const response = await axiosService.instance.get<Array<Widget>>('/widgets');
      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
  getCustomList: async () => {
    try {
      const response = await axiosService.instance.get<Array<Widget>>('/widget-custom');
      return response.data;
    } catch (error) {
      logger.error(error);
      throw error;
    }
  },
};
