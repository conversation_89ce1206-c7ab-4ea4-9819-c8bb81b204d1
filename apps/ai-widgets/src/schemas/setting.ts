import { z } from 'zod';
import { widgetInstanceStatus } from './constant';
import { REPLY_ASSISTANT_VARIABLES, SUMMARIZATION_VARIABLES } from '@/constants/widget';

export const settingSchema = z.object({
  name: z.string().min(1),
  description: z.string().optional(),
  status: widgetInstanceStatus,
});

export const replyAssistantSettingSchema = settingSchema.extend({
  settings: z.object({
    searchFolders: z.array(z.string()),
    searchBases: z.array(z.string()),
    searchDocuments: z.array(z.string()),
    prompt: z
      .string()
      .min(1)
      .refine(
        (val) =>
          val.includes(REPLY_ASSISTANT_VARIABLES.USER_INPUT) &&
          val.includes(REPLY_ASSISTANT_VARIABLES.KB_RESULT),
        { message: 'replyAssistant.error.prompt' }
      ),
  }),
});

export type ReplyAssistantSetting = z.infer<typeof replyAssistantSettingSchema>;

export const kbSettingSchema = settingSchema.extend({
  settings: z.object({
    searchFolders: z.array(z.string()),
    searchBases: z.array(z.string()),
  }),
});

export const proofreadingSettingSchema = settingSchema.extend({
  settings: z.object({
    prompt: z.string(),
    userDictionary: z.string(),
    useAI: z.boolean(),
    useUserDictionary: z.boolean(),
  }),
});

export const summarizationSettingSchema = settingSchema.extend({
  settings: z.object({
    prompt: z
      .string()
      .min(1, 'common.error.required')
      .includes(SUMMARIZATION_VARIABLES.INPUT_CONTENT, { message: 'summarization.error.prompt' }),
  }),
});

export type SummarizationSetting = z.infer<typeof summarizationSettingSchema>;

export const translationSettingSchema = settingSchema.extend({
  settings: z.object({
    additionalLanguages: z.array(z.string()),
    languages: z.array(z.string()).min(2),
  }),
});

export type TranslationSetting = z.infer<typeof translationSettingSchema>;
