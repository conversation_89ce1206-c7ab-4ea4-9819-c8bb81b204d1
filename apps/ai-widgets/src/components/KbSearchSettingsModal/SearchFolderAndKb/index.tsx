import { useState, Fragment, useCallback } from 'react';
import { Box, Divider, Flex, Loader, rem, Text, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight } from '@tabler/icons-react';
import { IconFolder } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { If } from '@resola-ai/ui';
import { Colors } from '@resola-ai/ui/constants/themeConfiguration';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import get from 'lodash/get';

import type { Folder, Kb, KBDocument } from '@/types';
import { KbsAPI } from '@/services/api/kbs';
import { useKbWidgetContext } from '@/contexts/KbWidgetContext';
import { ROOT_PATH } from '@/constants/kb';
import { NoContent } from '../NoContent';
import RightItem from '../RightItem';
import { renderIcon } from '@/components/FileTypeIcons';

const useStyles = createStyles((theme) => ({
  selectContainer: {
    minHeight: rem(309),
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: rem(4),
    border: `${rem(0.5)} solid ${theme.colors.silverFox[7]}`,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  list: {
    display: 'flex',
    flexDirection: 'column',
    height: rem(274),
    overflowY: 'auto',
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
  item: {
    padding: rem(4),
    gap: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    border: `${rem(1)} solid transparent`,
    ':hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  selected: {
    backgroundColor: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
  pathContainer: {
    height: rem(24),
    alignItems: 'center',
    color: theme.colors.decaGrey[4],
    fontSize: rem(12),
    backgroundColor: theme.colors.decaLight[1],
  },
}));
const SearchFolderAndKb = ({ multiple = false }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate();
  const [breadcrumb, setBreadcrumb] = useState<string[]>([]);
  const {
    fetchTempBases,
    loading,
    loadingMore,
    loadingSearch,
    searchFolders,
    selectedFolder,
    selectedKb,
    selectedDocuments,
    setBases,
    setSelectedFolder,
    setSelectedKb,
    setSelectedDocuments,
    setTempBases,
    tempBases,
    documents,
    latestSearchCombine,
    fetchDocuments,
  } = useKbWidgetContext();

  const onClickFolder = useCallback(
    (folder: Folder) => {
      setBreadcrumb([]);

      if (selectedFolder === folder.id) {
        setBases([]);
        setBreadcrumb([]);
        setSelectedFolder('');
        setTempBases(
          (latestSearchCombine?.data
            ?.filter((e) => e.type !== 'folder')
            .map((e) => e.data) as Kb[]) || []
        );
        return;
      }

      if (folder.id === ROOT_PATH) {
        setBreadcrumb([t('tree.root')]);
      } else {
        const breadcrumb = get(folder, 'breadcrumb', '')
          ?.replace('/root', t('tree.root'))
          .split('/');
        if (breadcrumb) {
          setBreadcrumb(breadcrumb);
        } else {
          const path = get(folder, 'path', '');
          const breadcrumb = path?.replace('/root', t('tree.root')).split('/');
          if (breadcrumb) {
            setBreadcrumb(breadcrumb);
          }
        }
      }

      fetchTempBases(folder.id);
      fetchDocuments(folder.id);
      setSelectedKb('');
      setSelectedFolder(folder.id);
      return folder.id;
    },
    [selectedFolder, latestSearchCombine]
  );

  const onClickBase = useCallback(
    (base: Kb) => {
      setBreadcrumb([]);
      if (selectedKb === base.id) {
        setBreadcrumb([]);
        setSelectedKb('');
        return;
      }
      KbsAPI.getKbById(base.id).then((res) => {
        setBreadcrumb(
          get(res, 'data.parentDirBreadcrumb', '').replace('/root', t('tree.root')).split('/')
        );
      });
      setSelectedFolder('');
      setSelectedKb(base.id);
    },
    [selectedKb]
  );

  const onClickDocument = useCallback(
    (document: KBDocument) => {
      setBreadcrumb([]);
      if (selectedDocuments.some((doc) => doc.id === document.id)) {
        setSelectedDocuments((prev) => prev.filter((doc) => doc.id !== document.id));
        return;
      }
      setBreadcrumb(
        get(document, 'parentDirBreadcrumb', '').replace('/root', t('tree.root')).split('/')
      );
      setSelectedFolder('');
      setSelectedKb('');
      setSelectedDocuments((prev) => [...prev, document]);
    },
    [selectedDocuments]
  );

  return (
    <Box className={classes.selectContainer}>
      <Flex>
        <Box w={'50%'}>
          <Box p={rem(10)} pr={rem(4)} pb={breadcrumb ? 0 : rem(10)}>
            <Flex className={classes.list}>
              {!loading &&
                searchFolders.length > 0 &&
                searchFolders.map((folder) => (
                  <Flex
                    key={`search-folder-${folder.id}`}
                    className={cx(classes.item, selectedFolder === folder.id && classes.selected)}
                    justify='space-between'
                  >
                    <Flex w='90%' gap={rem(4)} align='center' onClick={() => onClickFolder(folder)}>
                      <Flex w={rem(24)}>
                        <IconFolder color={Colors.decaBlue[5]} />
                      </Flex>
                      <TooltipWithOverflowText text={folder.name} />
                    </Flex>
                    {multiple && <RightItem data={folder} type='folder' />}
                  </Flex>
                ))}
              {!loading && !searchFolders.length && (
                <Flex justify='center' align='center' h='100%'>
                  <NoContent />
                </Flex>
              )}
              {loading && (
                <Flex justify='center' align='center' h='100%'>
                  <Loader />
                </Flex>
              )}
            </Flex>
          </Box>
        </Box>
        <Divider orientation='vertical' />
        <Box w={'50%'}>
          <Box p={rem(10)} pr={rem(4)} pb={breadcrumb ? 0 : rem(10)}>
            <Flex className={classes.list}>
              {!loading &&
                !loadingSearch &&
                tempBases.length > 0 &&
                tempBases.map((base: Kb) => (
                  <Flex
                    key={`search-kb-${base.id}`}
                    className={cx(classes.item, selectedKb === base.id && classes.selected)}
                    justify='space-between'
                  >
                    <Flex w='90%' gap={rem(4)} align='center' onClick={() => onClickBase(base)}>
                      <Flex w={rem(24)}>{renderIcon(base)}</Flex>
                      <TooltipWithOverflowText text={base.name} />
                    </Flex>
                    {multiple && <RightItem data={base} type='kb' />}
                  </Flex>
                ))}
              {!loading &&
                !loadingSearch &&
                documents.length > 0 &&
                documents.map((document: KBDocument) => (
                  <Flex
                    key={`search-document-${document.id}`}
                    className={cx(
                      classes.item,
                      selectedDocuments.some((doc) => doc.id === document.id) && classes.selected
                    )}
                    justify='space-between'
                  >
                    <Flex
                      w='90%'
                      gap={rem(4)}
                      align='center'
                      onClick={() => onClickDocument(document)}
                    >
                      <Flex w={rem(24)}>{renderIcon(document)}</Flex>
                      <TooltipWithOverflowText text={document.metadata?.name} />
                    </Flex>
                    {multiple && <RightItem data={document} type='document' />}
                  </Flex>
                ))}
              {!loading && !loadingSearch && !tempBases.length && !documents.length && (
                <Flex justify='center' align='center' h='100%'>
                  <NoContent />
                </Flex>
              )}
              {(loading || loadingMore || loadingSearch) && (
                <Flex justify='center' align='center' h='100%'>
                  <Loader />
                </Flex>
              )}
            </Flex>
          </Box>
        </Box>
      </Flex>
      <If condition={breadcrumb !== undefined}>
        <Tooltip label={breadcrumb?.join(' / ')} disabled={!breadcrumb?.length}>
          <Flex className={classes.pathContainer} px={rem(10)}>
            {breadcrumb?.map((e, index) => (
              <Fragment key={`breadcrumb-${index}`}>
                <Text lineClamp={1}>{e}</Text>
                {index !== breadcrumb.length - 1 && <IconChevronRight size={18} />}
              </Fragment>
            ))}
          </Flex>
        </Tooltip>
      </If>
    </Box>
  );
};

export default SearchFolderAndKb;
