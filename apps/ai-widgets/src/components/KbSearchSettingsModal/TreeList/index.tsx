import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { type NodeApi, Tree } from 'react-arborist';
import type { TreeProps } from 'react-arborist/dist/module/types/tree-props';

import type { TreeItem, Trees } from '@/types';
import TreeLeaf from './TreeLeaf';

interface TreeListProps extends TreeProps<TreeItem> {
  tree?: Trees;
  rightItem?: (node: NodeApi<TreeItem>) => React.ReactNode;
  onPrefetchChildFolders?: (nodeId: string) => void;
}

const useStyles = createStyles((theme) => ({
  tree: {
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
}));

const TreeList: React.FC<TreeListProps> = ({ tree, rightItem }) => {
  const { classes } = useStyles();

  return (
    <Tree
      className={classes.tree}
      data={tree}
      openByDefault={false}
      rowHeight={32}
      width={'100%'}
      height={260}
      disableMultiSelection
    >
      {({ node, style, dragHandle }) => (
        <TreeLeaf node={node} style={style} dragHandle={dragHandle} rightItem={rightItem} />
      )}
    </Tree>
  );
};

export default TreeList;
