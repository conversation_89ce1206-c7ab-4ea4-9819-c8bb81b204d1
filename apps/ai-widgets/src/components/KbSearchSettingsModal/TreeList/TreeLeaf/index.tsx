import { type CSSProperties, useCallback, useRef } from 'react';
import { Box, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown, IconChevronRight, IconFolder } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import type { NodeApi } from 'react-arborist';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';

import type { TreeItem } from '@/types';
import { useKbWidgetContext } from '../../../../contexts/KbWidgetContext';
import { IconFolderEmpty } from '../../Icons/IconFolderEmpty';

const MAX_DEPTH = 5;

interface TreeLeafProps<T> {
  node: NodeApi<T>;
  style: CSSProperties;
  rightItem?: (node: NodeApi<TreeItem>) => React.ReactNode;
  dragHandle?: (el: HTMLDivElement | null) => void;
  onPrefetchChildFolders?: (nodeId: string) => void;
}

const useStyles = createStyles((theme) => ({
  leaf: {
    height: rem(32),
    display: 'flex',
    alignItems: 'center',
    cursor: 'pointer',
    borderRadius: rem(4),
    color: theme.colors.decaNavy[5],
    border: `${rem(1)} solid transparent`,
    '&:hover': {
      background: theme.colors.decaViolet[0],
    },
  },
  selected: {
    background: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
  text: {
    color: theme.colors.decaMono[0],
    fontSize: theme.fontSizes.md,
    fontWeight: 400,
  },
}));

const TreeArrow: React.FC<{ node: NodeApi }> = ({ node }) => {
  if (!node.data.childFolderCount) return <Box component='span' w={rem(18)} display='flex' />;
  return (
    <Box component='span' display='flex'>
      {node.isOpen ? <IconChevronDown size={18} /> : <IconChevronRight size={18} />}
    </Box>
  );
};

const TreeLeaf: React.FC<TreeLeafProps<TreeItem>> = ({ node, style, rightItem, dragHandle }) => {
  const { t } = useTranslate();
  const { classes, cx } = useStyles();
  const {
    fetchFolders,
    fetchBases,
    fetchDocuments,
    setSelectedKb,
    selectedFolder,
    setSelectedFolder,
    setBases,
  } = useKbWidgetContext();
  const hasCalledFetchFolders = useRef(false);

  const onHover = useCallback(() => {
    if (
      !hasCalledFetchFolders.current &&
      node.data.childFolderCount &&
      node.level < MAX_DEPTH &&
      !node.data.children?.length
    ) {
      fetchFolders(node.data.id, 1);
      hasCalledFetchFolders.current = true;
    }
  }, [node, fetchFolders, hasCalledFetchFolders.current]);

  const onClick = () => {
    fetchFolders(node.data.id, 1);
    setSelectedFolder(node.data.id);
    if (selectedFolder !== node.data.id) {
      setBases([]);
      setSelectedKb('');
    }
    if (node.data.id !== selectedFolder) {
      fetchBases(node.data.id);
      fetchDocuments(node.data.id);
    }
    node.toggle();
  };

  return (
    <Flex
      className={cx(classes.leaf, node.isSelected && classes.selected)}
      style={style}
      ref={dragHandle}
      gap={rem(4)}
      onMouseEnter={onHover}
      justify='space-between'
      maw={rem(262)}
      key={`tree-leaf-${node.data.id}`}
    >
      <Flex w='90%' gap={rem(4)} align='center' onClick={onClick}>
        <Flex gap={rem(4)} align='center'>
          <TreeArrow node={node} />
          {node.data.childKbCount || node.data.childFolderCount ? (
            <IconFolder size={24} />
          ) : (
            <IconFolderEmpty />
          )}
        </Flex>
        <TooltipWithOverflowText text={t(node.data.name)} />
      </Flex>
      {rightItem?.(node)}
    </Flex>
  );
};

export default TreeLeaf;
