import { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, Flex, Input, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { DecaButton, SearchInput } from '@resola-ai/ui';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';
import debounce from 'lodash/debounce';

import type { WidgetInstance } from '@/types';
import { ROOT_FOLDER, ROOT_PATH } from '@/constants/kb';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { useAppContext } from '@/contexts/AppContext';
import { KbWidgetContextProvider, useKbWidgetContext } from '@/contexts/KbWidgetContext';
import { useFormStyles } from '@/constants/styles';
import { kbSettingSchema } from '@/schemas/setting';
import { getSelectedKbAndFolderName } from '@/utils/kb';
import { DescriptionField, NameField, StatusField } from '../DefaultSettingsModal/Fields';
import SearchFolderAndKb from './SearchFolderAndKb';
import SelectFolderAndKb from './SelectFolderAndKb';
import SelectedFolderAndKb from './SelectedFolderAndKb';
import KbFolderDeletedError from './KbFolderDeletedError';

interface KbSearchSettingsModal extends ConfirmModalProps {
  settings?: WidgetInstance;
}

const useStyles = createStyles(() => ({
  contentWrapper: {
    display: 'flex',
    flexDirection: 'column',
  },
  modalContent: {
    minWidth: rem(920),
  },
}));

const KbSearchSettingsModal: React.FC<KbSearchSettingsModal> = ({
  settings,
  onClose,
  ...props
}) => {
  const { classes: currentClasses } = useStyles();
  const { t } = useTranslate();
  const { classes, cx } = useFormStyles();
  const {
    control,
    getValues,
    setValue,
    formState: { errors, isSubmitting },
    handleSubmit,
    watch,
    reset,
  } = useForm({
    defaultValues: settings ?? {
      description: '',
      settings: {
        searchFolders: [ROOT_PATH],
        searchBases: [],
        searchDocuments: [],
      },
      status: 'public',
    },
    resolver: zodResolver(kbSettingSchema),
  });
  const {
    allBases,
    allDocuments,
    cleanup,
    deletedFolderIds,
    deletedKbIds,
    fetchBases,
    fetchDocuments,
    fetchFolderByIds,
    fetchFolders,
    fetchKbsByIds,
    folders,
    loading,
    searchCombineFolderAndKb,
    selectedFolders,
    selectedKbs,
    selectedDocuments,
    setSelectedFolders,
    setSelectedKbs,
    setSelectedDocuments,
    fetchDocumentsByIds,
  } = useKbWidgetContext();
  const {
    widgets,
    widgetInstances,
    installWidgetInstance,
    updateWidgetInstance,
    updateWidgetInstanceSettings,
  } = useWidgetContext();
  const { openConfirmModal } = useAppContext();

  const [isFolderState, setIsFolderState] = useState<boolean>(false);
  const [textSearch, setTextSearch] = useState<string>('');
  const [initPhase, setInitPhase] = useState<boolean>(true);

  const fetchCurrentKb = useCallback(
    (searchBases: string[]) => {
      if (searchBases.length) {
        fetchKbsByIds(searchBases);
      }
    },
    [fetchKbsByIds]
  );

  const fetchCurrentFolder = useCallback(
    (searchFolders: string[]) => {
      if (searchFolders.length) {
        const folderIds = searchFolders.filter((folderId) => folderId !== ROOT_PATH);
        const hasRootFolder = searchFolders.includes(ROOT_PATH);

        if (hasRootFolder) {
          setSelectedFolders((prev) =>
            prev.some((folder) => folder.id === ROOT_FOLDER.id) ? prev : [...prev, ROOT_FOLDER]
          );
        }

        if (folderIds.length) {
          fetchFolderByIds(folderIds);
        }
      }
    },
    [fetchFolderByIds]
  );

  const fetchCurrentDocument = useCallback(
    (searchDocuments: string[]) => {
      if (searchDocuments.length) {
        fetchDocumentsByIds(searchDocuments);
      }
    },
    [fetchDocumentsByIds]
  );
  useEffect(() => {
    fetchFolders(ROOT_PATH, 1);
    fetchBases();
    fetchDocuments(ROOT_PATH);
  }, []);

  useEffect(() => {
    if (settings) {
      const searchFolders = settings?.settings?.searchFolders ?? [];
      const searchBases = settings?.settings?.searchBases ?? [];
      const searchDocuments = settings?.settings?.searchDocuments ?? [];
      setValue('name', settings.name);
      setValue('description', settings.description);
      setValue('settings.searchFolders', searchFolders);
      setValue('settings.searchBases', searchBases);
      setValue('settings.searchDocuments', searchDocuments);
      setValue('status', settings.status);

      fetchCurrentKb(searchBases);
      fetchCurrentFolder(searchFolders);
      fetchCurrentDocument(searchDocuments);
    }
  }, [settings]);

  useEffect(() => {
    if (selectedFolders) {
      setValue(
        'settings.searchFolders',
        selectedFolders.map((f) => f.id)
      );
    }
  }, [selectedFolders]);

  useEffect(() => {
    if (selectedKbs) {
      setValue(
        'settings.searchBases',
        selectedKbs.map((kb) => kb.id)
      );
    }
  }, [selectedKbs]);

  useEffect(() => {
    if (selectedDocuments) {
      setValue(
        'settings.searchDocuments',
        selectedDocuments.map((doc) => doc.id)
      );
    }
  }, [selectedDocuments]);

  const handleOnClose = useCallback(() => {
    if (isFolderState) {
      setIsFolderState(false);

      const searchFolders = widgetInstances.find((w) => w.id === settings?.id)?.settings
        ?.searchFolders;
      const searchBases = widgetInstances.find((w) => w.id === settings?.id)?.settings?.searchBases;
      const searchDocuments = widgetInstances.find((w) => w.id === settings?.id)?.settings
        ?.searchDocuments;
      const searchFoldersData = folders?.filter((f) => searchFolders?.includes(f.id));
      const searchBasesData = allBases?.filter((b) => searchBases?.includes(b.id));
      const searchDocumentsData = allDocuments?.filter((d) => searchDocuments?.includes(d.id));

      setSelectedFolders(searchFoldersData);
      setSelectedKbs(searchBasesData);
      setSelectedDocuments(searchDocumentsData);
      return;
    }

    onClose();
    setTimeout(() => {
      reset();
      setIsFolderState(false);
      setTextSearch('');
      setInitPhase(true);
      cleanup();
    }, 200);
  }, [isFolderState, settings, folders, allBases, allDocuments, widgetInstances]);

  const onSubmit = useCallback(async () => {
    if (isFolderState) {
      setIsFolderState(false);

      return;
    }

    try {
      if (settings?.id) {
        const { settings: settingValue, ...rest } = getValues();

        const updatedSettings = {
          ...settingValue,
          searchFolders: settingValue?.searchFolders.length ? settingValue.searchFolders : [],
        };

        await updateWidgetInstance(settings.id, rest);
        await updateWidgetInstanceSettings(settings.id, updatedSettings);
      } else {
        await installWidgetInstance(
          widgets.find((w) => w.type === 'KB_SEARCH')?.id ?? '',
          getValues()
        );
      }

      onClose();
      openConfirmModal({
        content: t('modal.success.settings.saved'),
        cancelText: t('common.button.close'),
        options: {
          isShowConfirm: false,
        },
      });
    } catch (error) {
      onClose();
      if ((error as Error).message === 'Duplicated') {
        openConfirmModal({
          content: t('kb.error.duplicated'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } else if ((error as Error).message === 'Limited') {
        openConfirmModal({
          content: t('kb.error.limited'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      }
    } finally {
      handleOnClose();
    }
  }, [isFolderState, settings]);

  const handleSearch = useCallback(
    debounce((value: string) => {
      if (loading || textSearch === value) return;
      setTextSearch(value);
      searchCombineFolderAndKb(value);
    }, 500),
    [loading, textSearch]
  );

  const isContainDeletedKbAndFolder = useMemo(() => {
    return deletedFolderIds.length > 0 || deletedKbIds.length > 0;
  }, [deletedFolderIds, deletedKbIds]);

  const kbAndFolderName = getSelectedKbAndFolderName(
    watch('settings.searchBases'),
    watch('settings.searchFolders'),
    watch('settings.searchDocuments'),
    folders,
    allBases,
    allDocuments,
    t
  );

  return (
    <ConfirmModal
      cancelText={isFolderState ? t('modal.confirm.backBtn') : t('modal.confirm.cancelBtn')}
      confirmText={t('common.button.save')}
      onConfirm={handleSubmit(onSubmit)}
      title={isFolderState ? t('kb.selectFolderAndKb') : t('kb.title')}
      classNames={{
        content: isFolderState ? currentClasses.modalContent : classes.modalContent,
        title: classes.modalTitle,
        close: classes.modalClose,
      }}
      confirmButtonProps={{
        disabled:
          (!watch('name') && !isFolderState) ||
          (!selectedFolders?.length &&
            !selectedKbs?.length &&
            !selectedDocuments?.length &&
            isFolderState) ||
          (isContainDeletedKbAndFolder && initPhase) ||
          isSubmitting,
      }}
      cancelButtonProps={{
        disabled: isSubmitting,
      }}
      onClose={handleOnClose}
      {...props}
    >
      <form>
        <Box className={cx(classes.root, isFolderState && classes.hide)}>
          <NameField control={control} errors={errors} />
          <DescriptionField control={control} errors={errors} />
          <Input.Wrapper
            label={t('kb.fields.searchFolders.label')}
            classNames={{ label: classes.topLabel }}
          >
            <Flex
              className={classes.inputInner}
              direction='column'
              gap={kbAndFolderName ? rem(16) : 0}
            >
              <Text sx={{ whiteSpace: 'pre-line' }}>{kbAndFolderName}</Text>
              <KbFolderDeletedError show={isContainDeletedKbAndFolder && initPhase} />
              <DecaButton
                size='sm'
                variant='primary'
                type='button'
                onClick={() => {
                  setInitPhase(false);
                  setIsFolderState(true);
                }}
              >
                {t('kb.fields.searchFolders.change')}
              </DecaButton>
            </Flex>
          </Input.Wrapper>
          <StatusField watch={watch} setValue={setValue} />
        </Box>
        <Box className={cx(classes.root, !isFolderState && classes.hide)}>
          <SearchInput
            className={classes.searchBox}
            onChange={(value) => handleSearch(value)}
            onClear={() => handleSearch('')}
            placeholder={t('kb.searchFolderAndKb')}
          />
          <Flex justify={'space-between'} w='100%'>
            <Box className={currentClasses.contentWrapper} w='66%'>
              <Text fw={500} mb={rem(8)}>
                {t('kb.foldersAndKbs')}
              </Text>
              {textSearch ? (
                <SearchFolderAndKb multiple={true} />
              ) : (
                <SelectFolderAndKb multiple={true} />
              )}
            </Box>
            <Box className={currentClasses.contentWrapper} w='33%'>
              <Text fw={500} mb={rem(8)}>
                {t('kb.selectedFoldersAndKbs')}
              </Text>
              <SelectedFolderAndKb />
            </Box>
          </Flex>
        </Box>
      </form>
    </ConfirmModal>
  );
};

const KbSearchSettingsModalContainer = (props: KbSearchSettingsModal) => {
  return (
    <KbWidgetContextProvider>
      <KbSearchSettingsModal {...props} />
    </KbWidgetContextProvider>
  );
};

export default KbSearchSettingsModalContainer;
