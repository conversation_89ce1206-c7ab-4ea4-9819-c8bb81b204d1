import { useRef, useEffect } from 'react';
import { <PERSON>, Divider, Flex, Loader, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useIntersection } from '@mantine/hooks';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import { nanoid } from 'nanoid';
import type { NodeApi } from 'react-arborist';

import type { Folder, Kb, KBDocument, TreeItem } from '@/types';
import { convertListToTree } from '@/utils/kb';
import { NoContent } from '../NoContent';
import { useKbWidgetContext } from '../../../contexts/KbWidgetContext';
import TreeList from '../TreeList';
import RightItem from '../RightItem';
import { renderIcon } from '@/components/FileTypeIcons';

const useStyles = createStyles((theme) => ({
  selectContainer: {
    minHeight: rem(309),
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: rem(4),
    border: `${rem(0.5)} solid ${theme.colors.silverFox[7]}`,
    display: 'flex',
    justifyContent: 'space-between',
  },
  kbContainer: {
    padding: rem(10),
    paddingRight: rem(4),
  },
  listKb: {
    display: 'flex',
    flexDirection: 'column',
    height: rem(270),
    overflowY: 'auto',
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
  kbItem: {
    padding: rem(4),
    gap: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    border: `${rem(1)} solid transparent`,
    ':hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  selected: {
    backgroundColor: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
}));
const SelectFolderAndKb = ({ multiple = false }) => {
  const { classes, cx } = useStyles();
  const {
    folders,
    basesAndDocuments,
    selectedKb,
    loading,
    loadingMore,
    setSelectedKb,
    fetchMoreKnowledgeBases,
    fetchMoreDocuments,
  } = useKbWidgetContext();
  const containerRef = useRef(null);

  const { ref: bottomRef, entry: bottomEntry } = useIntersection({
    root: containerRef.current,
    threshold: 1,
  });

  useEffect(() => {
    if (containerRef?.current && bottomEntry?.isIntersecting) {
      fetchMoreKnowledgeBases();
      fetchMoreDocuments();
    }
  }, [bottomEntry, containerRef?.current, fetchMoreKnowledgeBases, fetchMoreDocuments]);

  const renderRightItem = (node: NodeApi<TreeItem>) => {
    return <RightItem data={node.data as Folder} type='folder' />;
  };

  return (
    <Box className={classes.selectContainer}>
      <Box w={'50%'} p={rem(10)} pr={rem(4)}>
        <TreeList
          tree={convertListToTree(folders)}
          rightItem={multiple ? renderRightItem : undefined}
        />
      </Box>
      <Divider orientation='vertical' />
      <Box w={'50%'} className={classes.kbContainer}>
        <Flex ref={containerRef} className={classes.listKb}>
          {!loading &&
            basesAndDocuments.length > 0 &&
            basesAndDocuments.map((base: Kb | KBDocument) => {
              const isDocument = 'metadata' in base;
              return (
                <Flex
                  key={`kb-${base.id}-${nanoid(8)}`}
                  className={cx(classes.kbItem, selectedKb === base.id && classes.selected)}
                  justify='space-between'
                >
                  <Flex w='90%' gap={rem(4)} align='center' onClick={() => setSelectedKb(base.id)}>
                    <Flex w={rem(24)}>{renderIcon(base)}</Flex>
                    <TooltipWithOverflowText text={isDocument ? base.metadata.name : base.name} />
                  </Flex>
                  {multiple && <RightItem data={base} type={isDocument ? 'document' : 'kb'} />}
                </Flex>
              );
            })}
          <Box mih={1}>
            {!loadingMore && !loading && basesAndDocuments.length > 0 && <Box ref={bottomRef} />}
          </Box>
          {!loading && !basesAndDocuments.length && (
            <Flex justify='center' align='center' h='100%'>
              <NoContent />
            </Flex>
          )}
          {(loading || loadingMore) && (
            <Flex justify='center' align='center' h='100%'>
              <Loader />
            </Flex>
          )}
        </Flex>
      </Box>
    </Box>
  );
};

export default SelectFolderAndKb;
