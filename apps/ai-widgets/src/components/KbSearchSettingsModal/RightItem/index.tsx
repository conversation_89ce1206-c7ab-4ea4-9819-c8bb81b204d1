import { useMemo } from 'react';
import { Flex, rem } from '@mantine/core';

import type { Kb, Folder, KBDocument } from '@/types';
import { useKbWidgetContext } from '@/contexts/KbWidgetContext';
import CircleCheckFilled from '../Icons/CircleCheckFilled';
import CirclePlusFilled from '../Icons/CirclePlusFilled';

const RightItem = ({
  data,
  type,
}: {
  data: Kb | Folder | KBDocument;
  type: 'folder' | 'kb' | 'document';
}) => {
  const {
    selectedKbs,
    selectedFolders,
    selectedDocuments,
    setSelectedKbs,
    setSelectedDocuments,
    setSelectedFolders,
  } = useKbWidgetContext();

  const isRemove = useMemo(() => {
    return (
      selectedKbs.find((e) => e.id === data.id) ||
      selectedFolders.find((e) => e.id === data.id) ||
      selectedDocuments.find((e) => e.id === data.id)
    );
  }, [selectedKbs, selectedFolders, selectedDocuments]);
  return (
    <Flex
      w={rem(24)}
      justify='flex-end'
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        if (type === 'folder') {
          setSelectedFolders((prev) => {
            if (isRemove) {
              return prev.filter((e) => e.id !== data.id);
            }
            return [...prev, data as Folder];
          });
        }
        if (type === 'kb') {
          setSelectedKbs((prev) => {
            if (isRemove) {
              return prev.filter((e) => e.id !== data.id);
            }
            return [...prev, data as Kb];
          });
        }
        if (type === 'document') {
          setSelectedDocuments((prev) => {
            if (isRemove) {
              return prev.filter((e) => e.id !== data.id);
            }
            return [...prev, data as KBDocument];
          });
        }
      }}
    >
      {isRemove ? <CircleCheckFilled /> : <CirclePlusFilled />}
    </Flex>
  );
};

export default RightItem;
