import { Fragment, useEffect, useState } from 'react';
import { Box, Flex, rem, Text, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronRight, IconFolder } from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { Colors } from '@resola-ai/ui/constants/themeConfiguration';
import { TooltipWithOverflowText } from '@resola-ai/ui/components';
import get from 'lodash/get';

import { KbsAPI } from '@/services/api/kbs';
import { ROOT_PATH } from '@/constants/kb';
import { useKbWidgetContext } from '@/contexts/KbWidgetContext';
import CircleMinusFilled from '../Icons/CircleMinusFilled';
import { useKbFolder } from '@/hooks/useKbFolder';
import { useKbDocument } from '@/hooks/useKbDocument';
import { renderIcon } from '@/components/FileTypeIcons';

const useStyles = createStyles((theme) => ({
  selectContainer: {
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: rem(4),
    border: `${rem(0.5)} solid ${theme.colors.silverFox[7]}`,
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'space-between',
  },
  listKb: {
    display: 'flex',
    flexDirection: 'column',
    overflowY: 'auto',
    scrollbarGutter: 'stable',
    '&::-webkit-scrollbar': {
      width: rem(8),
    },
    '&::-webkit-scrollbar-thumb': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    '&::-webkit-scrollbar-track': {
      backgroundColor: 'transparent',
      borderRadius: rem(4),
    },
    ':hover': {
      '&::-webkit-scrollbar-thumb': {
        backgroundColor: theme.colors.decaLight[5],
      },
    },
  },
  kbItem: {
    height: rem(32),
    padding: rem(4),
    gap: rem(4),
    cursor: 'pointer',
    borderRadius: rem(4),
    alignItems: 'center',
    border: `${rem(1)} solid transparent`,
    ':hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
  },
  selected: {
    backgroundColor: theme.colors.decaViolet[0],
    borderColor: theme.colors.decaViolet[5],
  },
  pathContainer: {
    height: rem(24),
    alignItems: 'center',
    color: theme.colors.decaGrey[4],
    fontSize: rem(12),
    backgroundColor: theme.colors.decaLight[1],
  },
}));

const SelectedFolderAndKb = ({ isView = false, h = 294 }: { isView?: boolean; h?: number }) => {
  const { classes, cx } = useStyles();
  const { t } = useTranslate();
  const [selected, setSelected] = useState<{
    id: string;
    type: string;
  }>();

  const [breadcrumb, setBreadcrumb] = useState<string[]>([]);
  const {
    selectedKbs,
    selectedFolders,
    selectedDocuments,
    setSelectedFolders,
    setSelectedKbs,
    setSelectedDocuments,
  } = useKbWidgetContext();
  const { data: selectedFolder } = useKbFolder(selected?.id ?? '');
  const { data: selectedDocument } = useKbDocument(selected?.id ?? '');
  useEffect(() => {
    if (!selected) setBreadcrumb([]);

    if (selected && selected.type === 'kb') {
      KbsAPI.getKbById(selected.id).then((res) => {
        setBreadcrumb(
          get(res, 'data.parentDirBreadcrumb', '').replace('/root', t('tree.root')).split('/')
        );
      });
    }

    if (selected && selected.type === 'folder') {
      if (selected.id === ROOT_PATH) {
        setBreadcrumb([t('tree.root')]);
      } else {
        setBreadcrumb(
          get(selectedFolder, 'breadcrumb', '').replace('/root', t('tree.root')).split('/')
        );
      }
    }
    if (selected && selected.type === 'document') {
      setBreadcrumb(
        get(selectedDocument, 'parentDirBreadcrumb', '').replace('/root', t('tree.root')).split('/')
      );
    }
  }, [t, selected, selectedFolder, selectedDocument]);

  return (
    <Box className={classes.selectContainer} mih={rem(h)}>
      <Box p={rem(10)} pr={rem(4)} pb={breadcrumb ? 0 : rem(10)}>
        <Flex className={classes.listKb} h={rem(h - 20)}>
          <>
            {selectedFolders.map((item, index) => (
              <Flex
                key={`selected-folder-${item.id ? (item.id !== '/root' ? item.id : `root-${Math.random() * 100000}`) : index}`}
                className={cx(classes.kbItem, selected?.id === item.id && classes.selected)}
                justify='space-between'
              >
                <Flex
                  w='90%'
                  gap={rem(4)}
                  align='center'
                  onClick={() => {
                    if (isView) return;
                    setSelected((prev) => {
                      if (prev?.id === item.id) return undefined;
                      return {
                        id: item.id,
                        type: 'folder',
                      };
                    });
                  }}
                >
                  <Flex w={rem(24)}>
                    <IconFolder color={Colors.decaNavy[5]} />
                  </Flex>
                  <TooltipWithOverflowText text={t(item.name)} />
                </Flex>
                {!isView && (
                  <Flex
                    w={rem(24)}
                    justify='flex-end'
                    onClick={() => {
                      setSelected(() => undefined);
                      setSelectedFolders((prev) => [...prev.filter((e) => e.id !== item.id)]);
                    }}
                  >
                    <CircleMinusFilled />
                  </Flex>
                )}
              </Flex>
            ))}
            {selectedKbs.map((item, index) => (
              <Flex
                key={`selected-kb-${item.id || index}`}
                className={cx(classes.kbItem, selected?.id === item.id && classes.selected)}
                justify='space-between'
              >
                <Flex
                  w='90%'
                  gap={rem(4)}
                  align='center'
                  onClick={() => {
                    if (isView) return;
                    setSelected((prev) => {
                      if (prev?.id === item.id) return undefined;
                      return {
                        id: item.id,
                        type: 'kb',
                      };
                    });
                  }}
                >
                  <Flex w={rem(24)}>{renderIcon(item)}</Flex>
                  <TooltipWithOverflowText text={item.name} />
                </Flex>
                {!isView && (
                  <Flex
                    w={rem(24)}
                    justify='flex-end'
                    onClick={() => {
                      setSelected(undefined);
                      setSelectedKbs((prev) => [...prev.filter((e) => e.id !== item.id)]);
                    }}
                  >
                    <CircleMinusFilled />
                  </Flex>
                )}
              </Flex>
            ))}
            {selectedDocuments.map((item, index) => (
              <Flex
                key={`selected-document-${item.id || index}`}
                className={cx(classes.kbItem, selected?.id === item.id && classes.selected)}
                justify='space-between'
              >
                <Flex
                  w='90%'
                  gap={rem(4)}
                  align='center'
                  onClick={() => {
                    if (isView) return;
                    setSelected((prev) => {
                      if (prev?.id === item.id) return undefined;
                      return {
                        id: item.id,
                        type: 'document',
                      };
                    });
                  }}
                >
                  <Flex w={rem(24)}>{renderIcon(item)}</Flex>
                  <TooltipWithOverflowText text={item.metadata.name} />
                </Flex>
                {!isView && (
                  <Flex
                    w={rem(24)}
                    justify='flex-end'
                    onClick={() => {
                      setSelected(undefined);
                      setSelectedDocuments((prev) => [...prev.filter((e) => e.id !== item.id)]);
                    }}
                  >
                    <CircleMinusFilled />
                  </Flex>
                )}
              </Flex>
            ))}
          </>
        </Flex>
      </Box>
      {!isView && (
        <Tooltip label={breadcrumb.join(' / ')} disabled={!breadcrumb.length}>
          <Flex className={classes.pathContainer} px={rem(10)}>
            {breadcrumb?.map((e, index) => (
              <Fragment key={`breadcrumb-${index}`}>
                <Text lineClamp={1}>{e}</Text>
                {index !== breadcrumb.length - 1 && <IconChevronRight size={18} />}
              </Fragment>
            ))}
          </Flex>
        </Tooltip>
      )}
    </Box>
  );
};

export default SelectedFolderAndKb;
