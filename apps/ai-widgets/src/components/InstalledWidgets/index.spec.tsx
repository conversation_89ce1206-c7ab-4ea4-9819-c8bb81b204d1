import { screen, waitFor } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';

import { renderWithMantine } from '@/utils/test';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { useAppContext } from '@/contexts/AppContext';
import { isCustomWidget, isLatestCompilationWidgetInstance } from '@/utils/widget';
import useSettingsModal from '@/hooks/useSettingsModal';
import InstalledWidgets from './index';

// Mock the hooks
vi.mock('@/contexts/WidgetContext', () => ({
  WidgetContextProvider: ({ children }) => children,
  useWidgetContext: vi.fn(),
}));

vi.mock('@/contexts/AppContext', () => ({
  AppContextProvider: ({ children }) => children,
  useAppContext: vi.fn(),
}));

vi.mock('@/utils/widget', () => ({
  isCustomWidget: vi.fn(),
  isLatestCompilationWidgetInstance: vi.fn(),
}));

vi.mock('@/hooks/useSettingsModal', () => ({
  default: vi.fn(),
}));

vi.mock('@resola-ai/services-shared', () => ({
  executeRequest: vi.fn(),
}));

describe('InstalledWidgets Component', () => {
  const user = userEvent.setup();
  const mockRemoveWidget = vi.fn();
  const mockUpdateWidget = vi.fn();
  const mockSyncCustomWidgetInstanceCompilation = vi.fn();
  const mockOpenConfirmModal = vi.fn();
  const mockCloseConfirmModal = vi.fn();
  const mockSetConfirmModalLoading = vi.fn();
  const mockOpenModal = vi.fn();
  const mockCloseModal = vi.fn();

  const mockWidgetInstances = [
    {
      id: 'widget1',
      name: 'Knowledge Base Search',
      description: 'Search your knowledge base',
      widgetType: 'KB_SEARCH',
      status: 'public',
      widget: {
        name: 'KB Search',
      },
    },
    {
      id: 'widget2',
      name: 'Reply Assistant',
      description: 'Get AI-powered replies',
      widgetType: 'REPLY_ASSISTANT',
      status: 'private',
      widget: {
        name: 'Reply Assistant',
      },
    },
    {
      id: 'widget3',
      name: 'Custom Widget',
      description: 'A custom widget',
      widgetType: 'BROWSER_EXTENSION',
      status: 'public',
      widgetId: 'custom-widget-1',
      isCustom: true,
      settings: {
        compilationName: 'v1.0.0',
      },
      widget: {
        name: 'Custom Widget',
      },
    },
    {
      id: 'widget4',
      name: 'Outdated Custom Widget',
      description: 'An outdated custom widget',
      widgetType: 'BROWSER_EXTENSION',
      status: 'public',
      widgetId: 'custom-widget-2',
      isCustom: true,
      settings: {
        compilationName: 'v0.9.0',
      },
      widget: {
        name: 'Outdated Custom Widget',
      },
    },
  ];

  const mockCustomWidgets = [
    {
      id: 'custom-widget-1',
      name: 'Custom Widget',
    },
    {
      id: 'custom-widget-2',
      name: 'Outdated Custom Widget',
    },
  ];
  const consoleError = console.error;

  beforeEach(() => {
    console.error = vi.fn();
    vi.clearAllMocks();

    // Reset all mock implementations
    mockOpenConfirmModal.mockReset();
    mockRemoveWidget.mockReset();
    mockUpdateWidget.mockReset();
    mockSyncCustomWidgetInstanceCompilation.mockReset();
    mockCloseConfirmModal.mockReset();
    mockSetConfirmModalLoading.mockReset();
    mockOpenModal.mockReset();
    mockCloseModal.mockReset();
    (useWidgetContext as any).mockReturnValue({
      widgetInstances: mockWidgetInstances,
      removeWidgetInstance: mockRemoveWidget,
      updateWidgetInstance: mockUpdateWidget,
      customWidgets: mockCustomWidgets,
      syncCustomWidgetInstanceCompilation: mockSyncCustomWidgetInstanceCompilation,
    });
    (useAppContext as any).mockReturnValue({
      openConfirmModal: mockOpenConfirmModal,
      closeConfirmModal: mockCloseConfirmModal,
      setConfirmModalLoading: mockSetConfirmModalLoading,
    });
    (useSettingsModal as any).mockReturnValue({
      Modal: ({ children }) => <div data-testid='settings-modal'>{children}</div>,
      opened: false,
      close: mockCloseModal,
      open: mockOpenModal,
    });

    // Setup isCustomWidget mock to differentiate between standard and custom widgets
    (isCustomWidget as any).mockImplementation((type) => type === 'BROWSER_EXTENSION');

    // Setup isLatestCompilationWidgetInstance mock
    (isLatestCompilationWidgetInstance as any).mockImplementation((_rootWidget, widgetInstance) => {
      // Mock widget3 as latest compilation, widget4 as outdated
      return widgetInstance.id === 'widget3';
    });
  });

  afterAll(() => {
    console.error = consoleError;
  });

  it('renders all widget instances initially', () => {
    renderWithMantine(<InstalledWidgets />);

    const widgets = screen.getAllByTestId('widget-box');
    expect(widgets).toHaveLength(4);
    expect(widgets[0]).toHaveTextContent('Knowledge Base Search');
    expect(widgets[1]).toHaveTextContent('Reply Assistant');
    expect(widgets[2]).toHaveTextContent('Custom Widget');
    expect(widgets[3]).toHaveTextContent('Outdated Custom Widget');
  });

  it('filters widgets when filter changes', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Initially shows all widgets
    let widgets = screen.queryAllByTestId('widget-box');
    expect(widgets).toHaveLength(4);
    expect(widgets[0]).toHaveTextContent('Knowledge Base Search');
    expect(widgets[1]).toHaveTextContent('Reply Assistant');
    expect(widgets[2]).toHaveTextContent('Custom Widget');
    expect(widgets[3]).toHaveTextContent('Outdated Custom Widget');

    // Click on Standard filter
    await user.click(screen.getByText('filters.standard'));

    widgets = screen.queryAllByTestId('widget-box');
    // Should now only show standard widgets
    expect(widgets).toHaveLength(2);
    expect(widgets[0]).toHaveTextContent('Knowledge Base Search');
    expect(widgets[1]).toHaveTextContent('Reply Assistant');

    // Click on Custom filter
    await user.click(screen.getByText('filters.custom'));

    widgets = screen.queryAllByTestId('widget-box');
    // Should now only show custom widgets
    expect(widgets).toHaveLength(2);
    expect(widgets[0]).toHaveTextContent('Custom Widget');
    expect(widgets[1]).toHaveTextContent('Outdated Custom Widget');
  });

  it('shows message when there are no widgets', () => {
    (useWidgetContext as any).mockReturnValue({
      widgetInstances: [],
      removeWidgetInstance: mockRemoveWidget,
      updateWidgetInstance: mockUpdateWidget,
      customWidgets: [],
      syncCustomWidgetInstanceCompilation: mockSyncCustomWidgetInstanceCompilation,
    });

    renderWithMantine(<InstalledWidgets />);

    expect(screen.getByText('settings.installed.noWidgets')).toBeInTheDocument();
  });

  it('searches widgets correctly', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Type in search box
    const searchInput = screen.getByPlaceholderText('settings.installed.search.placeholder');
    await user.type(searchInput, 'knowledge');

    // Click search button
    await user.click(screen.getByText('settings.installed.search.button'));

    // Should only show KB Search widget
    expect(screen.getByText('Knowledge Base Search')).toBeInTheDocument();
    expect(screen.queryByText('Reply Assistant')).not.toBeInTheDocument();
    expect(screen.queryByText('Custom Widget')).not.toBeInTheDocument();

    // Clear search
    await user.clear(searchInput);
    await user.click(screen.getByText('settings.installed.search.button'));

    // Should show all widgets again
    await waitFor(() => {
      const widgets = screen.queryAllByTestId('widget-box');
      expect(widgets).toHaveLength(4);
      expect(widgets[0]).toHaveTextContent('Knowledge Base Search');
      expect(widgets[1]).toHaveTextContent('Reply Assistant');
      expect(widgets[2]).toHaveTextContent('Custom Widget');
      expect(widgets[3]).toHaveTextContent('Outdated Custom Widget');
    });
  });

  it('shows no results message when search has no matches', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Type in search box
    await user.type(
      screen.getByPlaceholderText('settings.installed.search.placeholder'),
      'nonexistent'
    );
    await user.click(screen.getByText('settings.installed.search.button'));

    // Should show no results message
    expect(screen.getByText('settings.installed.noResults')).toBeInTheDocument();
  });

  it('opens settings modal when settings icon is clicked', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Find and click settings icon for KB_SEARCH widget
    await user.click(screen.getByTestId('settings-icon-widget1'));

    // Check if modal was opened
    expect(mockOpenModal).toHaveBeenCalledTimes(1);
  });

  it('opens confirm modal when trying to remove widget', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Find and click trash icon to remove widget
    await user.click(screen.getByTestId('trash-icon-widget1'));

    // Check if confirm modal was opened
    expect(mockOpenConfirmModal).toHaveBeenCalledTimes(1);
  });

  it('removes widget when confirmed', async () => {
    // Simulate confirm action by implementing openConfirmModal only for this test
    mockOpenConfirmModal.mockImplementation(({ onConfirm }) => {
      if (onConfirm && typeof onConfirm === 'function') {
        onConfirm();
      }
    });

    renderWithMantine(<InstalledWidgets />);

    // Find and click trash icon
    await user.click(screen.getByTestId('trash-icon-widget1'));

    // Check if widget removal was called with correct ID
    expect(mockSetConfirmModalLoading).toHaveBeenCalledWith(true);
    expect(mockRemoveWidget).toHaveBeenCalledWith('widget1');
    expect(mockCloseConfirmModal).toHaveBeenCalled();
    expect(mockSetConfirmModalLoading).toHaveBeenCalledWith(false);
  });

  it('toggles widget status (public/private) when badge is clicked', async () => {
    // Simulate confirm action by implementing openConfirmModal only for this test
    mockOpenConfirmModal.mockImplementation(({ onConfirm }) => {
      if (onConfirm && typeof onConfirm === 'function') {
        onConfirm();
      }
    });

    renderWithMantine(<InstalledWidgets />);

    // Find and click the status badge for the first widget (public)
    await user.click(screen.getByTestId('status-badge-widget1'));

    // Check if widget update was called to change to private
    expect(mockUpdateWidget).toHaveBeenCalledWith('widget1', {
      ...mockWidgetInstances[0],
      status: 'private',
    });

    // Find and click the status badge for the second widget (private)
    await user.click(screen.getByTestId('status-badge-widget2'));

    // Check if widget update was called to change to public
    expect(mockUpdateWidget).toHaveBeenCalledWith('widget2', {
      ...mockWidgetInstances[1],
      status: 'public',
    });
  });

  // New tests for sync compilation functionality
  it('shows refresh icon only for custom widgets that are not latest compilation', () => {
    renderWithMantine(<InstalledWidgets />);

    // Should not show refresh icon for standard widgets
    expect(screen.queryByTestId('refresh-icon-widget1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('refresh-icon-widget2')).not.toBeInTheDocument();

    // Should not show refresh icon for latest compilation custom widget
    expect(screen.queryByTestId('refresh-icon-widget3')).not.toBeInTheDocument();

    // Should show refresh icon for outdated custom widget
    expect(screen.getByTestId('refresh-icon-widget4')).toBeInTheDocument();
  });

  it('calls syncCustomWidgetInstanceCompilation when refresh icon is clicked', async () => {
    mockSyncCustomWidgetInstanceCompilation.mockResolvedValue(undefined);

    renderWithMantine(<InstalledWidgets />);

    await user.click(screen.getByTestId('refresh-icon-widget4'));

    expect(mockSyncCustomWidgetInstanceCompilation).toHaveBeenCalledWith('widget4');
  });

  it('shows success modal when sync compilation succeeds', async () => {
    mockSyncCustomWidgetInstanceCompilation.mockResolvedValue(undefined);

    renderWithMantine(<InstalledWidgets />);

    await user.click(screen.getByTestId('refresh-icon-widget4'));

    await waitFor(() => {
      expect(mockOpenConfirmModal).toHaveBeenCalledWith({
        content: 'settings.all.syncCompilation.success',
        cancelText: 'common.button.close',
        options: {
          isShowConfirm: false,
        },
      });
    });
  });

  it('shows error modal when sync compilation fails', async () => {
    const error = new Error('Sync failed');
    mockSyncCustomWidgetInstanceCompilation.mockRejectedValue(error);

    renderWithMantine(<InstalledWidgets />);

    await user.click(screen.getByTestId('refresh-icon-widget4'));

    await waitFor(() => {
      expect(mockOpenConfirmModal).toHaveBeenCalledWith({
        content: 'settings.all.syncCompilation.error',
        cancelText: 'common.button.close',
        options: {
          isShowConfirm: false,
        },
      });
    });
  });

  it('shows tooltip for refresh icon', async () => {
    renderWithMantine(<InstalledWidgets />);

    // Hover over the refresh icon
    await user.hover(screen.getByTestId('refresh-icon-widget4'));

    // Check if tooltip appears
    await waitFor(() => {
      expect(screen.getByText('settings.all.syncCompilation.tooltip')).toBeInTheDocument();
    });
  });
});
