import { useCallback, useEffect, useState } from 'react';
import { Bad<PERSON>, Box, Flex, rem, Text, Tooltip } from '@mantine/core';
import { createStyles, keyframes } from '@mantine/emotion';
import {
  IconEye,
  IconEyeOff,
  IconLogicAnd,
  IconRefresh,
  IconSettings,
  IconTrash,
} from '@tabler/icons-react';
import { useTranslate } from '@tolgee/react';
import { DecaButton, SearchInput } from '@resola-ai/ui';

import { useWidgetContext } from '@/contexts/WidgetContext';
import type { WidgetFilterValue, WidgetInstance, WidgetType } from '@/types/widget';
import { useAppContext } from '@/contexts/AppContext';
import { SETTINGS_WIDGET_TYPES, WidgetInfo } from '@/constants/widget';
import { isCustomWidget, isLatestCompilationWidgetInstance } from '@/utils/widget';
import useSettingsModal from '@/hooks/useSettingsModal';
import WidgetFilters from '../WidgetFilters';

const spin = keyframes`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(-360deg);
  }
`;

const useStyles = createStyles((theme) => ({
  widgetBox: {
    width: '100%',
    gap: rem(30),
    padding: `${rem(20)} ${rem(30)}`,
    borderRadius: rem(6),
    boxShadow: `0 0 ${rem(4)} 0 rgba(0, 0, 0, 0.2)`,
    justifyContent: 'space-between',
  },
  searchBox: {
    width: rem(300),
    borderRadius: rem(4),
  },
  actionIcon: {
    color: theme.colors.decaNavy[5],
    cursor: 'pointer',
  },
  icon: {
    color: theme.colors.decaBlue[5],
    width: rem(22),
    height: rem(22),
  },
  sync: {
    animation: `${spin} 1s linear infinite`,
  },
}));

const InstalledWidgets = () => {
  const { t: originT } = useTranslate();
  const t = (key: string) => originT(`settings.installed.${key}`);
  const { classes, cx } = useStyles();
  const {
    removeWidgetInstance,
    widgetInstances,
    updateWidgetInstance,
    customWidgets,
    syncCustomWidgetInstanceCompilation,
  } = useWidgetContext();
  const { openConfirmModal, closeConfirmModal, setConfirmModalLoading } = useAppContext();

  const [widgets, setWidgets] = useState<WidgetInstance[]>([]);
  const [currentModalWidgetType, setCurrentModalWidgetType] =
    useState<WidgetInstance['widgetType']>('KB_SEARCH');
  const [searchValue, setSearchValue] = useState('');
  const [selectedWidget, setSelectedWidget] = useState<WidgetInstance>();
  const [filters, setFilters] = useState<WidgetFilterValue>('all');
  const [syncCompilationLoading, setSyncCompilationLoading] = useState<boolean>(false);

  const {
    Modal: SettingsModal,
    opened: modalOpened,
    close: closeModal,
    open: openModal,
  } = useSettingsModal({ widgetType: currentModalWidgetType });

  const handleSettingsClick = (widget: WidgetInstance) => {
    setCurrentModalWidgetType(widget.widgetType);
    setSelectedWidget(widget);
    openModal();
  };

  const onFilterChange = useCallback((value: WidgetFilterValue) => {
    setFilters(value);
  }, []);

  const handleCloseModal = useCallback(() => {
    setSelectedWidget(undefined);
    closeModal();
  }, [closeModal]);

  const onRemoveWidget = useCallback(
    async (widget: WidgetInstance) => {
      openConfirmModal({
        content: originT('modal.confirm.widgetTitle', { widgetName: widget.name }),
        onConfirm: async () => {
          setConfirmModalLoading(true);
          await removeWidgetInstance(widget.id as string);
          closeConfirmModal();
          setConfirmModalLoading(false);
        },
        confirmText: originT('modal.confirm.uninstallBtn'),
        cancelText: originT('modal.confirm.cancelBtn'),
        options: { isRemoving: true },
      });
    },
    [closeConfirmModal, openConfirmModal, originT, removeWidgetInstance, setConfirmModalLoading]
  );

  const getFilteredWidgets = useCallback(
    (list: WidgetInstance[]) => {
      if (filters === 'all') return list;

      if (filters === 'custom') {
        return list.filter((widget) => isCustomWidget(widget.widgetType));
      }

      return list.filter((widget) => !isCustomWidget(widget.widgetType));
    },
    [filters]
  );

  const onSearch = useCallback(() => {
    const searchValueLower = searchValue.toLowerCase();

    if (!searchValueLower) {
      setWidgets(getFilteredWidgets(widgetInstances ?? []));
      return;
    }

    const filteredWidgets = getFilteredWidgets(widgetInstances ?? []).filter(
      (widget) =>
        widget.name.toLowerCase().includes(searchValueLower) ||
        widget.description.toLowerCase().includes(searchValueLower)
    );

    setWidgets(filteredWidgets);
  }, [searchValue, widgetInstances, getFilteredWidgets]);

  const onClearSearch = useCallback(() => {
    setSearchValue('');

    setWidgets(getFilteredWidgets(widgetInstances ?? []));
  }, [widgetInstances, getFilteredWidgets]);

  useEffect(() => {
    if (!searchValue) {
      setWidgets(getFilteredWidgets(widgetInstances ?? []));
    }
  }, [widgetInstances, getFilteredWidgets]);

  const handleStatusChange = useCallback(
    (widget: WidgetInstance) => {
      openConfirmModal({
        content: originT(
          widget.status === 'private'
            ? 'modal.confirm.publicChange'
            : 'modal.confirm.privateChange',
          { widgetName: widget.name }
        ),
        confirmText: originT(
          widget.status === 'private' ? 'common.button.public' : 'common.button.private'
        ),
        cancelText: originT('modal.confirm.cancelBtn'),
        onConfirm: async () => {
          setConfirmModalLoading(true);
          await updateWidgetInstance(widget.id as string, {
            ...widget,
            status: widget.status === 'private' ? 'public' : 'private',
          });
          if (searchValue) {
            onSearch();
          }
          closeConfirmModal();
          setConfirmModalLoading(false);
        },
        options: {
          reverseAction: true,
        },
      });
    },
    [
      openConfirmModal,
      originT,
      setConfirmModalLoading,
      updateWidgetInstance,
      searchValue,
      closeConfirmModal,
      onSearch,
    ]
  );

  const onSyncCompilation = useCallback(
    async (widget: WidgetInstance) => {
      try {
        setSyncCompilationLoading(true);
        await syncCustomWidgetInstanceCompilation(widget.id as string);

        openConfirmModal({
          content: originT('settings.all.syncCompilation.success'),
          cancelText: originT('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } catch (error) {
        console.error(error);
        openConfirmModal({
          content: originT('settings.all.syncCompilation.error'),
          cancelText: originT('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } finally {
        setSyncCompilationLoading(false);
      }
    },
    [syncCustomWidgetInstanceCompilation]
  );

  return (
    <Flex my={rem(22)} direction='column'>
      <Flex justify='space-between' align='center'>
        <WidgetFilters onChange={onFilterChange} />
        <Flex gap={rem(8)}>
          <SearchInput
            className={classes.searchBox}
            onChange={(value) => setSearchValue(value)}
            onClear={onClearSearch}
            placeholder={t('search.placeholder')}
          />
          <DecaButton variant='primary' onClick={onSearch}>
            {t('search.button')}
          </DecaButton>
        </Flex>
      </Flex>
      {!widgets.length ? (
        <Flex justify='center' align='center' h={rem('50vh')}>
          <Text sx={{ whiteSpace: 'pre-line' }} ta='center' c='decaDark.2'>
            {t(searchValue ? 'noResults' : 'noWidgets')}
          </Text>
        </Flex>
      ) : (
        <Flex direction='column' gap={rem(30)} my={rem(30)}>
          {widgets.map((widget) => {
            const widgetInfo = WidgetInfo[widget?.widgetType];
            const isPublic = widget.status === 'public';
            const rootWidget = customWidgets.find((w) => w.id === widget.widgetId);
            const isLatestCompilation =
              rootWidget && isLatestCompilationWidgetInstance(rootWidget, widget);
            const isLatestCompilationText = isLatestCompilation
              ? ` (${originT('settings.all.latest')})`
              : '';

            return (
              <Flex key={widget.id} className={classes.widgetBox} data-testid='widget-box'>
                <Box>
                  <Flex align='center' gap={rem(12)}>
                    <Text fz='xl' fw='bold' lh={rem(21)} c='decaGrey.9'>
                      {originT(`settings.all.${widgetInfo?.title}`, widget.widget.name)}
                    </Text>
                    <Text c='decaGrey.5' fz='md' fw='bold' lh={rem(24)}>
                      {widget.isCustom &&
                        `${widget.settings?.compilationName}${isLatestCompilationText} ・`}
                      {widget.name}
                    </Text>
                    <Badge
                      bg={isPublic ? 'decaGreen.0' : 'decaNavy.0'}
                      c={isPublic ? 'decaGreen.9' : 'decaNavy.9'}
                      leftSection={isPublic ? <IconEye size={16} /> : <IconEyeOff size={16} />}
                      sx={{ '& svg': { marginTop: rem(2) }, cursor: 'pointer' }}
                      onClick={() => handleStatusChange(widget)}
                      data-testid={`status-badge-${widget.id}`}
                    >
                      {t(isPublic ? 'public' : 'private')}
                    </Badge>
                    {widget.isCustom && (
                      <Tooltip
                        label={originT('settings.all.browserExtensionTooltip')}
                        withArrow
                        position='top'
                      >
                        <IconLogicAnd className={classes.icon} />
                      </Tooltip>
                    )}
                  </Flex>
                  <Text fz='md' c='decaGrey.8' mt={rem(6)}>
                    {widget.description}
                  </Text>
                </Box>
                <Flex gap={rem(20)} align='center'>
                  {isCustomWidget(widget.widgetType) && !isLatestCompilation && (
                    <Tooltip
                      label={originT('settings.all.syncCompilation.tooltip')}
                      withArrow
                      position='top'
                    >
                      <IconRefresh
                        className={cx(classes.actionIcon, syncCompilationLoading && classes.sync)}
                        size={24}
                        onClick={() => onSyncCompilation(widget)}
                        data-testid={`refresh-icon-${widget.id}`}
                      />
                    </Tooltip>
                  )}
                  {widget.widgetType &&
                    (SETTINGS_WIDGET_TYPES as WidgetType[]).includes(widget.widgetType) && (
                      <IconSettings
                        className={classes.actionIcon}
                        size={24}
                        onClick={() => handleSettingsClick(widget)}
                        data-testid={`settings-icon-${widget.id}`}
                      />
                    )}
                  <IconTrash
                    className={classes.actionIcon}
                    size={24}
                    onClick={() => onRemoveWidget(widget)}
                    data-testid={`trash-icon-${widget.id}`}
                  />
                </Flex>
              </Flex>
            );
          })}
        </Flex>
      )}
      {SettingsModal && (
        <SettingsModal
          opened={modalOpened}
          onClose={handleCloseModal}
          settings={selectedWidget}
          type={currentModalWidgetType}
          noSeparator
        />
      )}
    </Flex>
  );
};

export default InstalledWidgets;
