import { Box, Flex, Input, rem, Text } from '@mantine/core';
import type { ChangeEventHandler, Dispatch, FC, SetStateAction } from 'react';
import { useTranslate } from '@tolgee/react';

import { LANGUAGE_ITEMS_PER_COLUMN } from '@/constants/translation';
import type { TranslationLanguage } from '@/types/translation';
import { DecaCheckbox } from '@resola-ai/ui';

interface DefaultLanguageSelectionProps {
  cssClasses: {
    label: string;
    root: string;
  };
  defaultLanguageCodes: string[];
  defaultLanguages: TranslationLanguage[];
  defaultLanguageSelection: Record<string, boolean>;
  setDefaultLanguageSelection: Dispatch<SetStateAction<Record<string, boolean>>>;
}

const DefaultLanguageSelection: FC<DefaultLanguageSelectionProps> = (props) => {
  const {
    cssClasses,
    defaultLanguageCodes,
    defaultLanguages,
    defaultLanguageSelection,
    setDefaultLanguageSelection,
  } = props;
  const { t } = useTranslate(['home', 'language']);
  const numColumns = defaultLanguages.length
    ? Math.ceil(defaultLanguages.length / LANGUAGE_ITEMS_PER_COLUMN)
    : 1;

  const handleLanguageChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const {
      target: { checked, name },
    } = event;

    setDefaultLanguageSelection((prevState) => ({
      ...prevState,
      [name]: checked,
    }));
  };

  const handleDefaultLanguageChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const {
      target: { checked },
    } = event;

    setDefaultLanguageSelection((prevState) => ({
      ...prevState,
      ...Object.fromEntries(defaultLanguages.map(({ code }) => [code, checked])),
    }));
  };

  return (
    <>
      <Box mb={rem(8)}>
        <Text>{t('translation.languageSelectionDescription', { ns: 'home' })}</Text>
      </Box>
      <Input.Wrapper
        label={<Text size='lg'>{t('translation.settings.languages.label', { ns: 'home' })}</Text>}
        classNames={{ root: cssClasses.root, label: cssClasses.label }}
      >
        <DecaCheckbox
          checked={defaultLanguageCodes.every((code) => !!defaultLanguageSelection[code])}
          indeterminate={
            defaultLanguageCodes.some((code) => !!defaultLanguageSelection[code]) &&
            defaultLanguageCodes.some((code) => !defaultLanguageSelection[code])
          }
          label={t('translation.label.batchSelection', { ns: 'home' })}
          onChange={handleDefaultLanguageChange}
        />
      </Input.Wrapper>
      <Flex gap={rem(8)} w='100%' wrap='nowrap'>
        {[...Array(numColumns)].map((_, column) => (
          <Flex
            key={`column-${column}`}
            direction='column'
            gap={rem(8)}
            sx={{ flexBasis: `${100 / numColumns}%` }}
          >
            {defaultLanguages
              .slice(
                column * LANGUAGE_ITEMS_PER_COLUMN,
                column * LANGUAGE_ITEMS_PER_COLUMN + LANGUAGE_ITEMS_PER_COLUMN
              )
              .map(({ code }) => (
                <DecaCheckbox
                  key={code}
                  checked={!!defaultLanguageSelection[code]}
                  label={t(code, { ns: 'language' })}
                  name={code}
                  onChange={handleLanguageChange}
                />
              ))}
          </Flex>
        ))}
      </Flex>
    </>
  );
};

export default DefaultLanguageSelection;
