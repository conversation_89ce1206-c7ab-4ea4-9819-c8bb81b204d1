import { type ComboboxItem, Flex, Input, rem, Text, useMantineTheme } from '@mantine/core';
import { IconCircleMinus } from '@tabler/icons-react';
import {
  type ChangeEventHandler,
  type Dispatch,
  type FC,
  type SetStateAction,
  useMemo,
} from 'react';
import { useTranslate } from '@tolgee/react';

import type { TranslationLanguage } from '@/types/translation';
import { DecaCheckbox } from '@resola-ai/ui';
import { DecaSearchBox } from '@resola-ai/ui/components';
import LanguageSelectItem from '../LanguageSelectItem';

interface AdditionalLanguageSelectionProps {
  addedLanguages: TranslationLanguage[];
  additionalLanguages: TranslationLanguage[];
  additionalLanguageSelection: Record<string, boolean>;
  cssClasses: {
    label: string;
    minusIcon: string;
    root: string;
  };
  languageMap: Record<string, string>;
  setAddedLanguages: Dispatch<SetStateAction<TranslationLanguage[]>>;
  setAdditionalLanguageSelection: Dispatch<SetStateAction<Record<string, boolean>>>;
}

const AdditionalLanguageSelection: FC<AdditionalLanguageSelectionProps> = (props) => {
  const {
    addedLanguages,
    additionalLanguages,
    additionalLanguageSelection,
    cssClasses,
    languageMap,
    setAddedLanguages,
    setAdditionalLanguageSelection,
  } = props;
  const theme = useMantineTheme();
  const { t } = useTranslate(['home', 'language']);

  const additionalLanguageCodes = useMemo(
    () => addedLanguages.map(({ code }) => code),
    [addedLanguages]
  );

  const addableLanguages = useMemo<ComboboxItem[]>(
    () =>
      additionalLanguages
        .filter(({ code }) => !addedLanguages.find((language) => language.code === code))
        .map<ComboboxItem>(({ code }) => ({ label: t(code, { ns: 'language' }), value: code }))
        .sort((a, b) => (a.label && b.label ? a.label.localeCompare(b.label) : 0)),
    [addedLanguages, additionalLanguages, t]
  );

  const handleSearchBoxChange = (value: string) => {
    if (!value) return;

    setAddedLanguages((prevState) => [...prevState, { code: value, name: languageMap[value] }]);
    setAdditionalLanguageSelection((prevState) => ({ ...prevState, [value]: true }));
  };

  const handleLanguageChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const {
      target: { checked, name },
    } = event;

    setAdditionalLanguageSelection((prevState) => ({
      ...prevState,
      [name]: checked,
    }));
  };

  const handleAdditionalLanguageChange: ChangeEventHandler<HTMLInputElement> = (event) => {
    const {
      target: { checked },
    } = event;

    setAdditionalLanguageSelection((prevState) => ({
      ...prevState,
      ...Object.fromEntries(additionalLanguages.map(({ code }) => [code, checked])),
    }));
  };

  const handleDeleteLanguage = (code: string) => {
    setAddedLanguages((prevState) => prevState.filter((lang) => lang.code !== code));
    setAdditionalLanguageSelection((prevState) => ({ ...prevState, [code]: false }));
  };

  return (
    <>
      {!!addedLanguages.length && (
        <>
          <Input.Wrapper
            label={
              <Text size='lg'>{t('translation.settings.userLanguages.label', { ns: 'home' })}</Text>
            }
            classNames={{ root: cssClasses.root, label: cssClasses.label }}
          >
            <DecaCheckbox
              checked={additionalLanguageCodes.every((code) => !!additionalLanguageSelection[code])}
              indeterminate={
                additionalLanguageCodes.some((code) => !!additionalLanguageSelection[code]) &&
                additionalLanguageCodes.some((code) => !additionalLanguageSelection[code])
              }
              label={t('translation.label.batchSelection', { ns: 'home' })}
              onChange={handleAdditionalLanguageChange}
            />
          </Input.Wrapper>
          <Flex direction='column' rowGap={rem(8)} w={rem(308)}>
            {addedLanguages.map(({ code }) => (
              <Flex key={code} align='center' justify='space-between'>
                <DecaCheckbox
                  checked={!!additionalLanguageSelection[code]}
                  label={t(code, { ns: 'language' })}
                  name={code}
                  onChange={handleLanguageChange}
                />
                <IconCircleMinus
                  color={theme.white}
                  cursor='pointer'
                  className={cssClasses.minusIcon}
                  fill={theme.colors.decaRed[5]}
                  onClick={() => handleDeleteLanguage(code)}
                />
              </Flex>
            ))}
          </Flex>
        </>
      )}
      <Text size='lg' fw={500}>
        {t('translation.label.addOtherLanguages', { ns: 'home' })}
      </Text>
      <DecaSearchBox
        isSearch
        width={rem(308)}
        data={addableLanguages}
        optionComponent={LanguageSelectItem}
        placeholder={t('settings.installed.search.placeholder', { ns: 'home' })}
        inputProps={{ w: rem(308) }}
        withinPortal
        onOptionSubmit={handleSearchBoxChange}
      />
    </>
  );
};

export default AdditionalLanguageSelection;
