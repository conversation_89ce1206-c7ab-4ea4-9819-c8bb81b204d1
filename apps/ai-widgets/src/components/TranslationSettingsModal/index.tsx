import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { Box, Center, Flex, Input, Loader, Text } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { type FC, useCallback, useEffect, useMemo } from 'react';
import { useTranslate } from '@tolgee/react';
import {
  type SubmitHandler,
  useForm,
  type UseFormSetValue,
  type UseFormWatch,
  useWatch,
} from 'react-hook-form';
import { DecaButton, Modal } from '@resola-ai/ui';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';

import { useFormStyles } from '@/constants/styles';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { type TranslationSetting, translationSettingSchema } from '@/schemas/setting';
import type { WidgetInstance } from '@/types';
import { JAPANESE_LANGUAGE_CODE, LANGUAGE_ITEMS_PER_COLUMN } from '@/constants/translation';
import { NameField, DescriptionField, StatusField } from '../DefaultSettingsModal/Fields';
import useTranslationLanguages from '@/hooks/useTranslationLanguages';
import useLanguageState from '@/hooks/useLanguageState';
import TranslationLanguageSelectionModal from './TranslationLanguageSelectionModal';

interface TranslationSettingsModalProps extends ConfirmModalProps {
  settings?: WidgetInstance;
}

const TranslationSettingsModal: FC<TranslationSettingsModalProps> = (props) => {
  const { settings: widgetInstance, onClose, ...rest } = props;
  const { t } = useTranslate();
  const [languageModalOpened, { close: closeLanguageModal, open: openLanguageModal }] =
    useDisclosure();
  const { classes } = useFormStyles();
  const {
    data: defaultLanguages,
    error: defaultLanguagesLoadingError,
    isLoading: isLoadingDefaultLanguages,
  } = useTranslationLanguages({ type: 'default-languages' });
  const {
    control,
    formState: { errors, isSubmitting },
    getValues,
    handleSubmit,
    register,
    reset,
    resetField,
    setValue,
    watch,
  } = useForm<TranslationSetting>({
    defaultValues: {
      name: widgetInstance?.name ?? '',
      description: widgetInstance?.description ?? '',
      status: widgetInstance?.status ?? 'public',
      settings: {
        additionalLanguages: widgetInstance?.settings?.additionalLanguages ?? [],
        languages:
          widgetInstance?.settings?.languages.filter(
            (code: string) => code !== JAPANESE_LANGUAGE_CODE
          ) ?? [],
      },
    },
    resolver: zodResolver(translationSettingSchema),
  });
  const nameWatched = useWatch({ control, name: 'name' });
  const settingsWatched = useWatch({ control, name: 'settings' });
  const { data: additionalLanguages, isLoading: isLoadingAdditionalLanguages } =
    useTranslationLanguages({ type: 'more-languages' });
  const { widgets, installWidgetInstance, updateWidgetInstance, updateWidgetInstanceSettings } =
    useWidgetContext();
  const defaultLanguagesWithoutJapanese = useMemo(
    () => (defaultLanguages ?? []).filter(({ code }) => code !== JAPANESE_LANGUAGE_CODE),
    [defaultLanguages]
  );
  const languageMap = Object.fromEntries(
    (additionalLanguages ?? []).map(({ code, name }) => [code, name])
  );
  const languageState = useLanguageState({
    defaultLanguages: defaultLanguagesWithoutJapanese,
    formAdditionalLanguageCodes: settingsWatched.additionalLanguages,
    formDefaultLanguageCodes: settingsWatched.languages,
    languageMap,
  });
  const { resetLanguageSelection } = languageState;

  useEffect(() => {
    resetLanguageSelection();
  }, [settingsWatched, resetLanguageSelection]);

  useEffect(() => {
    if (settingsWatched.languages.length) return;

    // Default first column to checked for new widget installation
    setValue(
      'settings.languages',
      defaultLanguagesWithoutJapanese.slice(0, LANGUAGE_ITEMS_PER_COLUMN).map(({ code }) => code)
    );
  }, [defaultLanguagesWithoutJapanese, settingsWatched.languages.length, setValue]);

  const handleClose = useCallback(() => {
    onClose();
    reset();
  }, [onClose, reset]);

  const onSubmit: SubmitHandler<TranslationSetting> = useCallback(
    async (data) => {
      const { name, description, settings, status } = data;
      const { additionalLanguages, languages } = settings;

      try {
        if (widgetInstance?.id) {
          await updateWidgetInstance(widgetInstance.id, {
            name,
            description,
            status,
          });
          await updateWidgetInstanceSettings(widgetInstance.id, {
            additionalLanguages,
            languages: [...languages, JAPANESE_LANGUAGE_CODE],
          });
        } else {
          await installWidgetInstance(
            widgets.find(({ type }) => type === 'TRANSLATION')?.id ?? '',
            {
              name,
              description,
              settings: {
                ...settings,
                languages: [...settings.languages, JAPANESE_LANGUAGE_CODE],
              },
              status,
            }
          );
        }
        resetField('settings', { defaultValue: settingsWatched });
        handleClose();
      } catch (err) {
        notifications.show({ color: 'red', message: (err as Error).toString() });
      }
    },
    [
      settingsWatched,
      widgetInstance?.settings,
      widgetInstance?.id,
      handleClose,
      updateWidgetInstanceSettings,
      installWidgetInstance,
      widgets,
      getValues,
      resetField,
    ]
  );

  const handleConfirmLanguageModal = useCallback(
    (settings: TranslationSetting['settings']) => {
      setValue('settings', settings);
    },
    [setValue]
  );

  if (defaultLanguagesLoadingError) {
    return (
      <Modal opened={rest.opened} onClose={handleClose}>
        <Text>{defaultLanguagesLoadingError.toString()}</Text>
      </Modal>
    );
  }

  const isLoadingLanguages = isLoadingAdditionalLanguages || isLoadingDefaultLanguages;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <ConfirmModal
        cancelButtonProps={{ disabled: isSubmitting }}
        cancelText={t('modal.confirm.cancelBtn')}
        confirmButtonProps={{ disabled: !nameWatched, loading: isSubmitting, type: 'submit' }}
        confirmText={t('common.button.save')}
        title={t('translation.title')}
        classNames={{
          content: classes.modalContent,
          title: classes.modalTitle,
          close: classes.modalClose,
        }}
        size='lg'
        withinPortal={false}
        onClose={handleClose}
        {...rest}
      >
        {isLoadingLanguages && (
          <Box className={classes.root}>
            <Center w='100%'>
              <Loader />
            </Center>
          </Box>
        )}

        {!isLoadingLanguages && (
          <>
            <Box className={classes.root}>
              <NameField control={control} errors={errors} />
              <DescriptionField control={control} errors={errors} />
              <Input.Wrapper label={t('translation.fields.languages.label')}>
                <Flex className={classes.inputInner} align='center'>
                  <DecaButton size='sm' variant='primary' type='button' onClick={openLanguageModal}>
                    {t('translation.fields.languages.buttonText')}
                  </DecaButton>
                </Flex>
              </Input.Wrapper>
              <StatusField
                watch={watch as unknown as UseFormWatch<WidgetInstance>}
                setValue={setValue as unknown as UseFormSetValue<WidgetInstance>}
              />
            </Box>
            <input {...register('settings')} type='hidden' />
          </>
        )}
      </ConfirmModal>
      {additionalLanguages && (
        <TranslationLanguageSelectionModal
          opened={languageModalOpened}
          additionalLanguages={additionalLanguages}
          defaultLanguages={defaultLanguagesWithoutJapanese}
          languageState={languageState}
          onClose={closeLanguageModal}
          onConfirm={handleConfirmLanguageModal}
        />
      )}
    </form>
  );
};

export default TranslationSettingsModal;
