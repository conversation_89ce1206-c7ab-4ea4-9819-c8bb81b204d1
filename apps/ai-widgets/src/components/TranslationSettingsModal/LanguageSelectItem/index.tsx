import { type ComboboxOptionProps, Flex, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconCirclePlus } from '@tabler/icons-react';
import type { FC, PropsWithChildren } from 'react';

const useStyles = createStyles(() => ({
  item: {
    width: '100%',
    alignItems: 'center',
    cursor: 'pointer',
    justifyContent: 'space-between',
  },
  icon: {
    width: rem(16),
    height: rem(16),
  },
}));

const LanguageSelectItem: FC<PropsWithChildren<ComboboxOptionProps & { label: string }>> = (
  props
) => {
  const { label } = props;
  const { classes, theme } = useStyles();

  return (
    <Flex className={classes.item}>
      <Text c='decaGrey.9' fw={500}>
        {label}
      </Text>
      <IconCirclePlus
        className={classes.icon}
        color={theme.white}
        fill={theme.colors.decaGreen[5]}
      />
    </Flex>
  );
};

export default LanguageSelectItem;
