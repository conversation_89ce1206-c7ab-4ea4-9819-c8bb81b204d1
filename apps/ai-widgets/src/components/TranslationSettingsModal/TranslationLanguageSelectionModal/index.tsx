import { Box, Flex, rem, Sc<PERSON><PERSON><PERSON> } from '@mantine/core';
import { createStyles, getStylesRef } from '@mantine/emotion';
import type { FC } from 'react';
import { useTranslate } from '@tolgee/react';

import { useFormStyles } from '@/constants/styles';
import type { TranslationLanguage } from '@/types/translation';
import type { TranslationSetting } from '@/schemas/setting';
import type { ConfirmModalProps } from '@resola-ai/ui';
import ConfirmModal from '@resola-ai/ui/components/ConfirmModal';
import AdditionalLanguageSelection from '../AdditionalLanguageSelection';
import DefaultLanguageSelection from '../DefaultLanguageSelection';
import type useLanguageState from '@/hooks/useLanguageState';

const useStyles = createStyles(() => ({
  root: {
    alignItems: 'center !important',

    [`&& .${getStylesRef('label')}`]: {
      minWidth: '20%',
      width: '20%',
    },
  },
  label: {
    ref: getStylesRef('label'),
  },
  minusIcon: {
    width: rem(16),
    height: rem(16),
  },
}));

interface TranslationLanguageSelectionModalProps
  extends Omit<ConfirmModalProps, 'onClose' | 'onConfirm'> {
  additionalLanguages: TranslationLanguage[];
  defaultLanguages: TranslationLanguage[];
  languageState: ReturnType<typeof useLanguageState>;
  onConfirm: (settings: TranslationSetting['settings']) => void;
  onClose: () => void;
}

const TranslationLanguageSelectionModal: FC<TranslationLanguageSelectionModalProps> = (props) => {
  const { additionalLanguages, defaultLanguages, languageState, onClose, onConfirm, ...rest } =
    props;
  const { t } = useTranslate();
  const { classes: formClasses } = useFormStyles();
  const { classes } = useStyles();
  const languageMap = Object.fromEntries(additionalLanguages.map(({ code, name }) => [code, name]));
  const {
    addedLanguages,
    additionalLanguageSelection,
    defaultLanguageSelection,
    resetLanguageSelection,
    setAddedLanguages,
    setAdditionalLanguageSelection,
    setDefaultLanguageSelection,
  } = languageState;

  const defaultLanguageCodes = defaultLanguages.map(({ code }) => code);

  const handleClose = () => {
    resetLanguageSelection();
    onClose();
  };

  const handleConfirm = () => {
    const selectedDefaultLanguageCodes = Object.entries(defaultLanguageSelection)
      .filter(([, selected]) => selected)
      .map(([code]) => code);
    const selectedAdditionalLanguageCodes = Object.entries(additionalLanguageSelection)
      .filter(([, selected]) => selected)
      .map(([code]) => code);

    onConfirm({
      additionalLanguages: selectedAdditionalLanguageCodes,
      languages: selectedDefaultLanguageCodes,
    });
    onClose();
  };

  return (
    <ConfirmModal
      cancelText={t('modal.confirm.backBtn')}
      confirmText={t('common.button.save')}
      confirmButtonProps={{
        disabled: Object.values(defaultLanguageSelection).every((selected) => !selected),
      }}
      title={t('translation.languageSelectionTitle')}
      classNames={{
        content: formClasses.modalContent,
        title: formClasses.modalTitle,
      }}
      noSeparator
      scrollAreaComponent={ScrollArea.Autosize}
      size='100%'
      onClose={handleClose}
      onConfirm={handleConfirm}
      {...rest}
    >
      <Flex direction='column' rowGap={rem(32)}>
        <Box className={formClasses.root}>
          <DefaultLanguageSelection
            cssClasses={classes}
            defaultLanguageCodes={defaultLanguageCodes}
            defaultLanguageSelection={defaultLanguageSelection}
            defaultLanguages={defaultLanguages}
            setDefaultLanguageSelection={setDefaultLanguageSelection}
          />
        </Box>
        <Box className={formClasses.root}>
          <AdditionalLanguageSelection
            addedLanguages={addedLanguages}
            additionalLanguages={additionalLanguages}
            additionalLanguageSelection={additionalLanguageSelection}
            cssClasses={classes}
            languageMap={languageMap}
            setAddedLanguages={setAddedLanguages}
            setAdditionalLanguageSelection={setAdditionalLanguageSelection}
          />
        </Box>
      </Flex>
    </ConfirmModal>
  );
};

export default TranslationLanguageSelectionModal;
