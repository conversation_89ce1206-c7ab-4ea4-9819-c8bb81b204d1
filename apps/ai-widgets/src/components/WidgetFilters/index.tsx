import { useState } from 'react';
import { SegmentedControl, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

import type { WidgetFilterValue } from '@/types/widget';
import { useTranslate } from '@tolgee/react';

const useStyles = createStyles((theme) => ({
  root: {
    width: '100%',
    maxWidth: rem(500),
    backgroundColor: theme.colors.decaNavy[0],
    padding: rem(4),
    borderRadius: rem(6),
    gap: rem(4),
  },
  indicator: {
    backgroundColor: theme.colors.decaMono[1],
    boxShadow: theme.shadows.xs,
    borderRadius: rem(6),
  },
  label: {
    fontSize: theme.fontSizes.md,
    fontWeight: 500,
    padding: `${rem(8)} ${rem(16)}`,
    color: theme.colors.decaNavy[8],
    '&[data-active="true"]': {
      color: theme.colors.decaNavy[4],
    },
  },
}));

interface WidgetFiltersProps {
  value?: WidgetFilterValue;
  onChange?: (value: WidgetFilterValue) => void;
}

const WidgetFilters = ({ value = 'all', onChange }: WidgetFiltersProps) => {
  const { classes } = useStyles();
  const { t } = useTranslate();
  const [selectedFilter, setSelectedFilter] = useState<WidgetFilterValue>(value);

  const handleChange = (newValue: string) => {
    setSelectedFilter(newValue as WidgetFilterValue);
    onChange?.(newValue as WidgetFilterValue);
  };

  return (
    <SegmentedControl
      classNames={classes}
      value={selectedFilter}
      onChange={handleChange}
      data={[
        { label: t('filters.all'), value: 'all' },
        { label: t('filters.standard'), value: 'standard' },
        { label: t('filters.custom'), value: 'custom' },
      ]}
      withItemsBorders={false}
    />
  );
};

export default WidgetFilters;
