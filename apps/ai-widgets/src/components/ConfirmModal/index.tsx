import { Flex, Group, Modal, Text, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton } from '@resola-ai/ui';
import { useTranslate } from '@tolgee/react';

export interface ConfirmModalOptions {
  isConfirmLoading?: boolean;
  isRemoving?: boolean;
  isShowCancel?: boolean;
  isShowConfirm?: boolean;
  reverseAction?: boolean;
}

export interface ConfirmModalProps {
  opened: boolean;
  title?: string;
  content?: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  options?: ConfirmModalOptions;
}

const useStyles = createStyles((theme) => ({
  confirmModal: {
    '& .mantine-Modal-header': {
      padding: rem(5),
    },
    '& .mantine-Modal-close': {
      '&:focus': {
        outline: 'none',
      },
    },
    textAlign: 'center',
  },
  title: {
    fontSize: rem(20),
    fontWeight: 700,
    lineHeight: rem(34.1),
    color: theme.colors.decaGrey[9],
  },
  content: {
    fontSize: rem(16),
    fontWeight: 500,
    lineHeight: rem(25),
    color: theme.colors.decaGrey[9],
  },
  button: {
    textTransform: 'capitalize',
    minWidth: rem(100),
  },
}));

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  opened,
  title,
  content,
  confirmText,
  cancelText,
  onConfirm,
  onCancel,
  options,
}) => {
  const { t } = useTranslate();
  const { classes } = useStyles();
  const {
    isRemoving = false,
    isShowCancel = true,
    isShowConfirm = true,
    isConfirmLoading = false,
    reverseAction = false,
  } = options || {};

  const confirmButton = isShowConfirm && (
    <DecaButton
      key='confirm'
      size='md'
      className={classes.button}
      variant={isRemoving ? 'negative' : 'primary'}
      onClick={onConfirm}
      radius={'sm'}
      loading={isConfirmLoading}
    >
      {confirmText || t('modal.confirm.yesBtn')}
    </DecaButton>
  );

  const cancelButton = isShowCancel && (
    <DecaButton
      key='cancel'
      size='md'
      className={classes.button}
      variant='neutral'
      onClick={onCancel}
      radius={'sm'}
    >
      {cancelText || t('modal.confirm.noBtn')}
    </DecaButton>
  );

  const buttons = reverseAction ? [cancelButton, confirmButton] : [confirmButton, cancelButton];

  return (
    <Modal opened={opened} onClose={onCancel} size='md' className={classes.confirmModal} centered>
      <Flex direction={'column'} justify={'center'} gap={rem(15)} align={'center'}>
        {title ? <Text className={classes.title}>{title}</Text> : null}
        {content ? <Text className={classes.content}>{content}</Text> : null}
        <Group mt={rem(20)}>{buttons.map((button) => button)}</Group>
      </Flex>
    </Modal>
  );
};

export default ConfirmModal;
