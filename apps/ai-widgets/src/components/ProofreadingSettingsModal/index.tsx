import { useCallback, useEffect, useState, useRef } from 'react';
import { Box, Flex, Input, rem, Switch, Text } from '@mantine/core';
import { useForm } from 'react-hook-form';
import { useTranslate } from '@tolgee/react';
import { zodResolver } from '@hookform/resolvers/zod';
import { Textarea } from 'react-hook-form-mantine';
import { DecaButton } from '@resola-ai/ui';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';

import type { WidgetInstance } from '@/types';
import { useFormStyles } from '@/constants/styles';
import { proofreadingSettingSchema } from '@/schemas/setting';
import { PROOFREADING_DEFAULT_PROMPT } from '@/constants/widget';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { useAppContext } from '@/contexts/AppContext';
import { <PERSON><PERSON><PERSON>, Description<PERSON>ield, StatusField } from '../DefaultSettingsModal/Fields';

interface ProofreadingSettingsModalProps extends ConfirmModalProps {
  settings?: WidgetInstance;
}

const MAX_LINE_ERROR = 10000;

const ProofreadingSettingsModal: React.FC<ProofreadingSettingsModalProps> = ({
  settings,
  onClose,
  ...props
}) => {
  const { t } = useTranslate();
  const { classes, cx } = useFormStyles();
  const {
    control,
    getValues,
    setValue,
    formState: { errors },
    watch,
    reset,
  } = useForm({
    defaultValues: settings ?? {
      description: '',
      settings: {
        prompt: '',
        userDictionary: '',
        useAI: true,
        useUserDictionary: true,
      },
      status: 'public',
    },
    resolver: zodResolver(proofreadingSettingSchema),
  });
  const [isDictionaryState, setIsDictionaryState] = useState<boolean>(false);
  const [maxLineError, setMaxLineError] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { widgets, installWidgetInstance, updateWidgetInstance, updateWidgetInstanceSettings } =
    useWidgetContext();
  const { openConfirmModal } = useAppContext();

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setValue('settings.userDictionary', content.trim());

          // Reset input files
          if (fileInputRef.current) {
            const dt = new DataTransfer();
            fileInputRef.current.files = dt.files;
          }
        };
        reader.readAsText(file);
      }
    },
    [setValue, fileInputRef.current]
  );

  const handleUploadClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleDownloadCsv = useCallback(() => {
    const content = getValues('settings.userDictionary');
    if (!content.trim()) return;

    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'user_dictionary.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [getValues]);

  useEffect(() => {
    if (settings) {
      setValue('name', settings.name);
      setValue('description', settings.description);
      setValue('settings.prompt', settings?.settings?.prompt ?? PROOFREADING_DEFAULT_PROMPT);
      setValue('settings.useAI', settings?.settings?.useAI ?? true);
      setValue('settings.useUserDictionary', settings?.settings?.useUserDictionary ?? true);
      setValue('settings.userDictionary', settings?.settings?.userDictionary ?? '');
      setValue('status', settings.status);
    }
  }, [settings]);

  useEffect(() => {
    const userDictionary = getValues('settings.userDictionary');
    const lines = userDictionary.split('\n');
    setMaxLineError(lines.length > MAX_LINE_ERROR);
  }, [watch('settings.userDictionary')]);

  const handleOnClose = useCallback(() => {
    if (isDictionaryState) {
      setIsDictionaryState(false);

      setValue('settings.userDictionary', settings?.settings?.userDictionary ?? '');
      return;
    }

    onClose();
    reset();
    setIsDictionaryState(false);
  }, [isDictionaryState, settings]);

  const onSubmit = useCallback(async () => {
    if (isDictionaryState) {
      setIsDictionaryState(false);

      return;
    }

    try {
      if (settings?.id) {
        const { settings: settingValue, ...rest } = getValues();

        await updateWidgetInstance(settings.id, rest);
        await updateWidgetInstanceSettings(settings.id, settingValue);
      } else {
        await installWidgetInstance(
          widgets.find((w) => w.type === 'PROOFREADING')?.id ?? '',
          getValues()
        );
      }

      onClose();
      openConfirmModal({
        content: t('modal.success.settings.saved'),
        cancelText: t('common.button.close'),
        options: {
          isShowConfirm: false,
        },
      });
    } catch (error) {
      onClose();
      if ((error as Error).message === 'Duplicated') {
        openConfirmModal({
          content: t('proofreading.error.duplicated'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } else if ((error as Error).message === 'Limited') {
        openConfirmModal({
          content: t('proofreading.error.limited'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      }
    } finally {
      handleOnClose();
    }
  }, [isDictionaryState, settings]);

  const onRestoreDefaultPrompt = useCallback(() => {
    setValue('settings.prompt', PROOFREADING_DEFAULT_PROMPT);
  }, []);

  const handleOnChangeUseAI = useCallback(
    (value: boolean) => {
      setValue('settings.useAI', value);

      if (!value) {
        setValue('settings.useUserDictionary', true);
      }
    },
    [setValue]
  );

  const handleOnChangeUseUserDictionary = useCallback(
    (value: boolean) => {
      setValue('settings.useUserDictionary', value);

      if (!value) {
        setValue('settings.useAI', true);
      }
    },
    [setValue]
  );

  return (
    <ConfirmModal
      cancelText={isDictionaryState ? t('modal.confirm.backBtn') : t('modal.confirm.cancelBtn')}
      confirmText={t('common.button.save')}
      title={isDictionaryState ? t('proofreading.dictionaryTitle') : t('proofreading.title')}
      onConfirm={onSubmit}
      onClose={handleOnClose}
      size='lg'
      confirmButtonProps={{
        disabled: (!watch('name') && !isDictionaryState) || maxLineError,
      }}
      classNames={{
        content: classes.modalContent,
        title: classes.modalTitle,
        close: classes.modalClose,
      }}
      {...props}
    >
      <form>
        <Box className={cx(classes.root, isDictionaryState && classes.hide)}>
          <NameField control={control} errors={errors} />
          <DescriptionField control={control} errors={errors} />
          <Input.Wrapper label={t('proofreading.fields.settings.useUserDictionary.label')}>
            <Flex className={classes.inputInner} gap={rem(16)} align='center'>
              <Box>
                <Switch
                  checked={watch('settings.useUserDictionary')}
                  onChange={(e) => handleOnChangeUseUserDictionary(e.target.checked)}
                  name='settings.useUserDictionary'
                  label={t('proofreading.fields.settings.useUserDictionary.description')}
                  color='decaGreen.6'
                />
              </Box>
              <DecaButton
                size='sm'
                variant='primary'
                type='button'
                disabled={!watch('settings.useUserDictionary')}
                onClick={() => setIsDictionaryState(true)}
              >
                {t('proofreading.fields.settings.useUserDictionary.editButton')}
              </DecaButton>
            </Flex>
          </Input.Wrapper>
          <Input.Wrapper
            label={t('proofreading.fields.settings.prompt.label')}
            classNames={{
              label: classes.topLabel,
            }}
          >
            <Box className={classes.inputInner}>
              <Switch
                checked={watch('settings.useAI')}
                onChange={(e) => handleOnChangeUseAI(e.target.checked)}
                name='settings.useAI'
                label={t('proofreading.fields.settings.useAI.label')}
                color='decaGreen.6'
                mb={rem(10)}
              />
              <Textarea
                control={control}
                name='settings.prompt'
                placeholder={PROOFREADING_DEFAULT_PROMPT}
                error={errors.settings?.prompt?.message as string}
                autosize
                minRows={8}
                maxRows={12}
              />
              <Text
                mt={rem(5)}
                size='xs'
                c='decaNavy.5'
                onClick={onRestoreDefaultPrompt}
                style={{ cursor: 'pointer', display: 'inline-block', float: 'right' }}
              >
                {t('proofreading.label.restoreDefault')}
              </Text>
            </Box>
          </Input.Wrapper>
          <StatusField watch={watch} setValue={setValue} />
        </Box>
        <Box className={cx(classes.root, !isDictionaryState && classes.hide)}>
          <Text>{t('proofreading.label.userDictionary')}</Text>
          <Flex gap={rem(10)} align='center' my={rem(10)}>
            <input
              type='file'
              accept='.csv'
              style={{ display: 'none' }}
              ref={fileInputRef}
              onChange={handleFileUpload}
            />
            <DecaButton size='sm' variant='primary' type='button' onClick={handleUploadClick}>
              {t('proofreading.fields.settings.userDictionary.uploadCsvButton')}
            </DecaButton>
            <DecaButton size='sm' variant='neutral' type='button' onClick={handleDownloadCsv}>
              {t('proofreading.fields.settings.userDictionary.downloadCsvButton')}
            </DecaButton>
          </Flex>
          <Textarea
            control={control}
            name='settings.userDictionary'
            placeholder={t('proofreading.fields.settings.userDictionary.placeholder')}
            error={errors.settings?.userDictionary?.message as string}
            autosize
            minRows={15}
            maxRows={15}
          />
          {maxLineError && (
            <Text size='xs' c='decaRed.6'>
              {t('proofreading.error.maxLines')}
            </Text>
          )}
        </Box>
      </form>
    </ConfirmModal>
  );
};

export default ProofreadingSettingsModal;
