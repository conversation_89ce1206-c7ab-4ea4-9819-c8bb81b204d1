import { useCallback, useEffect, useMemo, useState } from 'react';
import { Box, Flex, Input, rem, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useForm, type UseFormSetValue, type UseFormWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Textarea } from 'react-hook-form-mantine';
import { DecaButton, SearchInput } from '@resola-ai/ui';
import { ZodIssueCode } from 'zod';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';
import debounce from 'lodash/debounce';

import type { WidgetInstance } from '@/types';
import { useFormStyles } from '@/constants/styles';
import { ROOT_FOLDER, ROOT_PATH } from '@/constants/kb';
import { REPLY_ASSISTANT_DEFAULT_PROMPT, REPLY_ASSISTANT_VARIABLES } from '@/constants/widget';
import { useAppContext } from '@/contexts/AppContext';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { useKbWidgetContext } from '@/contexts/KbWidgetContext';
import { type ReplyAssistantSetting, replyAssistantSettingSchema } from '@/schemas/setting';
import { getSelectedKbAndFolderName } from '@/utils/kb';
import { NameField, DescriptionField, StatusField } from '../DefaultSettingsModal/Fields';
import SearchFolderAndKb from '../KbSearchSettingsModal/SearchFolderAndKb';
import SelectFolderAndKb from '../KbSearchSettingsModal/SelectFolderAndKb';
import SelectedFolderAndKb from '../KbSearchSettingsModal/SelectedFolderAndKb';
import KbFolderDeletedError from '../KbSearchSettingsModal/KbFolderDeletedError';
import { createStyles } from '@mantine/emotion';

const useStyles = createStyles(() => ({
  modalContent: {
    minWidth: rem(920),
  },
}));

interface ReplyAssistantSettingsModalProps extends ConfirmModalProps {
  settings?: WidgetInstance;
}

const ReplyAssistantSettingsModal: React.FC<ReplyAssistantSettingsModalProps> = ({
  settings,
  onClose,
  ...props
}) => {
  const { t } = useTranslate();
  const { classes, cx } = useFormStyles();
  const { classes: currentClasses } = useStyles();
  const {
    control,
    getValues,
    setValue,
    formState: { errors, isSubmitting, isValid },
    handleSubmit,
    watch,
    reset,
    trigger,
  } = useForm<ReplyAssistantSetting>({
    defaultValues: settings ?? {
      description: '',
      settings: {
        searchFolders: [ROOT_PATH],
        searchBases: [],
        searchDocuments: [],
        prompt: REPLY_ASSISTANT_DEFAULT_PROMPT,
      },
      status: 'public',
    },
    mode: 'onChange',
    resolver: zodResolver(replyAssistantSettingSchema),
  });
  const {
    widgets,
    widgetInstances,
    installWidgetInstance,
    updateWidgetInstance,
    updateWidgetInstanceSettings,
  } = useWidgetContext();
  const { openConfirmModal } = useAppContext();
  const {
    allBases,
    cleanup,
    deletedFolderIds,
    deletedKbIds,
    fetchBases,
    fetchFolderByIds,
    fetchFolders,
    fetchKbsByIds,
    folders,
    loading,
    searchCombineFolderAndKb,
    selectedFolders,
    selectedKbs,
    selectedDocuments,
    setSelectedFolders,
    setSelectedKbs,
    setSelectedDocuments,
    fetchDocumentsByIds,
    fetchDocuments,
    allDocuments,
  } = useKbWidgetContext();

  const [isFolderState, setIsFolderState] = useState<boolean>(false);
  const [textSearch, setTextSearch] = useState<string>('');
  const [initPhase, setInitPhase] = useState<boolean>(true);

  const fetchCurrentKb = useCallback(
    (searchBases: string[]) => {
      if (searchBases.length) {
        fetchKbsByIds(searchBases);
      }
    },
    [fetchKbsByIds]
  );

  const fetchCurrentFolder = useCallback(
    (searchFolders: string[]) => {
      if (searchFolders.length) {
        const folderIds = searchFolders.filter((folderId) => folderId !== ROOT_PATH);
        const hasRootFolder = searchFolders.includes(ROOT_PATH);

        if (hasRootFolder) {
          setSelectedFolders((prev) =>
            prev.some((folder) => folder.id === ROOT_FOLDER.id) ? prev : [...prev, ROOT_FOLDER]
          );
        }

        if (folderIds.length) {
          fetchFolderByIds(folderIds);
        }
      }
    },
    [fetchFolderByIds]
  );

  const fetchCurrentDocument = useCallback(
    (searchDocuments: string[]) => {
      if (searchDocuments.length) {
        fetchDocumentsByIds(searchDocuments);
      }
    },
    [fetchDocumentsByIds]
  );

  useEffect(() => {
    fetchFolders(ROOT_PATH, 1);
    fetchBases();
    fetchDocuments(ROOT_PATH);
  }, []);

  useEffect(() => {
    if (selectedFolders) {
      setValue(
        'settings.searchFolders',
        selectedFolders.map((f) => f.id)
      );
    }
  }, [selectedFolders]);

  useEffect(() => {
    if (selectedKbs) {
      setValue(
        'settings.searchBases',
        selectedKbs.map((kb) => kb.id)
      );
    }
  }, [selectedKbs]);

  useEffect(() => {
    if (selectedDocuments) {
      setValue(
        'settings.searchDocuments',
        selectedDocuments.map((doc) => doc.id)
      );
    }
  }, [selectedDocuments]);

  useEffect(() => {
    if (settings) {
      const searchFolders = settings?.settings?.searchFolders ?? [];
      const searchBases = settings?.settings?.searchBases ?? [];
      const searchDocuments = settings?.settings?.searchDocuments ?? [];
      setValue('name', settings.name);
      setValue('description', settings.description);
      setValue('settings.searchFolders', searchFolders);
      setValue('settings.searchBases', searchBases);
      setValue('settings.searchDocuments', searchDocuments);
      setValue('settings.prompt', settings.settings?.prompt ?? REPLY_ASSISTANT_DEFAULT_PROMPT);
      setValue('status', settings.status);

      fetchCurrentKb(searchBases);
      fetchCurrentFolder(searchFolders);
      fetchCurrentDocument(searchDocuments);
    }
  }, [settings]);

  const handleOnClose = useCallback(() => {
    if (isFolderState) {
      setIsFolderState(false);

      const searchFolders = widgetInstances.find((w) => w.id === settings?.id)?.settings
        ?.searchFolders;
      const searchBases = widgetInstances.find((w) => w.id === settings?.id)?.settings?.searchBases;
      const searchDocuments = widgetInstances.find((w) => w.id === settings?.id)?.settings
        ?.searchDocuments;
      const searchFoldersData = folders?.filter((f) => searchFolders?.includes(f.id));
      const searchBasesData = allBases?.filter((b) => searchBases?.includes(b.id));
      const searchDocumentsData = allDocuments?.filter((d) => searchDocuments?.includes(d.id));

      setSelectedFolders(searchFoldersData);
      setSelectedKbs(searchBasesData);
      setSelectedDocuments(searchDocumentsData);
      return;
    }

    onClose();
    setTimeout(() => {
      reset();
      setIsFolderState(false);
      setInitPhase(true);
      setTextSearch('');
      cleanup();
    }, 200);
  }, [settings, widgetInstances, isFolderState, allBases, folders, allDocuments]);

  const onSubmit = useCallback(async () => {
    if (isFolderState) {
      setIsFolderState(false);

      return;
    }

    try {
      if (settings?.id) {
        const { settings: settingValue, ...rest } = getValues();
        let searchFolders = [ROOT_PATH];

        if (settingValue?.searchFolders.length) {
          searchFolders = settingValue.searchFolders;
        } else if (settingValue?.searchBases.length) {
          searchFolders = [];
        }

        const updatedSettings = {
          ...settingValue,
          searchFolders,
          searchDocuments: settingValue?.searchDocuments.length ? settingValue.searchDocuments : [],
        };

        await updateWidgetInstance(settings.id, rest);
        await updateWidgetInstanceSettings(settings.id, updatedSettings);
      } else {
        await installWidgetInstance(
          widgets.find((w) => w.type === 'REPLY_ASSISTANT')?.id ?? '',
          getValues()
        );
      }

      onClose();
      openConfirmModal({
        content: t('modal.success.settings.saved'),
        cancelText: t('common.button.close'),
        options: {
          isShowConfirm: false,
        },
      });
    } catch (error) {
      onClose();
      if ((error as Error).message === 'Duplicated') {
        openConfirmModal({
          content: t('replyAssistant.error.duplicated'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } else if ((error as Error).message === 'Limited') {
        openConfirmModal({
          content: t('replyAssistant.error.limited'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      }
    } finally {
      handleOnClose();
    }
  }, [isFolderState, settings]);

  const onRestoreDefaultPrompt = useCallback(() => {
    setValue('settings.prompt', REPLY_ASSISTANT_DEFAULT_PROMPT);
    trigger('settings.prompt');
  }, []);

  const handleSearch = useCallback(
    debounce((value: string) => {
      if (loading || textSearch === value) return;
      setTextSearch(value);
      searchCombineFolderAndKb(value);
    }, 500),
    [loading, textSearch]
  );

  const kbAndFolderName = getSelectedKbAndFolderName(
    watch('settings.searchBases'),
    watch('settings.searchFolders'),
    watch('settings.searchDocuments'),
    folders,
    allBases,
    allDocuments,
    t
  );

  const isContainDeletedKbAndFolder = useMemo(() => {
    return deletedFolderIds.length > 0 || deletedKbIds.length > 0;
  }, [deletedFolderIds, deletedKbIds]);

  const promptErrorMessages = useMemo(() => {
    if (!errors.settings?.prompt) return null;

    const isUserInputIncluded = getValues('settings.prompt').includes(
      REPLY_ASSISTANT_VARIABLES.USER_INPUT
    );

    const isKbResultIncluded = getValues('settings.prompt').includes(
      REPLY_ASSISTANT_VARIABLES.KB_RESULT
    );

    switch (errors.settings.prompt.type) {
      case ZodIssueCode.custom:
        if (!isUserInputIncluded && !isKbResultIncluded) {
          return t('replyAssistant.error.prompt', {
            variable: `${REPLY_ASSISTANT_VARIABLES.USER_INPUT}, ${REPLY_ASSISTANT_VARIABLES.KB_RESULT}`,
          });
        }
        return t('replyAssistant.error.prompt', {
          variable: isUserInputIncluded
            ? REPLY_ASSISTANT_VARIABLES.KB_RESULT
            : REPLY_ASSISTANT_VARIABLES.USER_INPUT,
        });
      case ZodIssueCode.too_small:
      default:
        return t('common.error.required');
    }
  }, [JSON.stringify(errors), watch('settings.prompt')]);

  return (
    <ConfirmModal
      cancelText={isFolderState ? t('modal.confirm.backBtn') : t('modal.confirm.cancelBtn')}
      confirmText={t('common.button.save')}
      title={
        isFolderState
          ? t('replyAssistant.targetKnowledgeBaseFolderTitle')
          : t('replyAssistant.title')
      }
      onConfirm={handleSubmit(onSubmit)}
      onClose={handleOnClose}
      confirmButtonProps={{
        disabled:
          (!isValid && !isFolderState) ||
          (!selectedFolders?.length &&
            !selectedKbs?.length &&
            !selectedDocuments?.length &&
            isFolderState) ||
          (isContainDeletedKbAndFolder && initPhase) ||
          isSubmitting,
      }}
      cancelButtonProps={{
        disabled: isSubmitting,
      }}
      classNames={{
        content: isFolderState ? currentClasses.modalContent : classes.modalContent,
        title: classes.modalTitle,
        close: classes.modalClose,
      }}
      {...props}
    >
      <form>
        <Box className={cx(classes.root, isFolderState && classes.hide)}>
          <NameField control={control} errors={errors} />
          <DescriptionField control={control} errors={errors} />
          <Input.Wrapper
            label={t('replyAssistant.fields.searchFolders.label')}
            classNames={{
              label: classes.topLabel,
            }}
          >
            <Flex className={classes.inputInner} gap={rem(16)} direction='column'>
              <Text sx={{ whiteSpace: 'pre-line' }}>{kbAndFolderName}</Text>
              <KbFolderDeletedError show={isContainDeletedKbAndFolder && initPhase} />
              <DecaButton
                size='sm'
                variant='primary'
                type='button'
                onClick={() => {
                  setInitPhase(false);
                  setIsFolderState(true);
                }}
              >
                {t('replyAssistant.fields.searchFolders.change')}
              </DecaButton>
            </Flex>
          </Input.Wrapper>
          <Input.Wrapper
            label={t('replyAssistant.fields.prompt.label')}
            classNames={{
              label: classes.topLabel,
            }}
          >
            <Box className={classes.inputInner}>
              <Textarea
                control={control}
                name='settings.prompt'
                placeholder={REPLY_ASSISTANT_DEFAULT_PROMPT}
                error={promptErrorMessages}
                autosize
                minRows={8}
                maxRows={12}
              />
              <Text
                mt={rem(5)}
                size='xs'
                c='decaNavy.5'
                onClick={onRestoreDefaultPrompt}
                style={{ cursor: 'pointer', display: 'inline-block', float: 'right' }}
              >
                {t('replyAssistant.label.restoreDefault')}
              </Text>
            </Box>
          </Input.Wrapper>
          <StatusField
            watch={watch as unknown as UseFormWatch<WidgetInstance>}
            setValue={setValue as unknown as UseFormSetValue<WidgetInstance>}
          />
        </Box>
        <Box className={cx(classes.root, !isFolderState && classes.hide)}>
          <SearchInput
            className={classes.searchBox}
            onChange={(value) => handleSearch(value)}
            onClear={() => handleSearch('')}
            placeholder={t('kb.searchFolderAndKb')}
          />
          <Flex justify={'space-between'} w='100%'>
            <Flex direction='column' w='66%'>
              <Text fw={500} mb={rem(8)}>
                {t('kb.foldersAndKbs')}
              </Text>
              {textSearch ? (
                <SearchFolderAndKb multiple={true} />
              ) : (
                <SelectFolderAndKb multiple={true} />
              )}
            </Flex>
            <Flex direction='column' w='33%'>
              <Text fw={500} mb={rem(8)}>
                {t('kb.selectedFoldersAndKbs')}
              </Text>
              <SelectedFolderAndKb />
            </Flex>
          </Flex>
        </Box>
      </form>
    </ConfirmModal>
  );
};

export default ReplyAssistantSettingsModal;
