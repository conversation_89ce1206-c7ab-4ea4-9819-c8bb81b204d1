import { useCallback, useState } from 'react';
import { Box, Flex, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { type BreadcrumbItem, Breadcrumbs, SearchInput } from '@resola-ai/ui';
import { type TFnType, useTranslate } from '@tolgee/react';
import { IconChevronRight, IconFolder } from '@tabler/icons-react';
import { useDebouncedValue } from '@mantine/hooks';

import type { Folder } from '@/types';
import { useFolderContext } from '@/contexts/FolderContext';
import { useKbFolderSearch } from '@/hooks/useKbFolderSearch';

const useStyles = createStyles((theme) => ({
  folderBox: {
    width: '100%',
    padding: `${rem(13)} ${rem(40)}`,
    borderRadius: rem(10),
    border: `${rem(1)} solid ${theme.colors.decaLight[2]}`,
    maxHeight: rem(80),
    backgroundColor: theme.colors.decaLight[0],
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: theme.colors.decaViolet[0],
    },
    color: theme.colors.decaGrey[3],
  },
  selectedFolderbox: {
    backgroundColor: theme.colors.decaViolet[0],
    border: `${rem(1)} solid ${theme.colors.decaViolet[5]}`,
  },
  nameBox: {
    color: theme.colors.decaNavy[5],
  },
  searchBox: {
    width: rem(200),
    borderRadius: rem(4),
  },
}));

const getDefaultBreadcrumbs = (t: TFnType) => [
  { title: t('tree.root'), href: '' },
  { title: '', href: '' },
];

const KbKeywordSearch = () => {
  const { t } = useTranslate();
  const { classes, cx } = useStyles();
  const { selectedFolder, setSelectedFolder } = useFolderContext();
  const [search, setSearch] = useState('');
  const [debouncedSearch] = useDebouncedValue(search, 300);
  const { data: searchFoldersResponse, mutate: mutateFolders } = useKbFolderSearch(debouncedSearch);
  const folders = searchFoldersResponse?.data ?? [];

  const getBreadcrumbs = useCallback(
    (folder: Folder) => {
      if (!folder.breadcrumb) {
        return getDefaultBreadcrumbs(t);
      }

      const breadcrumbs = folder.breadcrumb?.split('/').filter(Boolean) ?? [];
      return [
        { title: t('tree.root'), href: '' },
        ...breadcrumbs.map((item, index) =>
          index === 0 || index === breadcrumbs.length - 1
            ? undefined
            : {
                title: item,
                href: '',
              }
        ),
        { title: '', href: '' },
      ].filter(Boolean) as BreadcrumbItem[];
    },
    [t]
  );

  return (
    <Box>
      <SearchInput
        className={classes.searchBox}
        onChange={(value) => setSearch(value)}
        onClear={() => mutateFolders({ data: [] }, { revalidate: false })}
      />
      {folders.length ? (
        <Flex direction='column' gap={rem(10)} mt={rem(20)}>
          <Text>{t('kb.label.searchResults', { count: folders.length })}</Text>
          {folders.map((folder) => (
            <Box
              key={folder.id}
              className={cx(
                classes.folderBox,
                selectedFolder === folder.id && classes.selectedFolderbox
              )}
              onClick={() => setSelectedFolder(folder.id)}
            >
              <Breadcrumbs
                items={getBreadcrumbs(folder)}
                separator={<IconChevronRight size={16} />}
              />
              <Flex align='center' className={classes.nameBox} gap={rem(15)} mt={rem(5)}>
                <IconFolder size={24} />
                <Text fz='lg' fw={500} lh={rem(24)} truncate>
                  {folder.name}
                </Text>
              </Flex>
            </Box>
          ))}
        </Flex>
      ) : null}
    </Box>
  );
};

export default KbKeywordSearch;
