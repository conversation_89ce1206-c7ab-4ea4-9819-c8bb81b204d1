import { IconFileDescription, IconArticle } from '@tabler/icons-react';
import { Colors } from '@resola-ai/ui/constants/themeConfiguration';
import type { Kb, KBDocument } from '@/types';

import pdfIcon from '@/assets/icons/pdf.svg';
import txtIcon from '@/assets/icons/txt.svg';
import csvIcon from '@/assets/icons/csv.svg';
import docIcon from '@/assets/icons/doc.svg';
import docxIcon from '@/assets/icons/docx.svg';
import mdIcon from '@/assets/icons/md.svg';

type IconType = 'pdf' | 'txt' | 'csv' | 'doc' | 'docx' | 'md';
type ContentType =
  | 'application/pdf'
  | 'text/plain'
  | 'text/csv'
  | 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  | 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  | 'text/markdown';

const SvgToReactComponent = (props: { svg: string; type: IconType }) => {
  return <img src={props.svg} alt={props.type} />;
};

// pdf, txt, csv, doc, docx, md
export const FileTypeIconMapping: Record<ContentType, React.ReactNode> = {
  'application/pdf': <SvgToReactComponent svg={pdfIcon} type='pdf' />,
  'text/plain': <SvgToReactComponent svg={txtIcon} type='txt' />,
  'text/csv': <SvgToReactComponent svg={csvIcon} type='csv' />,
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': (
    <SvgToReactComponent svg={docIcon} type='doc' />
  ),
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': (
    <SvgToReactComponent svg={docxIcon} type='docx' />
  ),
  'text/markdown': <SvgToReactComponent svg={mdIcon} type='md' />,
};

const extensionToContentType: Record<IconType, ContentType> = {
  pdf: 'application/pdf',
  txt: 'text/plain',
  csv: 'text/csv',
  doc: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  docx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  md: 'text/markdown',
};

const getContentType = (extension: string) => {
  return extensionToContentType[extension as keyof typeof extensionToContentType];
};

const FallbackIcon = () => {
  return <IconFileDescription color={Colors.decaBlue[5]} />;
};

export const renderIcon = (base: Kb | KBDocument) => {
  if ('baseType' in base && base.baseType === 'article') {
    return <IconArticle color={Colors.decaBlue[5]} />;
  }
  const isDocument = 'metadata' in base;
  if (isDocument) {
    const extension = base.metadata.name.split('.').pop();
    if (!extension) return <FallbackIcon />;
    const contentType = getContentType(extension);
    if (!contentType) return <FallbackIcon />;
    const Icon = FileTypeIconMapping[contentType];
    // if not found, return FallbackIcon
    if (!Icon) return <FallbackIcon />;
    return Icon;
  }
  return <FallbackIcon />;
};
