import { useMemo } from 'react';
import { AppShell, Flex, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { LayoutStructure, HeaderContainer } from '@resola-ai/ui/components';
import { usePathName } from '@resola-ai/ui/hooks';
import { kbService } from '@/services';

const DEFAULT_LOGO_URL_JA = 'images/DECA-ai-widget.svg';
const DEFAULT_LOGO_URL_EN = 'images/DECA-ai-widget-en.svg';
const HEIGHT_OF_HEADER = 60;

const useStyles = createStyles((_theme, _, u) => ({
  headerContainer: {
    position: 'fixed',
  },
  appWrapper: {
    width: rem('100%'),
    height: `calc(100% - ${rem(HEIGHT_OF_HEADER)})`,
    minHeight: `calc(100vh - ${rem(HEIGHT_OF_HEADER)})`,
    marginTop: rem(HEIGHT_OF_HEADER),
  },
  drawerCustomClass: {
    '.mantine-Drawer-content': {
      flex: `0 0 ${rem(62)}`,
    },
  },
  burgerControlInHome: {
    display: 'none',
  },
  burgerControlInDetail: {
    display: 'none',
    [u.smallerThan('md')]: {
      display: 'block',
    },
  },
}));

interface BaseLayoutProps {
  navigationMobile?: React.ReactNode;
  children: React.ReactNode;
}

const BaseLayout: React.FC<BaseLayoutProps> = ({ navigationMobile, children }) => {
  const { classes } = useStyles();
  const pathName = usePathName();

  const burgerClass = useMemo(
    () => (pathName === '/ai-widget' ? classes.burgerControlInHome : classes.burgerControlInDetail),
    [pathName]
  );

  return (
    <LayoutStructure extendAxiosServices={[kbService]}>
      <AppShell header={{ height: HEIGHT_OF_HEADER }}>
        <HeaderContainer
          className={classes.headerContainer}
          navigationMobile={navigationMobile}
          logoUrl={DEFAULT_LOGO_URL_EN}
          jaLogoUrl={DEFAULT_LOGO_URL_JA}
          drawerCustomClass={classes.drawerCustomClass}
          burgerCustomClass={burgerClass}
        />
      </AppShell>
      <Flex className={classes.appWrapper}>{children}</Flex>
    </LayoutStructure>
  );
};

export default BaseLayout;
