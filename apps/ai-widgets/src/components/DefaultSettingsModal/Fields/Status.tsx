import { Box, Flex, Input, Switch, Text, Tooltip, rem } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { IconInfoCircle } from '@tabler/icons-react';
import type { UseFormSetValue, UseFormWatch } from 'react-hook-form';

import { useFormStyles } from '@/constants/styles';
import type { WidgetInstance } from '@/types';

interface WidgetStatusFieldProps {
  watch: UseFormWatch<WidgetInstance>;
  setValue: UseFormSetValue<WidgetInstance>;
}

const WidgetStatusField: React.FC<WidgetStatusFieldProps> = ({ watch, setValue }) => {
  const { t } = useTranslate();
  const { classes } = useFormStyles();

  return (
    <Input.Wrapper label={t('default.fields.isPublic.label')}>
      <Box className={classes.inputInner}>
        <Switch
          checked={watch('status') === 'public'}
          onChange={(e) => setValue('status', e.target.checked ? 'public' : 'private')}
          name='status'
          label={
            <Flex gap={rem(10)} align='center'>
              <Text>{t('default.fields.isPublic.description')}</Text>
              <Tooltip
                label={t('default.fields.isPublic.tooltip')}
                position='top'
                withArrow
                multiline
                sx={{ width: rem(245) }}
              >
                <IconInfoCircle size={24} className={classes.tooltipIcon} />
              </Tooltip>
            </Flex>
          }
          color='decaGreen.6'
        />
      </Box>
    </Input.Wrapper>
  );
};

export default WidgetStatusField;
