import { Box, Input } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { TextInput } from 'react-hook-form-mantine';
import type { Control } from 'react-hook-form';

import { useFormStyles } from '@/constants/styles';
import type { WidgetInstance, WidgetInstanceFieldProps } from '@/types';

const WidgetDescriptionField: React.FC<WidgetInstanceFieldProps> = ({ control, errors }) => {
  const { t } = useTranslate();
  const { classes } = useFormStyles();

  return (
    <Input.Wrapper label={t('default.fields.description.label')}>
      <Box className={classes.inputInner}>
        <TextInput
          control={control as Control<WidgetInstance>}
          name='description'
          placeholder={t('default.fields.description.placeholder')}
          error={errors.description?.message}
        />
      </Box>
    </Input.Wrapper>
  );
};

export default WidgetDescriptionField;
