import { Box, Input } from '@mantine/core';
import { TextInput } from 'react-hook-form-mantine';
import { useTranslate } from '@tolgee/react';
import { ZodIssueCode } from 'zod';
import type { Control } from 'react-hook-form';

import { useFormStyles } from '@/constants/styles';
import type { WidgetInstance, WidgetInstanceFieldProps } from '@/types';

const WidgetNameField: React.FC<WidgetInstanceFieldProps> = ({ control, errors }) => {
  const { t } = useTranslate();
  const { classes } = useFormStyles();

  return (
    <Input.Wrapper label={t('default.fields.name.label')} withAsterisk>
      <Box className={classes.inputInner}>
        <TextInput
          control={control as Control<WidgetInstance>}
          name='name'
          placeholder={t('default.fields.name.placeholder')}
          error={
            errors.name?.type === ZodIssueCode.too_small
              ? t('common.error.required')
              : errors.name?.message
          }
        />
      </Box>
    </Input.Wrapper>
  );
};

export default WidgetNameField;
