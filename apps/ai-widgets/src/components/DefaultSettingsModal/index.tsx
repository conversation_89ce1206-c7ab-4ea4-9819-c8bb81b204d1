import { useCallback, useEffect } from 'react';
import { Box } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';

import { type WidgetInstance, type WidgetType, WidgetTypeEnum } from '@/types';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { useAppContext } from '@/contexts/AppContext';
import { useFormStyles } from '@/constants/styles';
import { settingSchema } from '@/schemas/setting';
import { NameField, DescriptionField, StatusField } from './Fields';

interface DefaultSettingsModal extends ConfirmModalProps {
  settings?: WidgetInstance;
  type?: WidgetType;
  selectedWidgetId?: string;
}

const DefaultSettingsModal: React.FC<DefaultSettingsModal> = ({
  settings,
  type = 'TONE_ADJUSTMENT',
  onClose,
  selectedWidgetId,
  ...props
}) => {
  const { t } = useTranslate();
  const { classes } = useFormStyles();
  const { widgets, customWidgets, installWidgetInstance, updateWidgetInstance } =
    useWidgetContext();
  const { openConfirmModal } = useAppContext();
  const { name: widgetName, id: widgetId = '' } =
    (selectedWidgetId
      ? [...customWidgets, ...widgets].find((w) => w.id === selectedWidgetId)
      : widgets.find((w) => w.type === type)) ?? {};
  const {
    control,
    getValues,
    setValue,
    formState: { errors },
    watch,
    reset,
  } = useForm({
    defaultValues: settings ?? {
      name: t(`settings.all.widget.${WidgetTypeEnum[type]}.title`, widgetName),
      description: '',
      settings: {},
      status: 'public',
    },
    resolver: zodResolver(settingSchema),
  });

  useEffect(() => {
    if (settings) {
      setValue('name', settings.name);
      setValue('description', settings.description);
      setValue('status', settings.status);
    }
    if (widgetName && !settings) {
      setValue('name', widgetName);
    }
  }, [settings, widgetName]);

  const handleOnClose = useCallback(() => {
    onClose();
    reset();
  }, []);

  const onSubmit = useCallback(async () => {
    try {
      if (settings?.id) {
        await updateWidgetInstance(settings.id, getValues());
      } else {
        await installWidgetInstance(widgetId, {
          ...getValues(),
          settings: {},
        });
      }

      onClose();
      openConfirmModal({
        content: t('modal.success.settings.saved'),
        cancelText: t('common.button.close'),
        options: {
          isShowConfirm: false,
        },
      });
    } catch (error) {
      onClose();
      if ((error as Error).message === 'Duplicated') {
        openConfirmModal({
          content: t(`${WidgetTypeEnum[type]}.error.duplicated`, t('default.error.duplicated')),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } else if ((error as Error).message === 'Limited') {
        openConfirmModal({
          content: t(`${WidgetTypeEnum[type]}.error.limited`, t('default.error.limited')),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      }
    } finally {
      handleOnClose();
    }
  }, [settings]);

  return (
    <ConfirmModal
      cancelText={t('modal.confirm.cancelBtn')}
      confirmText={t('common.button.save')}
      onConfirm={onSubmit}
      title={t(`${WidgetTypeEnum[type]}.title`, t('default.title'))}
      classNames={{
        content: classes.modalContent,
        title: classes.modalTitle,
        close: classes.modalClose,
      }}
      size='lg'
      confirmButtonProps={{
        disabled: !watch('name'),
      }}
      onClose={handleOnClose}
      {...props}
    >
      <form>
        <Box className={classes.root}>
          <NameField control={control} errors={errors} />
          <DescriptionField control={control} errors={errors} />
          <StatusField watch={watch} setValue={setValue} />
        </Box>
      </form>
    </ConfirmModal>
  );
};

export default DefaultSettingsModal;
