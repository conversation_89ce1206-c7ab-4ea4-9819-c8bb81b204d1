import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod';
import { Box, Input, rem, Text } from '@mantine/core';
import { useTranslate } from '@tolgee/react';
import { useCallback, useEffect } from 'react';
import { useForm, type UseFormSetValue, type UseFormWatch } from 'react-hook-form';
import { Textarea } from 'react-hook-form-mantine';
import ConfirmModal, { type ConfirmModalProps } from '@resola-ai/ui/components/ConfirmModal';

import { useFormStyles } from '@/constants/styles';
import { SUMMARIZATION_DEFAULT_PROMPT, SUMMARIZATION_VARIABLES } from '@/constants/widget';
import { useAppContext } from '@/contexts/AppContext';
import { useWidgetContext } from '@/contexts/WidgetContext';
import { type SummarizationSetting, summarizationSettingSchema } from '@/schemas/setting';
import type { WidgetInstance } from '@/types';
import { Name<PERSON>ield, DescriptionField, StatusField } from '../DefaultSettingsModal/Fields';

interface SummarizationSettingsModalProps extends ConfirmModalProps {
  settings?: WidgetInstance;
}

const SummarizationSettingsModal: React.FC<SummarizationSettingsModalProps> = ({
  settings,
  onClose,
  ...props
}) => {
  const { t } = useTranslate();
  const { classes } = useFormStyles();
  const {
    control,
    getValues,
    setValue,
    formState: { errors, isSubmitting, isValid },
    watch,
    reset,
    trigger,
  } = useForm<SummarizationSetting>({
    defaultValues: settings ?? {
      name: t('settings.all.widget.summarization.title'),
      description: '',
      settings: {
        prompt: SUMMARIZATION_DEFAULT_PROMPT,
      },
      status: 'public',
    },
    mode: 'onChange',
    resolver: zodResolver(summarizationSettingSchema),
  });

  const { widgets, installWidgetInstance, updateWidgetInstance, updateWidgetInstanceSettings } =
    useWidgetContext();
  const { openConfirmModal } = useAppContext();

  useEffect(() => {
    if (settings) {
      setValue('name', settings.name);
      setValue('description', settings.description);
      setValue('settings.prompt', settings.settings?.prompt ?? SUMMARIZATION_DEFAULT_PROMPT);
      setValue('status', settings.status);
    }
  }, [settings]);

  const handleOnClose = useCallback(() => {
    onClose();
    reset();
  }, []);

  const onSubmit = useCallback(async () => {
    try {
      if (settings?.id) {
        const { settings: settingValue, ...rest } = getValues();
        await updateWidgetInstance(settings.id, rest);
        await updateWidgetInstanceSettings(settings.id, settingValue);
      } else {
        await installWidgetInstance(
          widgets.find((w) => w.type === 'SUMMARIZATION')?.id ?? '',
          getValues()
        );
      }

      onClose();
      openConfirmModal({
        content: t('modal.success.settings.saved'),
        cancelText: t('common.button.close'),
        options: {
          isShowConfirm: false,
        },
      });
    } catch (error) {
      onClose();
      if ((error as Error).message === 'Duplicated') {
        openConfirmModal({
          content: t('summarization.error.duplicated'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      } else if ((error as Error).message === 'Limited') {
        openConfirmModal({
          content: t('summarization.error.limited'),
          cancelText: t('common.button.close'),
          options: {
            isShowConfirm: false,
          },
        });
      }
    } finally {
      handleOnClose();
    }
  }, [settings]);

  const onRestoreDefaultPrompt = useCallback(() => {
    setValue('settings.prompt', SUMMARIZATION_DEFAULT_PROMPT);
    trigger('settings.prompt');
  }, []);

  return (
    <ConfirmModal
      cancelText={t('modal.confirm.cancelBtn')}
      confirmText={t('common.button.save')}
      onConfirm={onSubmit}
      title={t('summarization.title')}
      classNames={{
        content: classes.modalContent,
        title: classes.modalTitle,
        close: classes.modalClose,
      }}
      size='lg'
      confirmButtonProps={{
        disabled: !isValid,
        loading: isSubmitting,
      }}
      onClose={handleOnClose}
      {...props}
    >
      <form>
        <Box className={classes.root}>
          <NameField control={control} errors={errors} />
          <DescriptionField control={control} errors={errors} />
          <Input.Wrapper label={t('summarization.fields.prompt.label')}>
            <Box className={classes.inputInner}>
              <Textarea
                control={control}
                name='settings.prompt'
                placeholder={SUMMARIZATION_DEFAULT_PROMPT}
                autosize
                minRows={8}
                maxRows={12}
                error={
                  errors.settings?.prompt?.message
                    ? t(errors.settings?.prompt?.message, {
                        variable: SUMMARIZATION_VARIABLES.INPUT_CONTENT,
                      })
                    : undefined
                }
              />
              <Text
                mt={rem(5)}
                size='xs'
                c='decaNavy.5'
                onClick={onRestoreDefaultPrompt}
                style={{ cursor: 'pointer', display: 'inline-block', float: 'right' }}
              >
                {t('summarization.label.restoreDefault')}
              </Text>
            </Box>
          </Input.Wrapper>
          <StatusField
            watch={watch as unknown as UseFormWatch<WidgetInstance>}
            setValue={setValue as unknown as UseFormSetValue<WidgetInstance>}
          />
        </Box>
      </form>
    </ConfirmModal>
  );
};

export default SummarizationSettingsModal;
