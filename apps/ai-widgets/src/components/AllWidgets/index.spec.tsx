import { screen, waitFor, within } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useDisclosure } from '@mantine/hooks';
import userEvent from '@testing-library/user-event';

import { renderWithMantine } from '@/utils/test';
import { useWidgetContext } from '@/contexts/WidgetContext';
import AllWidgets from './index';

// Mock the hooks
vi.mock('@/contexts/WidgetContext', () => ({
  WidgetContextProvider: ({ children }) => children,
  useWidgetContext: vi.fn(),
}));

vi.mock('@mantine/hooks', () => ({
  useDisclosure: vi.fn(),
}));

vi.mock('@/hooks/useSettingsModal', () => ({
  default: vi.fn().mockImplementation(({ widgetType }) => {
    const [opened, handlers] = useDisclosure();

    return {
      Modal: () => <div data-testid={`settings-modal-${widgetType}`} />,
      opened,
      close: handlers.close,
      open: handlers.open,
    };
  }),
}));

vi.mock('@resola-ai/services-shared', () => ({
  executeRequest: vi.fn(),
}));

describe('AllWidgets Component', () => {
  const user = userEvent.setup();
  const mockOpen = vi.fn();
  const mockClose = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
    (useDisclosure as any).mockReturnValue([false, { open: mockOpen, close: mockClose }]);

    // Mock widget context data
    (useWidgetContext as any).mockReturnValue({
      widgets: [
        {
          id: 'widget1',
          name: 'KB Search Widget',
          description: 'Search knowledge base',
          type: 'KB_SEARCH',
          created: new Date(),
          updated: new Date(),
        },
        {
          id: 'widget2',
          name: 'Reply Assistant Widget',
          description: 'AI-powered reply suggestions',
          type: 'REPLY_ASSISTANT',
          created: new Date(),
          updated: new Date(),
        },
        {
          id: 'widget3',
          name: 'Translation Widget',
          description: 'Translation widget',
          type: 'TRANSLATION',
          created: new Date(),
          updated: new Date(),
        },
      ],
      customWidgets: [
        {
          id: 'custom1',
          name: 'Custom Widget',
          description: 'A custom widget',
          type: 'BROWSER_EXTENSION',
          created: new Date(),
          updated: new Date(),
          widget: {
            compilations: [
              {
                compilationId: 'comp1',
                compilationName: 'v1.0',
                compilationDescription: 'Initial version',
                manifestUrl: 'https://example.com/manifest.json',
                metadata: {},
              },
            ],
            settings: {
              isAI: true,
              maxInstances: 1,
              type: 'singleton',
            },
          },
        },
      ],
      widgetInstances: [
        {
          id: 'instance1',
          name: 'Translation Instance',
          widgetId: 'widget3',
          widgetType: 'TRANSLATION',
          orgId: 'org1',
          created: new Date(),
          updated: new Date(),
          status: 'public',
          widget: {
            id: 'widget3',
            name: 'Translation Widget',
            description: 'Translation widget',
          },
        },
      ],
    });
  });

  it('renders all widgets correctly', async () => {
    renderWithMantine(<AllWidgets />);

    expect(screen.getByText('settings.all.widget.kbSearch.title')).toBeInTheDocument();
    expect(screen.getByText('settings.all.widget.replyAssistant.title')).toBeInTheDocument();

    expect(screen.getByText('settings.all.widget.kbSearch.description')).toBeInTheDocument();
    expect(screen.getByText('settings.all.widget.replyAssistant.description')).toBeInTheDocument();

    // Check if all install buttons are present
    const installButtons = screen.getAllByText('settings.all.button.install');
    expect(installButtons).toHaveLength(4);

    // KB Search widget's button should be disabled since it has an instance
    const translationInstallButton = screen.getByTestId('install-button-widget3');
    expect(translationInstallButton).toBeDisabled();

    // Other buttons should be enabled
    const replyAssistantInstallButton = screen.getByTestId('install-button-widget2');
    expect(replyAssistantInstallButton).not.toBeDisabled();
  });

  it('filters widgets when filter is changed', async () => {
    renderWithMantine(<AllWidgets />);

    // Initially all widgets should be visible
    expect(screen.getAllByTestId(/widget-box-/)).toHaveLength(4);

    // Click on Standard filter
    await user.click(screen.getByText('filters.standard'));

    // Only standard widgets should be visible
    expect(screen.getAllByTestId(/widget-box-/)).toHaveLength(3);
    expect(screen.getByTestId('widget-box-widget1')).toBeInTheDocument();
    expect(screen.getByTestId('widget-box-widget2')).toBeInTheDocument();
    expect(screen.queryByTestId('widget-box-custom1')).not.toBeInTheDocument();

    // Click on Custom filter
    await user.click(screen.getByText('filters.custom'));

    // Only custom widgets should be visible
    expect(screen.getAllByTestId(/widget-box-/)).toHaveLength(1);
    expect(screen.queryByTestId('widget-box-widget1')).not.toBeInTheDocument();
    expect(screen.queryByTestId('widget-box-widget2')).not.toBeInTheDocument();
    expect(screen.getByTestId('widget-box-custom1')).toBeInTheDocument();
  });

  it('shows AI icon for widgets that use AI', () => {
    renderWithMantine(<AllWidgets />);

    expect(screen.getByTestId('ai-tooltip-widget2')).toBeInTheDocument();
    expect(screen.queryByTestId('ai-tooltip-widget1')).toBeNull();
  });

  it('opens settings modal when install button is clicked', async () => {
    renderWithMantine(<AllWidgets />);

    // Click on the Reply Assistant widget's install button
    const replyAssistantWidgetBox = screen.getByTestId('widget-box-widget2');
    const replyAssistantInstallButton = within(replyAssistantWidgetBox).getByText(
      'settings.all.button.install'
    );
    await user.click(replyAssistantInstallButton);

    // Check if the modal open function was called
    expect(mockOpen).toHaveBeenCalledTimes(1);
  });

  it('shows version popover for custom widgets with compilations', async () => {
    renderWithMantine(<AllWidgets />);

    // Find the custom widget
    const customWidget = screen.getByTestId('widget-box-custom1');

    // The version label should be visible
    const versionLabel = within(customWidget).getByText('v1.0');
    expect(versionLabel).toBeInTheDocument();

    // Click on the version label to open the popover
    await user.click(versionLabel);

    // Check if the popover content is visible
    await waitFor(() => {
      expect(screen.getByTestId('version-popover-custom1')).toBeInTheDocument();
      expect(screen.getByTestId('version-title-custom1')).toBeInTheDocument();
      expect(screen.getByTestId('version-description-custom1')).toBeInTheDocument();
    });

    // Close the popover
    await user.click(screen.getByText('common.button.close'));
  });
});
