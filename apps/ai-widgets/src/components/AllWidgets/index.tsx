import { Box, Flex, Popover, rem, Stack, Text, Tooltip } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useClickOutside } from '@mantine/hooks';
import { IconLogicAnd, IconSparkles } from '@tabler/icons-react';
import { type CombinedOptions, type DefaultParamType, useTranslate } from '@tolgee/react';
import { useCallback, useMemo, useRef, useState } from 'react';

import { SINGLE_WIDGET_INSTANCE_LIST, WidgetInfo } from '@/constants/widget';
import { useWidgetContext } from '@/contexts/WidgetContext';
import useSettingsModal from '@/hooks/useSettingsModal';
import type { Widget, WidgetFilterValue, WidgetType } from '@/types/widget';
import { isCustomWidget } from '@/utils/widget';
import { DecaButton } from '@resola-ai/ui';
import WidgetFilters from '../WidgetFilters';

const useStyles = createStyles((theme) => ({
  container: {
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: rem(30),
  },
  widgetBox: {
    height: rem(150),
    padding: rem(20),
    flexBasis: '48%',
    width: '100%',
    maxWidth: rem(576),
    boxShadow: `0 0 ${rem(4)} 0 rgba(0, 0, 0, 0.2);`,
    borderRadius: rem(6),
  },
  icon: {
    color: theme.colors.decaBlue[5],
    width: rem(22),
    height: rem(22),
  },
  versionLabel: {
    fontSize: theme.fontSizes.md,
    fontWeight: 700,
    color: theme.colors.decaGrey[5],
    cursor: 'pointer',
  },
  versionTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: 700,
    color: theme.colors.decaNavy[5],
  },
  versionDescription: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.decaGrey[9],
  },
}));

const AllWidgets = () => {
  const { t: originT } = useTranslate();
  const t = (key: string, defaultValue?: string, options?: CombinedOptions<DefaultParamType>) =>
    originT(`settings.all.${key}`, defaultValue, options);
  const { classes } = useStyles();
  const { widgets, widgetInstances, customWidgets } = useWidgetContext();
  const [currentModalWidgetType, setCurrentModalWidgetType] = useState<WidgetType>('KB_SEARCH');
  const [selectedFilter, setSelectedFilter] = useState<WidgetFilterValue>('all');
  const [versionPopoverOpened, setVersionPopoverOpened] = useState<string>();
  const selectedWidgetId = useRef<string>();
  const versionPopoverRef = useClickOutside(() => setVersionPopoverOpened(undefined));

  const {
    Modal: SettingsModal,
    opened: modalOpened,
    close: closeModal,
    open: openModal,
  } = useSettingsModal({ widgetType: currentModalWidgetType });

  const onInstallWidget = useCallback(
    (widget: Widget) => {
      setCurrentModalWidgetType(widget.type);
      selectedWidgetId.current = widget.id;
      openModal();
    },
    [openModal]
  );

  const handleChangeFilter = useCallback((value: WidgetFilterValue) => {
    setSelectedFilter(value);
  }, []);

  const filteredWidgets = useMemo(() => {
    switch (selectedFilter) {
      case 'standard':
        return widgets;
      case 'custom':
        return customWidgets;
      case 'all':
      default:
        return [...widgets, ...customWidgets];
    }
  }, [selectedFilter, widgets, customWidgets]);

  const isDisabledInstallWidget = useCallback(
    (widget: Widget) => {
      return (
        Boolean(widgetInstances.find((w) => w.widgetId === widget.id)) &&
        ((SINGLE_WIDGET_INSTANCE_LIST as WidgetType[]).includes(widget.type) ||
          (isCustomWidget(widget.type) && widget.widget?.settings?.type === 'singleton'))
      );
    },
    [widgetInstances]
  );

  return (
    <Flex direction='column' gap={rem(30)} my={rem(22)}>
      <WidgetFilters onChange={handleChangeFilter} />
      <Flex className={classes.container}>
        {filteredWidgets.map((widget) => {
          const widgetInfo = WidgetInfo[widget.type];
          const isUsingAI =
            (isCustomWidget(widget.type) ? widget.widget?.settings?.isAI : widgetInfo?.isUsingAI) ??
            false;
          const compilation = isCustomWidget(widget.type) && widget.widget?.compilations?.[0];

          return (
            <Box
              key={widget.id}
              className={classes.widgetBox}
              data-testid={`widget-box-${widget.id}`}
            >
              <Flex justify='space-between' align='center'>
                <Flex gap={rem(10)}>
                  <Text fz='xl' fw='bold' lh={rem(21)} c='decaGrey.9' maw={rem(350)} truncate>
                    {t(`${widgetInfo?.title}`, widget.name)}
                  </Text>
                  {isUsingAI && (
                    <Tooltip label={t('aiTooltip')} withArrow position='top'>
                      <IconSparkles
                        className={classes.icon}
                        data-testid={`ai-tooltip-${widget.id}`}
                      />
                    </Tooltip>
                  )}
                  {isCustomWidget(widget.type) && (
                    <Tooltip label={t('browserExtensionTooltip')} withArrow position='top'>
                      <IconLogicAnd className={classes.icon} />
                    </Tooltip>
                  )}
                </Flex>
                <DecaButton
                  variant='primary'
                  onClick={() => onInstallWidget(widget)}
                  disabled={isDisabledInstallWidget(widget)}
                  data-testid={`install-button-${widget.id}`}
                >
                  {t('button.install')}
                </DecaButton>
              </Flex>
              {compilation && (
                <Popover
                  opened={versionPopoverOpened === widget.id}
                  position='bottom-start'
                  withArrow
                  data-testid={`version-popover-${widget.id}`}
                >
                  <Popover.Target>
                    <Text
                      className={classes.versionLabel}
                      onClick={() => setVersionPopoverOpened(widget.id)}
                    >
                      {compilation.compilationName}
                    </Text>
                  </Popover.Target>
                  <Popover.Dropdown w={rem(274)} maw={rem(274)} ref={versionPopoverRef}>
                    <Stack gap={rem(8)}>
                      <Text
                        className={classes.versionTitle}
                        data-testid={`version-title-${widget.id}`}
                      >
                        {t('version.title', '', { name: compilation.compilationName })}
                      </Text>
                      <Text
                        className={classes.versionDescription}
                        data-testid={`version-description-${widget.id}`}
                      >
                        {compilation.compilationDescription || t('version.noChangeLog')}
                      </Text>
                      <Flex justify='flex-end' mt={rem(8)}>
                        <DecaButton
                          variant='neutral'
                          onClick={() => setVersionPopoverOpened(undefined)}
                        >
                          {originT('common.button.close')}
                        </DecaButton>
                      </Flex>
                    </Stack>
                  </Popover.Dropdown>
                </Popover>
              )}
              <Text lineClamp={3} mt={rem(16)} fz='md' c='decaGrey.8'>
                {t(`${widgetInfo?.description}`, widget.description)}
              </Text>
            </Box>
          );
        })}
        {SettingsModal && (
          <SettingsModal
            opened={modalOpened}
            onClose={closeModal}
            noSeparator
            type={currentModalWidgetType}
            selectedWidgetId={selectedWidgetId.current}
          />
        )}
      </Flex>
    </Flex>
  );
};

export default AllWidgets;
