import { useEffect, useMemo, useState } from 'react';
import { ActionIcon, Group, rem, Stack } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconLayoutGridAdd } from '@tabler/icons-react';
import { NavLink, useLocation } from 'react-router-dom';
import { usePathParams } from '@resola-ai/ui/hooks';
import cloneDeep from 'lodash/cloneDeep';

import { AppConfig } from '@/configs';

const BASE_PATH = AppConfig.BASE_PATH;

const useStyles = createStyles((theme) => ({
  activeIcon: {
    borderRadius: rem(33),
    backgroundColor: theme.colors.decaNavy[5],
    color: theme.white,
    '&:hover': {
      backgroundColor: theme.colors.decaNavy[5],
    },
  },
  icon: {
    borderRadius: rem(33),
    color: theme.colors.decaNavy[5],
    '&:hover': {
      backgroundColor: theme.colors.decaNavy[0],
    },
  },
}));

const defaultNavigationItems = [
  {
    id: 1,
    path: `${BASE_PATH}`,
    icon: <IconLayoutGridAdd stroke={2} />,
  },
];

const NavigationControls = () => {
  const { classes, cx } = useStyles();
  const pathParams = usePathParams();
  const location = useLocation();
  const pathName = location.pathname;
  const [activePath, setActivePath] = useState<string>(`${BASE_PATH}`);

  const navigationItems = useMemo(() => {
    const activeNav = cloneDeep(defaultNavigationItems)
      .reverse()
      .find((item) => activePath.includes(item.path));

    return defaultNavigationItems.map((item) => {
      return {
        ...item,
        active: item.id === activeNav?.id,
      };
    });
  }, [activePath]);

  const createPathWithLngParam = (path: string) => {
    return pathParams.createPathWithLngParam(path);
  };

  useEffect(() => {
    setActivePath(pathName);
  }, [pathName]);

  return (
    <Stack align='center' gap='xl'>
      {navigationItems.map((item, index) => (
        <NavLink to={createPathWithLngParam(item.path)} key={`nav-link-${index}`}>
          <Group gap='xs'>
            <ActionIcon
              key={item.id}
              size={40}
              className={cx(classes.icon, {
                [classes.activeIcon]: item.active,
              })}
            >
              {item.icon}
            </ActionIcon>
          </Group>
        </NavLink>
      ))}
    </Stack>
  );
};

export default NavigationControls;
