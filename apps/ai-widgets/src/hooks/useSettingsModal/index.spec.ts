import { describe, it, expect } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import useSettingsModal from './index';
import type { WidgetType } from '@/types';

// Import the modal components
import KbSearchSettingsModal from '@/components/KbSearchSettingsModal';
import ReplyAssistantSettingsModal from '@/components/ReplyAssistantSettingsModal';
import DefaultSettingsModal from '@/components/DefaultSettingsModal';
import ProofreadingSettingsModal from '@/components/ProofreadingSettingsModal';
import SummarizationSettingsModal from '@/components/SummarizationSettingsModal';
import TranslationSettingsModal from '@/components/TranslationSettingsModal';

describe('useSettingsModal', () => {
  it('should return the correct modal component based on widget type', () => {
    const testCases: { widgetType: WidgetType; expectedModal: React.ComponentType<any> }[] = [
      { widgetType: 'KB_SEARCH', expectedModal: KbSearchSettingsModal },
      { widgetType: 'REPLY_ASSISTANT', expectedModal: ReplyAssistantSettingsModal },
      { widgetType: 'PROOFREADING', expectedModal: ProofreadingSettingsModal },
      { widgetType: 'SUMMARIZATION', expectedModal: SummarizationSettingsModal },
      { widgetType: 'TRANSLATION', expectedModal: TranslationSettingsModal },
      { widgetType: 'EXPLANATION', expectedModal: DefaultSettingsModal },
      { widgetType: 'TONE_ADJUSTMENT', expectedModal: DefaultSettingsModal },
      { widgetType: 'BROWSER_EXTENSION', expectedModal: DefaultSettingsModal },
    ];

    testCases.forEach(({ widgetType, expectedModal }) => {
      const { result } = renderHook(() => useSettingsModal({ widgetType }));
      expect(result.current.Modal).toBe(expectedModal);
    });
  });

  it('should return default modal for unknown widget types', () => {
    const { result } = renderHook(() =>
      useSettingsModal({ widgetType: 'UNKNOWN_TYPE' as WidgetType })
    );
    expect(result.current.Modal).toBe(DefaultSettingsModal);
  });

  it('should initialize with modal closed', () => {
    const { result } = renderHook(() => useSettingsModal({ widgetType: 'KB_SEARCH' }));
    expect(result.current.opened).toBe(false);
  });

  it('should open and close the modal', () => {
    const { result } = renderHook(() => useSettingsModal({ widgetType: 'KB_SEARCH' }));

    // Initially closed
    expect(result.current.opened).toBe(false);

    // Open the modal
    act(() => {
      result.current.open();
    });
    expect(result.current.opened).toBe(true);

    // Close the modal
    act(() => {
      result.current.close();
    });
    expect(result.current.opened).toBe(false);
  });
});
