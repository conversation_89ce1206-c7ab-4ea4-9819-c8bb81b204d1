import { useDisclosure } from '@mantine/hooks';

import type { WidgetType } from '@/types';
import KbSearchSettingsModal from '@/components/KbSearchSettingsModal';
import ReplyAssistantSettingsModal from '@/components/ReplyAssistantSettingsModal';
import DefaultSettingsModal from '@/components/DefaultSettingsModal';
import ProofreadingSettingsModal from '@/components/ProofreadingSettingsModal';
import SummarizationSettingsModal from '@/components/SummarizationSettingsModal';
import TranslationSettingsModal from '@/components/TranslationSettingsModal';

const getSettingsModalByWidgetType = (widgetType: WidgetType) => {
  switch (widgetType) {
    case 'KB_SEARCH':
      return KbSearchSettingsModal;
    case 'REPLY_ASSISTANT':
      return ReplyAssistantSettingsModal;
    case 'PROOFREADING':
      return ProofreadingSettingsModal;
    case 'SUMMARIZATION':
      return SummarizationSettingsModal;
    case 'TRANSLATION':
      return TranslationSettingsModal;
    case 'EXPLANATION':
    case 'TONE_ADJUSTMENT':
    case 'BROWSER_EXTENSION':
    default:
      return DefaultSettingsModal;
  }
};

interface UseSettingsModalProps {
  widgetType: WidgetType;
}

const useSettingsModal = (props: UseSettingsModalProps) => {
  const { widgetType } = props;
  const [opened, { close, open }] = useDisclosure();
  const Modal = getSettingsModalByWidgetType(widgetType);

  return {
    opened: opened,
    Modal,
    close,
    open,
  };
};
export default useSettingsModal;
