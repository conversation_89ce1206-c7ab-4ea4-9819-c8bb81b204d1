import useSWR from 'swr';
import { KbsAPI } from '@/services/api/kbs';

export const useKbDocuments = (
  parentDirId: string,
  limit: number,
  cursor: string,
  direction: string
) => {
  return useSWR(
    parentDirId ? ['/document', parentDirId] : null,
    async () => (parentDirId ? KbsAPI.getDocuments(parentDirId, limit, cursor, direction) : null),
    {
      revalidateOnFocus: false,
    }
  );
};

export const useKbListDocuments = (documentIds: string[]) => {
  return useSWR(
    documentIds.map((documentId) => ['/document', documentId]),
    async () => {
      const data = await Promise.all(
        documentIds.map(async (documentId) => {
          const response = await KbsAPI.getDocument(documentId);
          return response.data;
        })
      );

      return data.filter((item) => item !== null);
    }
  );
};
