import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';

import type { Widget } from '@/types/widget';
import { useWidgetInstances } from './index';

// Mock the external dependencies
vi.mock('swr');

describe('useWidgetInstances', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should call useSWR with the correct key and fetcher', () => {
    // Mock the SWR hook
    const mockUseSWR = vi.mocked(useSWR);

    // Render the hook
    renderHook(() => useWidgetInstances('1'));

    // Verify SWR was called with correct parameters
    expect(mockUseSWR).toHaveBeenCalledTimes(1);
    expect(mockUseSWR.mock.calls[0][0]).toBe('1/widgetInstance');
    expect(typeof mockUseSWR.mock.calls[0][1]).toBe('function');
    expect(mockUseSWR.mock.calls[0][2]).toEqual({
      revalidateOnFocus: false,
    });
  });

  it('should return widget data when API call is successful', () => {
    // Mock sample widget data
    const mockWidgets: Widget[] = [
      {
        id: '1',
        name: 'Test Widget 1',
        description: 'Description for test widget 1',
        type: 'KB_SEARCH',
        created: new Date(),
        updated: new Date(),
      },
      {
        id: '2',
        name: 'Test Widget 2',
        description: 'Description for test widget 2',
        type: 'BROWSER_EXTENSION',
        created: new Date(),
        updated: new Date(),
      },
    ];

    // Mock SWR to return the data
    vi.mocked(useSWR).mockReturnValue({
      data: mockWidgets,
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useWidgetInstances('1'));

    // Verify the returned data
    expect(result.current.data).toEqual(mockWidgets);
    expect(result.current.error).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle API errors', () => {
    // Mock API error
    const mockError = new Error('Failed to fetch custom widgets');

    // Mock SWR to return the error
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useWidgetInstances('1'));

    // Verify the returned error
    expect(result.current.error).toEqual(mockError);
    expect(result.current.data).toBeUndefined();
  });

  it('should indicate loading state', () => {
    // Mock SWR to indicate loading
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isValidating: true,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useWidgetInstances('1'));

    // Verify loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });
});
