import { useCallback, useMemo, useState } from 'react';

import type { TranslationLanguage } from '@/types/translation';

interface UseLanguageStateProps {
  defaultLanguages: TranslationLanguage[];
  formAdditionalLanguageCodes: string[];
  formDefaultLanguageCodes: string[];
  languageMap: Record<string, string>;
}

const useLanguageState = (props: UseLanguageStateProps) => {
  const { defaultLanguages, formAdditionalLanguageCodes, formDefaultLanguageCodes, languageMap } =
    props;

  const defaultLanguageCodes = useMemo(
    () => defaultLanguages.map(({ code }) => code),
    [defaultLanguages]
  );

  const initialDefaultLanguageSelection = useMemo(
    () =>
      Object.fromEntries(
        [...formDefaultLanguageCodes, ...defaultLanguageCodes].map((code) => [
          code,
          formDefaultLanguageCodes.includes(code),
        ])
      ),
    [defaultLanguageCodes, formDefaultLanguageCodes]
  );

  const [defaultLanguageSelection, setDefaultLanguageSelection] = useState(
    initialDefaultLanguageSelection
  );

  const [addedLanguages, setAddedLanguages] = useState<TranslationLanguage[]>(
    formAdditionalLanguageCodes.map((code) => ({ code, name: languageMap[code] }))
  );

  const initialAdditionalLanguageSelection = useMemo(
    () => Object.fromEntries(formAdditionalLanguageCodes.map((code) => [code, true])),
    [formAdditionalLanguageCodes]
  );

  const [additionalLanguageSelection, setAdditionalLanguageSelection] = useState(
    initialAdditionalLanguageSelection
  );

  const resetLanguageSelection = useCallback(() => {
    setDefaultLanguageSelection(initialDefaultLanguageSelection);
    setAdditionalLanguageSelection(initialAdditionalLanguageSelection);
  }, [initialAdditionalLanguageSelection, initialDefaultLanguageSelection]);

  return {
    addedLanguages,
    additionalLanguageSelection,
    defaultLanguageSelection,
    resetLanguageSelection,
    setAddedLanguages,
    setAdditionalLanguageSelection,
    setDefaultLanguageSelection,
  };
};

export default useLanguageState;
