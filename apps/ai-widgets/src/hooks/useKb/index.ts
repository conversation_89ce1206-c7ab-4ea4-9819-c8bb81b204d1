import useSWR from 'swr';

import { KB_PATH } from '@/constants/kb';
import { KbsAPI } from '@/services/api/kbs';

export const useKb = (kbId: string) => {
  return useSWR(
    !!kbId && [KB_PATH, kbId],
    async () => {
      const response = await KbsAPI.getKbById(kbId);

      return response.data;
    },
    {
      revalidateOnFocus: false,
    }
  );
};

export const useKbByIds = (kbIds: string[]) => {
  return useSWR(
    kbIds.map((kbId) => [KB_PATH, kbId]),
    async () => {
      return Promise.allSettled(
        kbIds.map(async (kbId) => {
          const response = await KbsAPI.getKbById(kbId);

          return response.data;
        })
      );
    },
    {
      revalidateOnFocus: false,
    }
  );
};
