import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';

import { WorkspaceAPI } from '@/services/api/workspace';
import type { Workspace } from '@/types/workspace';
import { useDefaultWorkspace } from './index';

// Mock the external dependencies
vi.mock('swr');

describe('useDefaultWorkspace', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should call useSWR with the correct key and fetcher', () => {
    // Mock the SWR hook
    const mockUseSWR = vi.mocked(useSWR);

    // Render the hook
    renderHook(() => useDefaultWorkspace());

    // Verify SWR was called with correct parameters
    expect(mockUseSWR).toHaveBeenCalledWith(
      '/workspaces/default',
      WorkspaceAPI.getDefaultWorkspace,
      {
        revalidateOnFocus: false,
      }
    );
  });

  it('should return widget data when API call is successful', () => {
    // Mock sample widget data
    const mockWorkspace: Workspace = {
      id: '1',
      name: 'Test Workspace 1',
      description: 'Description for test workspace 1',
      orgId: '1',
      settings: {},
      metadata: {},
      created: new Date().toISOString(),
      updated: new Date().toISOString(),
      default: true,
    };

    // Mock SWR to return the data
    vi.mocked(useSWR).mockReturnValue({
      data: mockWorkspace,
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useDefaultWorkspace());

    // Verify the returned data
    expect(result.current.data).toEqual(mockWorkspace);
    expect(result.current.error).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle API errors', () => {
    // Mock API error
    const mockError = new Error('Failed to fetch default workspace');

    // Mock SWR to return the error
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useDefaultWorkspace());

    // Verify the returned error
    expect(result.current.error).toEqual(mockError);
    expect(result.current.data).toBeUndefined();
  });

  it('should indicate loading state', () => {
    // Mock SWR to indicate loading
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isValidating: true,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useDefaultWorkspace());

    // Verify loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });
});
