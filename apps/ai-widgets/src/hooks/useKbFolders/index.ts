import useSWR from 'swr';

import { KB_FOLDERS_PATH } from '@/constants/kb';
import { KbsAPI } from '@/services/api/kbs';

export const useKbFolders = (...args: Parameters<typeof KbsAPI.getFolders>) => {
  const [parentDirId] = args;

  return useSWR(
    !!parentDirId && [KB_FOLDERS_PATH, ...args],
    async () => {
      const response = await KbsAPI.getFolders(...args);

      return response.data;
    },
    {
      revalidateOnFocus: false,
    }
  );
};

export const useKbListFolder = (folderIds: string[]) => {
  return useSWR(
    folderIds.map((folderId) => [KB_FOLDERS_PATH, folderId]),
    async () => {
      return Promise.allSettled(
        folderIds.map(async (folderId) => {
          const response = await KbsAPI.getFolder(folderId);

          return response.data;
        })
      );
    },
    {
      revalidateOnFocus: false,
    }
  );
};
