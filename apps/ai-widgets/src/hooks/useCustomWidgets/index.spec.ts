import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import useSWR from 'swr';

import { WidgetAPI } from '@/services/api';
import type { Widget } from '@/types/widget';
import { useCustomWidgets } from './index';

// Mock the external dependencies
vi.mock('swr');

describe('useCustomWidgets', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should call useSWR with the correct key and fetcher', () => {
    // Mock the SWR hook
    const mockUseSWR = vi.mocked(useSWR);

    // Render the hook
    renderHook(() => useCustomWidgets());

    // Verify SWR was called with correct parameters
    expect(mockUseSWR).toHaveBeenCalledWith('/widget-custom', WidgetAPI.getCustomList, {
      revalidateOnFocus: false,
    });
  });

  it('should return widget data when API call is successful', () => {
    // Mock sample widget data
    const mockWidgets: Widget[] = [
      {
        id: '1',
        name: 'Test Widget 1',
        description: 'Description for test widget 1',
        type: 'KB_SEARCH',
        created: new Date(),
        updated: new Date(),
      },
      {
        id: '2',
        name: 'Test Widget 2',
        description: 'Description for test widget 2',
        type: 'BROWSER_EXTENSION',
        created: new Date(),
        updated: new Date(),
      },
    ];

    // Mock SWR to return the data
    vi.mocked(useSWR).mockReturnValue({
      data: mockWidgets,
      error: undefined,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useCustomWidgets());

    // Verify the returned data
    expect(result.current.data).toEqual(mockWidgets);
    expect(result.current.error).toBeUndefined();
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle API errors', () => {
    // Mock API error
    const mockError = new Error('Failed to fetch custom widgets');

    // Mock SWR to return the error
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: mockError,
      isLoading: false,
      isValidating: false,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useCustomWidgets());

    // Verify the returned error
    expect(result.current.error).toEqual(mockError);
    expect(result.current.data).toBeUndefined();
  });

  it('should indicate loading state', () => {
    // Mock SWR to indicate loading
    vi.mocked(useSWR).mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
      isValidating: true,
      mutate: vi.fn(),
    });

    // Render the hook
    const { result } = renderHook(() => useCustomWidgets());

    // Verify loading state
    expect(result.current.isLoading).toBe(true);
    expect(result.current.data).toBeUndefined();
    expect(result.current.error).toBeUndefined();
  });
});
