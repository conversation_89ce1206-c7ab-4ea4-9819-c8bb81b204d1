import { screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import userEvent from '@testing-library/user-event';

import { renderWithMantine } from '@/utils/test';
import HomePage from './index';

// Mock the components
vi.mock('@/components/InstalledWidgets', () => ({
  default: () => <div />,
}));

vi.mock('@/components/AllWidgets', () => ({
  default: () => <div />,
}));

vi.mock('@resola-ai/services-shared', () => ({
  executeRequest: vi.fn(),
}));

// Mock react-router-dom with proper hoisting support
const mockSetSearchParams = vi.fn();
let mockSearchParams: URLSearchParams;

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useSearchParams: () => [mockSearchParams, mockSetSearchParams],
  };
});

describe('HomePage Component', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();

    // Default to 'installed' tab
    mockSearchParams = new URLSearchParams();
    mockSearchParams.set('tab', 'installed');

    mockSearchParams.get = vi.fn((key) => {
      if (key === 'tab') return 'installed';
      return null;
    });
  });

  it('renders the component with title', () => {
    renderWithMantine(<HomePage />);

    expect(screen.getByText('title')).toBeInTheDocument();
  });

  it('renders tabs correctly', () => {
    renderWithMantine(<HomePage />);

    expect(screen.getByText('settings.installed.tabTitle')).toBeInTheDocument();
    expect(screen.getByText('settings.all.tabTitle')).toBeInTheDocument();
  });

  it('shows the InstalledWidgets component by default', () => {
    renderWithMantine(<HomePage />);

    const tabPanel = screen.getByRole('tabpanel');
    expect(tabPanel).toHaveAttribute('data-testid', 'installed-widgets');
  });

  it('changes tab when clicked', async () => {
    const { rerender } = renderWithMantine(<HomePage />);

    await user.click(screen.getByText('settings.all.tabTitle'));

    expect(mockSetSearchParams).toHaveBeenCalled();

    // Simulate tab change
    mockSearchParams = new URLSearchParams();
    mockSearchParams.set('tab', 'all');

    mockSearchParams.get = vi.fn((key) => {
      if (key === 'tab') return 'all';
      return null;
    });

    // Render again to simulate re-render after state change
    rerender(<HomePage />);

    // All widgets tab should be active after tab change
    const tabPanel = screen.getByRole('tabpanel');
    expect(tabPanel).toHaveAttribute('data-testid', 'all-widgets');
  });

  it('uses URL param for initial tab selection', () => {
    mockSearchParams = new URLSearchParams();
    mockSearchParams.set('tab', 'all');

    mockSearchParams.get = vi.fn((key) => {
      if (key === 'tab') return 'all';
      return null;
    });

    renderWithMantine(<HomePage />);

    // All widgets tab should be active
    const tabPanel = screen.getByRole('tabpanel');
    expect(tabPanel).toHaveAttribute('data-testid', 'all-widgets');
  });
});
