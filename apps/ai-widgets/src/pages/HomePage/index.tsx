import { useCallback, useState } from 'react';
import { Box, rem, Tabs, Title } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { useTranslate } from '@tolgee/react';
import { useSearchParams } from 'react-router-dom';
import { IconDownload, IconLayoutGrid } from '@tabler/icons-react';

import InstalledWidgets from '@/components/InstalledWidgets';
import AllWidgets from '@/components/AllWidgets';

const useStyles = createStyles((theme) => ({
  container: {
    width: `calc(${rem('100%')} - ${rem(56)})`,
    marginLeft: rem(56),
    padding: rem(30),
  },
  wrapper: {
    maxWidth: rem(1200),
    height: '100%',
    margin: '0 auto',
  },
  root: {
    marginTop: rem(30),
  },
  tab: {
    color: theme.colors.decaDark[4],
    '&[data-active="true"]': {
      color: theme.colors.decaNavy[4],
      borderColor: theme.colors.decaNavy[4],
    },
  },
  tabLabel: {
    fontSize: theme.fontSizes.lg,
    fontWeight: 700,
    lineHeight: theme.other.lineHeights.lg,
  },
}));

const HomePage = () => {
  const { classes } = useStyles();
  const { t } = useTranslate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [tab] = useState<string>(searchParams.get('tab') || 'installed');

  const onTabChange = useCallback(
    (value: string | null) => {
      setSearchParams((prev) => {
        prev.set('tab', value ?? 'installed');

        return prev;
      });
    },
    [searchParams]
  );

  return (
    <Box className={classes.container}>
      <Box className={classes.wrapper}>
        <Title order={4} c='decaGrey.9'>
          {t('title')}
        </Title>
        <Tabs defaultValue={tab} classNames={classes} onChange={onTabChange}>
          <Tabs.List>
            <Tabs.Tab value='installed' leftSection={<IconDownload />} data-testid='installed-tab'>
              {t('settings.installed.tabTitle')}
            </Tabs.Tab>
            <Tabs.Tab value='all' leftSection={<IconLayoutGrid />} data-testid='all-tab'>
              {t('settings.all.tabTitle')}
            </Tabs.Tab>
          </Tabs.List>
          <Tabs.Panel value='installed' pt='xs' data-testid='installed-widgets'>
            <InstalledWidgets />
          </Tabs.Panel>
          <Tabs.Panel value='all' pt='xs' data-testid='all-widgets'>
            <AllWidgets />
          </Tabs.Panel>
        </Tabs>
      </Box>
    </Box>
  );
};

export default HomePage;
