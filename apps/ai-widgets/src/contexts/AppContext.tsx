import { createContext, useCallback, useContext, useState } from 'react';
import { useTranslate } from '@tolgee/react';

import ConfirmModal, {
  type ConfirmModalOptions,
  type ConfirmModalProps,
} from '@/components/ConfirmModal';

const useApp = () => {
  const { t } = useTranslate();
  const [confirmModal, setConfirmModal] = useState<ConfirmModalProps>({
    opened: false,
    title: '',
    content: '',
    confirmText: '',
    cancelText: '',
    onConfirm: () => {},
    onCancel: () => {},
    options: {},
  });

  const closeConfirmModal = useCallback(
    (options: ConfirmModalOptions = {}) => {
      setConfirmModal((prev) => ({
        ...prev,
        opened: false,
      }));

      setTimeout(() => {
        setConfirmModal({
          opened: false,
          title: t('confirmModalTitleDefault'),
          content: '',
          confirmText: '',
          cancelText: '',
          onConfirm: () => {},
          onCancel: () => {},
          options,
        });
      }, 200);
    },
    [t]
  );

  const openConfirmModal = useCallback(
    ({
      onConfirm,
      title,
      content,
      confirmText,
      cancelText,
      options,
      onCancel,
    }: Partial<ConfirmModalProps>) => {
      setConfirmModal({
        opened: true,
        title: title,
        content: content || t('modal.confirm.defaultTitle'),
        confirmText,
        cancelText,
        onConfirm: onConfirm || (() => {}),
        onCancel: onCancel ? onCancel : () => closeConfirmModal(options),
        options,
      });
    },
    [closeConfirmModal, t]
  );

  const setConfirmModalLoading = useCallback((isLoading: boolean) => {
    setConfirmModal((prev) => ({
      ...prev,
      options: {
        ...prev.options,
        isConfirmLoading: isLoading,
      },
    }));
  }, []);

  return {
    confirmModal,
    openConfirmModal,
    closeConfirmModal,
    setConfirmModalLoading,
  };
};

export type AppContextType = ReturnType<typeof useApp>;

const context = createContext<AppContextType | null>(null);

export const AppContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useApp();

  return (
    <context.Provider value={value}>
      {children}
      <ConfirmModal {...value.confirmModal} />
    </context.Provider>
  );
};

export const useAppContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useAppContext must be used inside AppContextProvider');
  }

  return value;
};
