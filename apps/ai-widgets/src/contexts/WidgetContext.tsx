import type React from 'react';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';

import { WidgetInstanceAPI } from '@/services/api';
import type { WidgetPayload } from '@/types/widget';
import { useWidgetInstances } from '@/hooks/useWidgetInstances';
import { useWidgets } from '@/hooks/useWidgets';
import { useCustomWidgets } from '@/hooks/useCustomWidgets';
import { useDefaultWorkspace } from '@/hooks/useDefaultWorkspace';

const useWidget = () => {
  const [wsId, setWsId] = useState<string>('');
  const { data: defaultWorkspace } = useDefaultWorkspace();
  const { data: widgets = [] } = useWidgets();
  const { data: customWidgets = [] } = useCustomWidgets();
  const { data: widgetInstances = [], mutate: mutateWidgetInstances } = useWidgetInstances(wsId);

  useEffect(() => {
    if (defaultWorkspace) {
      setWsId(defaultWorkspace.id);
    }
  }, [defaultWorkspace]);

  const removeWidgetInstance = useCallback(
    async (id: string) => {
      await mutateWidgetInstances(
        async () => {
          await WidgetInstanceAPI.remove(wsId, id);

          return widgetInstances.filter((widgetInstance) => widgetInstance.id !== id);
        },
        { revalidate: false }
      );
    },
    [mutateWidgetInstances, widgetInstances, wsId]
  );

  const installWidgetInstance = useCallback(
    async (widgetId: string, payload: WidgetPayload) => {
      try {
        await mutateWidgetInstances(
          async () => {
            const response = await WidgetInstanceAPI.create(wsId, widgetId, payload);

            return [response, ...widgetInstances];
          },
          { revalidate: false }
        );
      } catch (error) {
        if ((error as Error).message === 'Duplicated' || (error as Error).message === 'Limited') {
          throw error;
        }
      }
    },
    [mutateWidgetInstances, widgetInstances, wsId]
  );

  const updateWidgetInstance = useCallback(
    async (id: string, payload: Omit<WidgetPayload, 'settings'>) => {
      try {
        await mutateWidgetInstances(
          async () => {
            const response = await WidgetInstanceAPI.update(wsId, id, payload);

            return widgetInstances.map((widgetInstance) =>
              widgetInstance.id === id ? response : widgetInstance
            );
          },
          { revalidate: false }
        );
      } catch (error) {
        if ((error as Error).message === 'Duplicated') {
          throw error;
        }
      }
    },
    [mutateWidgetInstances, widgetInstances, wsId]
  );

  const updateWidgetInstanceSettings = useCallback(
    async (id: string, payload: Record<string, any> = {}) => {
      try {
        await mutateWidgetInstances(
          async () => {
            const response = await WidgetInstanceAPI.updateSettings(wsId, id, payload);

            return widgetInstances.map((widgetInstance) =>
              widgetInstance.id === id ? response : widgetInstance
            );
          },
          { revalidate: false }
        );
      } catch (error) {
        if ((error as Error).message === 'Duplicated') {
          throw error;
        }
      }
    },
    [mutateWidgetInstances, widgetInstances, wsId]
  );

  const syncCustomWidgetInstanceCompilation = useCallback(
    async (id: string) => {
      try {
        await mutateWidgetInstances(
          async () => {
            const response = await WidgetInstanceAPI.syncCompilation(wsId, id);

            return widgetInstances.map((widgetInstance) =>
              widgetInstance.id === id ? response : widgetInstance
            );
          },
          { revalidate: false }
        );
      } catch (error) {
        console.error('Sync compilation error', error);
        throw error;
      }
    },
    [widgetInstances, wsId]
  );

  return {
    widgets,
    customWidgets,
    widgetInstances,
    removeWidgetInstance,
    installWidgetInstance,
    updateWidgetInstance,
    updateWidgetInstanceSettings,
    syncCustomWidgetInstanceCompilation,
  };
};

export type WidgetContextType = ReturnType<typeof useWidget>;

const context = createContext<WidgetContextType | null>(null);

export const WidgetContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useWidget();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useWidgetContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useWidgetContext must be used inside WidgetContextProvider');
  }

  return value;
};
