import { createContext, useCallback, useContext, useEffect, useState } from 'react';

import { DEFAULT_TAKE, ROOT_PATH } from '@/constants/kb';
import { sortUnionItems } from '@/utils/kb';
import { useKbFolders } from '@/hooks/useKbFolders';
import type { Folder } from '@/types';

const useFolder = () => {
  const [folders, setFolders] = useState<Folder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState<string>();
  const [fetchFoldersParams, setFetchFoldersParams] = useState<Parameters<typeof useKbFolders>>([
    '',
    undefined,
    DEFAULT_TAKE,
  ]);
  const { data: responseFolders, isLoading } = useKbFolders(...fetchFoldersParams);

  useEffect(() => {
    if (!responseFolders?.length) return;

    setFolders((prevFolders) => sortUnionItems(responseFolders, prevFolders));
  }, [responseFolders]);

  const fetchFolders = useCallback((parentDirId?: string, depth?: number) => {
    setFetchFoldersParams([parentDirId ?? ROOT_PATH, depth, DEFAULT_TAKE]);
  }, []);

  return {
    folders,
    loading: isLoading,
    fetchFolders,
    selectedFolder,
    setSelectedFolder,
  };
};

export type FolderContextType = ReturnType<typeof useFolder>;
const context = createContext<FolderContextType | null>(null);

export const FolderContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useFolder();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useFolderContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useFolderContext must be used within a FolderContextProvider');
  }

  return value;
};
