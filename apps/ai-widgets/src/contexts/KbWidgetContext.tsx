import { type Folder, type Kb, type KBDocument, KbDirection } from '@/types';
import { createContext, useCallback, useContext, useEffect, useState } from 'react';

import { DEFAULT_KB_RETRIEVE_LIMIT, ROOT_PATH } from '@/constants/kb';
import { sortUnionItems } from '@/utils/kb';
import { useKb, useKbByIds } from '@/hooks/useKb';
import { useKbFolder } from '@/hooks/useKbFolder';
import { useKbFolders, useKbListFolder } from '@/hooks/useKbFolders';
import { useKbFolderSearch } from '@/hooks/useKbFolderSearch';
import { useKbs } from '@/hooks/useKbs';
import { useKbCombineSearch } from '@/hooks/useKbCombineSearch';
import { useKbDocuments, useKbListDocuments } from '@/hooks/useKbDocuments';

const DefaultKbPayloadCapture = {
  parentDirId: '',
  direction: KbDirection.Backward,
  pagination: { hasNextPage: false, hasPreviousPage: false, first: '', last: '' },
};

const DefaultKbDocumentPayloadCapture = {
  parentDirId: '',
  direction: KbDirection.Backward,
  pagination: { hasNextPage: false, hasPreviousPage: false, first: '', last: '' },
};

const initialBasesParams: Parameters<typeof useKbs> = [
  '',
  DEFAULT_KB_RETRIEVE_LIMIT,
  '',
  KbDirection.Backward,
  [],
];

const initialDocumentsParams: Parameters<typeof useKbDocuments> = [
  '',
  DEFAULT_KB_RETRIEVE_LIMIT,
  '',
  KbDirection.Backward,
];

const useKbWidget = () => {
  const [allBases, setAllBases] = useState<Kb[]>([]);
  const [bases, setBases] = useState<Kb[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [kbPayloadCapture, setKbPayloadCapture] = useState(DefaultKbPayloadCapture);
  const [searchFolders, setSearchFolders] = useState<Folder[]>([]);
  const [selectedFolder, setSelectedFolder] = useState('');
  const [selectedFolders, setSelectedFolders] = useState<Folder[]>([]);
  const [selectedKb, setSelectedKb] = useState('');
  const [selectedKbs, setSelectedKbs] = useState<Kb[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<KBDocument[]>([]);
  const [tempBases, setTempBases] = useState<Kb[]>([]);
  const [allDocuments, setAllDocuments] = useState<KBDocument[]>([]);
  const [documents, setDocuments] = useState<KBDocument[]>([]);
  const [kbDocumentPayloadCapture, setKbDocumentPayloadCapture] = useState(
    DefaultKbDocumentPayloadCapture
  );
  const [basesAndDocuments, setBasesAndDocuments] = useState<Kb[] | KBDocument[]>([]);

  const [basesParams, setBasesParams] = useState(initialBasesParams);
  const [moreBasesParams, setMoreBasesParams] = useState(initialBasesParams);
  const [tempBasesParams, setTempBasesParams] = useState(initialBasesParams);
  const [fetchKbId, setFetchKbId] = useState('');
  const [fetchKbIds, setFetchKbIds] = useState<string[]>([]);
  const [fetchFoldersParams, setFetchFoldersParams] = useState<Parameters<typeof useKbFolders>>([
    '',
  ]);
  const [documentsParams, setDocumentsParams] = useState(initialDocumentsParams);
  const [moreDocumentsParams, setMoreDocumentsParams] = useState(initialDocumentsParams);
  const [fetchFolderId, setFetchFolderId] = useState('');
  const [fetchFolderIds, setFetchFolderIds] = useState<string[]>([]);
  const [fetchDocumentsIds, setFetchDocumentsIds] = useState<string[]>([]);
  const [deletedFolderIds, setDeletedFolderIds] = useState<string[]>([]);
  const [deletedKbIds, setDeletedKbIds] = useState<string[]>([]);
  const [searchFolderQuery, setSearchFolderQuery] = useState('');
  const [searchCombinedQuery, setSearchCombinedQuery] = useState('');

  const { data: responseDocuments, isLoading: loadingDocuments } = useKbDocuments(
    ...documentsParams
  );
  const { data: moreDocumentsResponse, isLoading: loadingMoreDocuments } = useKbDocuments(
    ...moreDocumentsParams
  );
  const { data: basesResponse, isLoading: loadingBases } = useKbs(...basesParams);
  const { data: moreBasesResponse, isLoading: loadingMoreBases } = useKbs(...moreBasesParams);
  const { data: tempBasesResponse, isLoading: loadingSearch } = useKbs(...tempBasesParams);
  const { data: responseKbs } = useKbByIds(fetchKbIds ?? []);
  const { data: responseKb } = useKb(fetchKbId);
  const { data: responseFolders } = useKbFolders(...fetchFoldersParams);
  const { data: responseFolder } = useKbFolder(fetchFolderId);
  const { data: searchFoldersResponse } = useKbFolderSearch(searchFolderQuery);
  const { data: searchCombinedResponse } = useKbCombineSearch(searchCombinedQuery);
  const { data: responseFolderIds } = useKbListFolder(fetchFolderIds);
  const { data: responseDocumentsByIds } = useKbListDocuments(fetchDocumentsIds);
  // merge bases and documents
  useEffect(() => {
    setBasesAndDocuments(sortUnionItems([...bases, ...documents] as any, []));
  }, [bases, documents]);

  useEffect(() => {
    setDocumentsParams([selectedFolder, DEFAULT_KB_RETRIEVE_LIMIT, '', 'backward']);
  }, [selectedFolder]);

  useEffect(() => {
    if (!responseDocuments) return;

    setDocuments(responseDocuments.data);
    setAllDocuments((prevDocuments) => sortUnionItems(responseDocuments.data, prevDocuments));
  }, [responseDocuments]);

  useEffect(() => {
    if (!moreDocumentsResponse) return;

    setKbDocumentPayloadCapture((prevPayload) => ({
      ...(prevPayload ?? { parentDirId: ROOT_PATH, direction: KbDirection.Backward }),
      pagination: moreDocumentsResponse.pagination,
    }));

    if (!moreDocumentsResponse.data.length) return;
    setDocuments((prevDocuments) => [...prevDocuments, ...moreDocumentsResponse.data]);
    setAllDocuments((prevDocuments) => sortUnionItems(moreDocumentsResponse.data, prevDocuments));
  }, [moreDocumentsResponse]);

  useEffect(() => {
    if (!basesResponse?.data.length) return;

    setBases(basesResponse.data);
    setAllBases((prevBases) => sortUnionItems(basesResponse.data, prevBases));
  }, [basesResponse]);

  useEffect(() => {
    if (!moreBasesResponse) return;

    setKbPayloadCapture((prevPayload) => ({
      ...(prevPayload ?? { parentDirId: ROOT_PATH, direction: KbDirection.Backward }),
      pagination: moreBasesResponse.pagination,
    }));

    if (!moreBasesResponse.data.length) return;

    setBases((prevBases) => [...prevBases, ...moreBasesResponse.data]);
    setAllBases((prevBases) => sortUnionItems(moreBasesResponse.data, prevBases));
  }, [moreBasesResponse]);

  useEffect(() => {
    if (!tempBasesResponse) return;

    setTempBases(tempBasesResponse.data);

    if (!tempBasesResponse.data.length) return;

    setAllBases((prevBases) => sortUnionItems(tempBasesResponse.data, prevBases));
  }, [tempBasesResponse]);

  useEffect(() => {
    if (!basesResponse) return;

    const [parentDirId] = basesParams;

    setKbPayloadCapture({
      parentDirId: parentDirId ?? ROOT_PATH,
      direction: KbDirection.Backward,
      pagination: basesResponse.pagination,
    });
  }, [basesParams, basesResponse]);

  useEffect(() => {
    if (!responseKb) return;

    setAllBases((prevBases) => sortUnionItems([responseKb], prevBases));
    setSelectedKbs((prevBases) => [...prevBases, responseKb]);
  }, [responseKb]);

  useEffect(() => {
    if (!responseKbs?.length) return;

    const successKbs = responseKbs
      .filter((item): item is PromiseFulfilledResult<Kb> => item.status === 'fulfilled')
      .map((item) => item.value);
    const failedKbs = responseKbs
      .filter((item): item is PromiseRejectedResult => item.status === 'rejected')
      .map((item) => item.reason.response.config.url.split('/').pop());

    setSelectedKbs((prevBases) => [...prevBases, ...successKbs]);
    setAllBases((prevBases) => sortUnionItems(successKbs, prevBases));
    setDeletedKbIds(failedKbs);
  }, [responseKbs]);

  useEffect(() => {
    if (!responseFolders?.length) return;

    setFolders((prevFolders) => sortUnionItems(responseFolders, prevFolders));
  }, [responseFolders]);

  useEffect(() => {
    if (!responseFolderIds?.length) return;

    const successFolders = responseFolderIds
      .filter((item): item is PromiseFulfilledResult<Folder> => item.status === 'fulfilled')
      .map((item) => item.value);
    const failedFolders = responseFolderIds
      .filter((item): item is PromiseRejectedResult => item.status === 'rejected')
      .map((item) => item.reason.response.config.url.split('/').pop());

    setSelectedFolders((prevSelectedFolders) =>
      sortUnionItems(successFolders, prevSelectedFolders)
    );
    setFolders((prevFolders) => sortUnionItems(successFolders, prevFolders));
    setDeletedFolderIds(failedFolders);
  }, [responseFolderIds]);

  useEffect(() => {
    if (!responseDocumentsByIds) return;

    setDocuments(responseDocumentsByIds);
    setAllDocuments((prevDocuments) => sortUnionItems(responseDocumentsByIds, prevDocuments));

    setSelectedDocuments((prevSelectedDocuments) =>
      sortUnionItems(responseDocumentsByIds, prevSelectedDocuments)
    );
  }, [responseDocumentsByIds]);

  useEffect(() => {
    if (!responseFolder) return;

    setFolders((prevFolders) => sortUnionItems([responseFolder], prevFolders));
    setSelectedFolders((prevSelectedFolders) => [...prevSelectedFolders, responseFolder]);
  }, [responseFolder]);

  useEffect(() => {
    setTempBases([]);
    setSelectedFolder('');

    if (!searchFoldersResponse) return;

    setSearchFolders(searchFoldersResponse.data);

    if (!searchFoldersResponse.data.length) return;

    setFolders((prevFolders) => sortUnionItems(searchFoldersResponse.data, prevFolders));
  }, [searchFoldersResponse]);

  useEffect(() => {
    setTempBases([]);
    setSelectedFolder('');
    setSelectedKb('');
    if (!searchCombinedResponse) return;

    const items = searchCombinedResponse.data;
    const folders = items
      .filter((item) => item.type === 'folder')
      .map((item) => item.data) as Folder[];
    const kbs: Kb[] = [];
    const docs: KBDocument[] = [];

    items
      .filter((item) => item.type !== 'folder')
      .forEach((item) => {
        if ('metadata' in item.data && 'type' in item.data) {
          docs.push(item.data as KBDocument);
        } else {
          kbs.push(item.data as Kb);
        }
      });

    setSearchFolders(folders);
    setTempBases(kbs);
    setDocuments(docs);

    if (!searchCombinedResponse.data.length) return;

    setAllBases((prevBases) => sortUnionItems(kbs, prevBases));
    setFolders((prevFolders) => sortUnionItems(folders, prevFolders));
    setAllDocuments((prevDocuments) => sortUnionItems(docs, prevDocuments));
  }, [searchCombinedResponse]);

  const fetchFolders = useCallback((parentDirId = ROOT_PATH, depth?: number) => {
    setFetchFoldersParams([parentDirId, depth]);
  }, []);

  const fetchFolderById = useCallback((folderId: string) => {
    setFetchFolderId(folderId);
  }, []);

  const fetchFolderByIds = useCallback((folderIds: string[]) => {
    setFetchFolderIds(folderIds);
  }, []);

  const fetchDocumentsByIds = useCallback((documentIds: string[]) => {
    setFetchDocumentsIds(documentIds);
  }, []);

  const fetchBases = useCallback((parentDirId?: string) => {
    setBasesParams([parentDirId, DEFAULT_KB_RETRIEVE_LIMIT, '', KbDirection.Backward]);
  }, []);

  const fetchDocuments = useCallback((parentDirId?: string) => {
    setDocumentsParams([parentDirId ?? '', DEFAULT_KB_RETRIEVE_LIMIT, '', KbDirection.Backward]);
  }, []);

  const fetchKbById = useCallback((kbId: string) => {
    setFetchKbId(kbId);
  }, []);

  const fetchKbsByIds = useCallback((kbIds: string[]) => {
    setFetchKbIds(kbIds);
  }, []);

  const fetchMoreKnowledgeBases = useCallback(() => {
    const { parentDirId, direction, pagination } = kbPayloadCapture;

    if (!pagination?.hasPreviousPage || !parentDirId) return;

    const cursor = direction === KbDirection.Backward ? pagination?.first : pagination?.last;

    setMoreBasesParams([parentDirId, DEFAULT_KB_RETRIEVE_LIMIT, cursor, KbDirection.Backward]);
  }, [kbPayloadCapture]);

  const fetchMoreDocuments = useCallback(() => {
    const { parentDirId, direction, pagination } = kbDocumentPayloadCapture;

    if (!pagination?.hasPreviousPage || !parentDirId) return;

    const cursor = direction === KbDirection.Backward ? pagination?.first : pagination?.last;

    setMoreDocumentsParams([parentDirId, DEFAULT_KB_RETRIEVE_LIMIT, cursor, KbDirection.Backward]);
  }, [kbDocumentPayloadCapture]);

  const handleSearchFolders = useCallback((query: string) => {
    setSearchFolderQuery(query);
  }, []);

  const handleSearchCombined = useCallback((query: string) => {
    setSearchCombinedQuery(query);
  }, []);

  const fetchTempBases = useCallback((parentDirId?: string) => {
    setTempBasesParams([parentDirId, DEFAULT_KB_RETRIEVE_LIMIT, '', KbDirection.Backward]);
  }, []);

  const cleanup = useCallback(() => {
    setSearchFolders([]);
    setSelectedFolder('');
    setSelectedFolders([]);
    setSelectedKb('');
    setSelectedKbs([]);
    setTempBases([]);
    setDocumentsParams(initialDocumentsParams);
    setBasesParams(initialBasesParams);
    setMoreBasesParams(initialBasesParams);
    setTempBasesParams(initialBasesParams);
    setFetchKbIds([]);
    setFetchKbId('');
    setFetchFoldersParams(['']);
    setSearchFolderQuery('');
    setFetchFolderId('');
    setFetchFolderIds([]);
    setFetchDocumentsIds([]);
    setSearchCombinedQuery('');
    setDeletedFolderIds([]);
    setDeletedKbIds([]);
  }, []);

  return {
    allBases,
    allDocuments,
    basesAndDocuments,
    bases,
    cleanup,
    deletedFolderIds,
    deletedKbIds,
    documents,
    fetchBases,
    fetchFolderById,
    fetchFolderByIds,
    fetchFolders,
    fetchKbById,
    fetchKbsByIds,
    fetchMoreKnowledgeBases,
    fetchMoreDocuments,
    fetchTempBases,
    fetchDocumentsByIds,
    fetchDocuments,
    folders,
    loading: loadingBases || loadingDocuments,
    loadingMore: loadingMoreBases || loadingMoreDocuments,
    loadingSearch,
    searchFolderAndKb: handleSearchFolders,
    searchCombineFolderAndKb: handleSearchCombined,
    searchFolders,
    selectedFolder,
    selectedFolders,
    selectedKb,
    selectedKbs,
    selectedDocuments,
    setBases,
    setSelectedFolder,
    setSelectedKb,
    setSelectedFolders,
    setSelectedKbs,
    setSelectedDocuments,
    setTempBases,
    tempBases,
    latestSearchCombine: searchCombinedResponse,
  };
};

export type KbWidgetContextType = ReturnType<typeof useKbWidget>;
const context = createContext<KbWidgetContextType | null>(null);

export const KbWidgetContextProvider = ({ children }: { children: React.ReactNode }) => {
  const value = useKbWidget();

  return <context.Provider value={value}>{children}</context.Provider>;
};

export const useKbWidgetContext = () => {
  const value = useContext(context);

  if (!value) {
    throw new Error('useFolderContext must be used within a FolderContextProvider');
  }

  return value;
};
