import type { Folder } from '@/types';

export const ROOT_PATH = '/root';
export const DEFAULT_TAKE = 50;
export const DEFAULT_KB_RETRIEVE_LIMIT = 20;
export const KB_FOLDERS_PATH = '/knowledgebases/folders';
export const KB_COMBINED_PATH = '/knowledgebases/combined';
export const KB_PATH = '/knowledgebases/bases';

export const KB_API_URL = 'https://api.kb.deca-dev.com/v2';
export const KB_API_PROD_URL = 'https://api.kb.deca.cloud/v2';
export const KB_FOLDERS_SHORT_PATH = '/folders/';
export const KB_BASES_SHORT_PATH = '/bases/';

export const ROOT_FOLDER = {
  id: ROOT_PATH,
  name: 'tree.root',
  path: ROOT_PATH,
  childFolderCount: 0,
  childKbCount: 0,
  createdAt: new Date(),
  updatedAt: new Date(),
} as Folder;
