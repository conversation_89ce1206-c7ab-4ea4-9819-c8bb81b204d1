import type { WidgetType, WidgetTypeInfo } from '@/types/widget';

export const SETTINGS_WIDGET_TYPES = [
  'KB_SEARCH',
  'REPLY_ASSISTANT',
  'PROOFREADING',
  'SUMMARIZATION',
  'TRANSLATION',
  'BROWSER_EXTENSION',
] satisfies WidgetType[];

export const WidgetInfo: WidgetTypeInfo = {
  KB_SEARCH: {
    title: 'widget.kbSearch.title',
    description: 'widget.kbSearch.description',
    isUsingAI: false,
  },
  REPLY_ASSISTANT: {
    title: 'widget.replyAssistant.title',
    description: 'widget.replyAssistant.description',
    isUsingAI: true,
  },
  PROOFREADING: {
    title: 'widget.proofreading.title',
    description: 'widget.proofreading.description',
    isUsingAI: true,
  },
  TONE_ADJUSTMENT: {
    title: 'widget.toneAdjustment.title',
    description: 'widget.toneAdjustment.description',
    isUsingAI: true,
  },
  SUMMARIZATION: {
    title: 'widget.summarization.title',
    description: 'widget.summarization.description',
    isUsingAI: true,
  },
  TRANSLATION: {
    title: 'widget.translation.title',
    description: 'widget.translation.description',
    isUsingAI: true,
  },
  EXPLANATION: {
    title: 'widget.explanation.title',
    description: 'widget.explanation.description',
    isUsingAI: true,
  },
  BROWSER_EXTENSION: {},
};

export const REPLY_ASSISTANT_DEFAULT_PROMPT =
  'あなたは優秀なカスタマーサポートオペレーターです。\n以下の#ユーザーの質問に対して、#参考情報をもとに回答を作成してください。\nなお、#参考情報にない情報は決して答えないでください。#参考情報が空の場合は、回答できないと答えてください。\n\n#ユーザーの質問\n{user_input}\n\n#参考情報\n{KB_result}';

export const PROOFREADING_DEFAULT_PROMPT =
  'あなたはユーザーが入力したテキストを校正する役割を持ちます。可能な限り全ての修正箇所を洗い出してください。各修正箇所は次のカテゴリのいずれかに分類してください：「文法」「表現」「入力ミス」「単語」「その他」（「その他」は分類が難しい場合）。１つの修正箇所につき１つの改善提案を出してください。';

export const SUMMARIZATION_DEFAULT_PROMPT = `あなたは優秀な要約作成者です。以下の情報を基に、簡潔でわかりやすい要約を作成してください。

指示事項:
情報分析: 提供された情報を詳細に分析し、重要なポイントや主要な考えを特定してください。
日本語要約の作成: 正確に核心を捉えた簡潔な要約を日本語で作成してください。
品質保証: 要約が明確で簡潔であることを確認し、不要な詳細は除外してください。両方の形式が日本語の適切な言語規則に準拠していることを確認し、読みやすさを確保してください。

入力情報:
{input_content}`;

export const SINGLE_WIDGET_INSTANCE_LIST = [
  'TONE_ADJUSTMENT',
  'TRANSLATION',
  'EXPLANATION',
] satisfies WidgetType[];

export enum REPLY_ASSISTANT_VARIABLES {
  USER_INPUT = '{user_input}',
  KB_RESULT = '{KB_result}',
}

export enum SUMMARIZATION_VARIABLES {
  INPUT_CONTENT = '{input_content}',
}
