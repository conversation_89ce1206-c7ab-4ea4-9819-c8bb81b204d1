import { rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';

export const useFormStyles = createStyles((theme) => ({
  root: {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    flexDirection: 'column',
    gap: theme.spacing.md,
    '.mantine-InputWrapper-root': {
      width: '100%',
      display: 'flex',
      justifyContent: 'flex-start',
      alignItems: 'flex-start',

      '&.mantine-TextInput-root, &.mantine-Textarea-root': {
        flexDirection: 'column',
      },
    },
    '.mantine-InputWrapper-label': {
      width: rem('30%'),
      minWidth: rem('30%'),
      marginRight: theme.spacing.xs,
      color: theme.colors.decaGrey[9],
      fontSize: theme.fontSizes.md,
      alignSelf: 'center',
    },
    '.mantine-Input-wrapper': {
      width: '100%',
    },
    '.mantine-InputWrapper-error': {
      width: '70%',
      display: 'flex',
    },
  },
  inputInner: {
    width: '100%',
    '.mantine-Input-wrapper': {
      width: '100%',
    },
  },
  modalTitle: {
    fontSize: rem(20),
    fontWeight: 700,
    lineHeight: rem(31),
    color: theme.colors.decaGrey[9],
  },
  modalContent: {
    minWidth: rem(695),
  },
  modalClose: {
    '&:focus': {
      outline: 'none',
    },
  },
  hide: {
    display: 'none',
  },
  treeBox: {
    width: '100%',
    overflow: 'auto',
    backgroundColor: theme.colors.decaLight[0],
    borderRadius: rem(6),
    padding: rem(20),
  },
  tooltipIcon: {
    color: theme.colors.decaLight[5],
  },
  topLabel: {
    alignSelf: 'flex-start !important',
  },
  searchBox: {
    width: '100%',
    borderRadius: rem(4),
  },
}));
