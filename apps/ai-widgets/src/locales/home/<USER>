{"aiWidgets": "AI Widgets", "common": {"button": {"changeFolder": "Change Folder", "close": "Close", "private": "Make it private", "public": "Make it public", "restoreDefault": "<PERSON><PERSON>", "save": "Save"}, "error": {"required": "This field is required."}, "label": {"description": "Description", "name": "Name"}, "placeholder": {"description": "This is an explanation for management purposes.", "name": "This is a name for management purposes."}, "searchResults": "Search Results ({count})"}, "default": {"error": {"duplicated": "A widget with this name already exists", "limited": "You have reached the maximum number of widgets allowed"}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}}, "title": "Widget <PERSON>s"}, "explanation": {"error": {"duplicated": "A widget with this name already exists", "limited": "You have reached the maximum number of widgets allowed"}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}}, "title": "Explanation Settings"}, "filters": {"all": "All Widgets", "custom": "Custom Widgets", "standard": "Standard Widgets"}, "home": "Home", "kb": {"allKnowledgeBases": "All Knowledge Bases", "error": {"deletedKbAndFolder": "All or some of the set target folders or KBs have been deleted.", "duplicated": "The widget cannot be created because the same search target is set as the existing widget. Please set a different search target.", "limited": "The number of Knowledge Search widgets for this organization has reached the limit of 10.", "reSelect": "Please set up the target folders and KBs again."}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}, "searchFolders": {"change": "Change Folder", "label": "Target Folders and KBs", "root": "All Knowledge Bases"}}, "foldersAndKbs": "Folders and Knowledge Bases", "label": {"keywordSearch": "Keyword search", "searchResults": "Search Results ({count})", "searchTargetFolder": "Search Target Folder", "selectFromTheFolderTree": "Select from the folder tree", "selectionMethod": "Selection Method"}, "searchFolderAndKb": "Search folders and knowledge bases", "searchTargetFolderSetting": "Search Target Folder Setting", "selectedFoldersAndKbs": "Selected Folders and Knowledge Bases", "selectFolderAndKb": "Select Folders and Knowledge Bases", "targetFolderExists": "The widget cannot be created because the same search target folder is set as the existing widget. Please set a different folder.", "title": "Knowledge Search Widget Settings"}, "modal": {"confirm": {"backBtn": "Back", "cancelBtn": "Cancel", "defaultTitle": "Are you sure you want to remove?", "noBtn": "No", "privateChange": "Do you want to make the widget \"{widgetName}\" private?", "publicChange": "Do you want to make the widget \"{widgetName}\" public?", "singleton": "This widget has been successfully installed and is automatically in private mode. Do you want to make it public on the application?", "uninstallBtn": "Uninstall", "widgetTitle": "Do you really want to delete the widget \"{widgetName}\"?", "yesBtn": "Yes"}, "success": {"settings": {"saved": "Your settings are saved."}}}, "noContent": "No Content", "proofreading": {"dictionaryTitle": "User Dictionary Settings", "error": {"duplicated": "The widget cannot be created because the same search target folder is set as the existing widget. Please set a different folder.", "limited": "The number of Proofreading widgets for this organization has reached the limit of 10.", "maxLines": "The number of lines entered exceeds 10,000; please limit the number of lines to 10,000 or less."}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}, "settings": {"prompt": {"label": "Prompt"}, "useAI": {"description": "Use AI", "label": "Use AI"}, "userDictionary": {"downloadCsvButton": "Download CSV", "placeholder": "例：Givery, ギブリー", "uploadCsvButton": "Upload CSV"}, "useUserDictionary": {"description": "Use user dictionary", "editButton": "Edit Dictionary", "label": "User Dictionary"}}, "status": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}}, "label": {"restoreDefault": "<PERSON><PERSON>", "userDictionary": "Enter the correct expression on the far left, and similar expressions on the right, separated by commas. When a similar expression is entered, the proofreading widget will suggest the correct expression. A maximum of 10,000 lines can be entered."}, "title": "Proofreading Widget Settings"}, "replyAssistant": {"error": {"deletedKbAndFolder": "All or some of the set target folders or KBs have been deleted.", "duplicated": "The widget cannot be created because it has the same search target and the same prompt as an existing widget. Please set a different search target or prompt.", "limited": "The number of Reply Assistant widgets for this organization has reached the limit of 10.", "prompt": "This field 「{variable}」is required.", "reSelect": "Please set up the target folders and KBs again."}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}, "prompt": {"label": "Prompt"}, "searchFolders": {"change": "Change Folder", "label": "Target Folders and KBs", "root": "All Knowledge Bases"}}, "label": {"keywordSearch": "Keyword search", "restoreDefault": "<PERSON><PERSON>", "selectFromTheFolderTree": "Select from the folder tree", "selectionMethod": "Selection Method"}, "targetFolderExists": "The widget cannot be created because it has the same search folder and the same prompt as an existing widget. Please set a different folder or prompt.", "targetKnowledgeBaseFolderTitle": "Select Folders and Knowledge Bases", "title": "Reply <PERSON><PERSON> Widget <PERSON>s"}, "settings": {"all": {"aiTooltip": "This widget uses AI", "browserExtensionTooltip": "Custom Widget", "button": {"install": "Install", "uninstall": "Uninstall"}, "latest": "Latest", "syncCompilation": {"error": "Failed to update to the latest version", "success": "Successfully updated to the latest version", "tooltip": "Click to update to the latest version"}, "tabTitle": "All Widgets", "version": {"noChangeLog": "No change log available for this version.", "title": "Updates for {name}"}, "widget": {"explanation": {"description": "This widget provides a clear explanation of the meaning of the specified text.", "title": "Explanation"}, "kbSearch": {"description": "This widget performs keyword searches of knowledge in the Knowledge Base.", "title": "Knowledge Search"}, "proofreading": {"description": "This widget points out errors and omissions in the input text, such as typos and grammatical errors, and offers suggestions for improvement. User dictionaries are also available.", "title": "Proofreading"}, "replyAssistant": {"description": "This widget automatically generates a reply to the input text based on the information in the knowledge base.", "title": "Reply Assistant"}, "summarization": {"description": "This widget summarizes the main points of the input text in a shorter number of words.", "title": "Summarization"}, "toneAdjustment": {"description": "This widget rewrites the input text with your preferred nuance, such as “polite” or “friendly”.", "title": "Tone Adjustment"}, "translation": {"description": "This widget converts the input text into a different language from the original, such as Japanese to English.", "title": "Translation"}}}, "installed": {"dragTips": "Widgets can be rearranged by drag and drop.", "noResults": "No installed widgets found.", "noWidgets": "There are no widgets installed.\n Please select the widget you wish to use from all widgets.", "private": "Private", "public": "Public", "search": {"button": "Search", "placeholder": "Please input keywords"}, "tabTitle": "Installed"}}, "summarization": {"error": {"prompt": "This field 「{variable}」is required."}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}, "prompt": {"label": "Prompt"}}, "label": {"restoreDefault": "<PERSON><PERSON>"}, "title": "Summarization Widget Settings"}, "title": "Widgets", "toneAdjustment": {"error": {"duplicated": "The widget cannot be created because the same search target folder is set as the existing widget. Please set a different folder.", "limited": "The number of Tone Adjustment widgets for this organization has reached the limit of 1"}, "fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "isPublic": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}}, "title": "Tone Adjustment Widget Settings"}, "translation": {"fields": {"description": {"label": "Description", "placeholder": "This is an explanation for management purposes."}, "languages": {"buttonText": "Select Languages", "label": "Languages to Display in the List"}, "name": {"label": "Name", "placeholder": "This is a name for management purposes."}, "status": {"description": "Make this widget public", "label": "Widget Publication Setting", "tooltip": "Once made public, end users will be able to use this widget on the DECA AI Widgets."}}, "label": {"addOtherLanguages": "Add Other Languages", "batchSelection": "Batch Selection"}, "languageSelectionDescription": "You can narrow down the languages that users can select on Translation Widget. By listing only the languages that are likely to be used, the amount of time spent selecting translation languages can be reduced.", "languageSelectionTitle": "Languages to Display in the Language List of Translation Widget", "settings": {"languages": {"label": "Frequently Used Languages"}, "userLanguages": {"label": "Languages Added by User"}}, "title": "Translation Widget Settings"}, "tree": {"root": "All Knowledge Bases"}}