import { CUSTOM_WIDGET_TYPES, type Widget, type WidgetInstance } from '@/types/widget';

export const isCustomWidget = (widgetType: string) => {
  return CUSTOM_WIDGET_TYPES.includes(widgetType);
};

export const isLatestCompilationWidgetInstance = (
  widget: Widget,
  widgetInstance: WidgetInstance
) => {
  const currentCompilation = widgetInstance.settings?.compilationId;
  const latestCompilation = widget.widget?.compilations[0]?.compilationId;

  return currentCompilation === latestCompilation;
};
