import { vi } from 'vitest';
import { render, type RenderOptions } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { emotionTransform, MantineEmotionProvider } from '@mantine/emotion';
import { themeConfigurations } from '@resola-ai/ui/constants';
import { MemoryRouter } from 'react-router-dom';
import { AppContextProvider } from '@/contexts/AppContext';
import { WidgetContextProvider } from '@/contexts/WidgetContext';

if (typeof global.ResizeObserver === 'undefined') {
  global.ResizeObserver = class MockResizeObserver {
    observe = vi.fn();
    unobserve = vi.fn();
    disconnect = vi.fn();
  };
}

export const MantineWrapper = ({ children }: { children: React.ReactNode }) => (
  <MantineProvider stylesTransform={emotionTransform} theme={themeConfigurations}>
    <MantineEmotionProvider>
      <MemoryRouter>
        <AppContextProvider>
          <WidgetContextProvider>{children}</WidgetContextProvider>
        </AppContextProvider>
      </MemoryRouter>
    </MantineEmotionProvider>
  </MantineProvider>
);

export const renderWithMantine = (ui: React.ReactNode, options?: RenderOptions) => {
  return render(ui, { wrapper: MantineWrapper, ...options });
};

export const mockLibraries = () => {
  vi.mock('react', async () => {
    const actual = await vi.importActual<typeof import('react')>('react');
    return {
      ...actual,
    };
  });

  vi.mock('@mantine/core', async () => {
    const actual = await vi.importActual<typeof import('@mantine/core')>('@mantine/core');
    return {
      ...actual,
    };
  });

  vi.mock('@tabler/icons-react', async () => {
    const actual =
      await vi.importActual<typeof import('@tabler/icons-react')>('@tabler/icons-react');
    return {
      ...actual,
    };
  });

  vi.mock('@mantine/emotion', async () => {
    const actual = await vi.importActual<typeof import('@mantine/emotion')>('@mantine/emotion');
    return {
      ...actual,
    };
  });

  vi.mock('@mantine/hooks', async () => {
    const actual = await vi.importActual<typeof import('@mantine/hooks')>('@mantine/hooks');
    return {
      ...actual,
    };
  });
};
