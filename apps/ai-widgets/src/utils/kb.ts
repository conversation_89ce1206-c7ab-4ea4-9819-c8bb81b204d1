import unionBy from 'lodash/unionBy';
import type { TFnType } from '@tolgee/web';

import { ROOT_PATH } from '@/constants/kb';
import type { Folder, Kb, KBDocument, TreeItem, Trees } from '@/types';

export const convertListToTree = (folders: Folder[]): Trees => {
  const map = new Map<string, TreeItem>();
  const root: TreeItem = {
    id: ROOT_PATH,
    name: 'tree.root',
    path: ROOT_PATH,
    count: 0,
    childFolderCount: 0,
    childKbCount: 0,
    children: [],
  };

  map.set(ROOT_PATH, root);

  folders.forEach((folder) => {
    map.set(folder.id, {
      ...folder,
      children: [],
    });
  });

  folders.forEach((folder) => {
    const node = map.get(folder.id)!;
    const parent = folder.parentDirId && map.get(folder.parentDirId);

    if (parent) {
      parent.children = [...(parent.children || []), node];
    }
  });

  root.count = root.children?.length || 0;
  root.childFolderCount = root.children?.length || 0;

  return [root];
};

export const sortUnionItems = <T extends Folder | Kb | KBDocument>(a: T[], b: T[]) =>
  unionBy([...a, ...b], 'id').sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

export const getSelectedKbAndFolderName = (
  searchBases: string[],
  searchFolders: string[],
  searchDocuments: string[],
  folders: Folder[],
  allBases: Kb[],
  allDocuments: KBDocument[],
  t: TFnType
) => {
  if (
    searchFolders.includes(ROOT_PATH) ||
    (!searchBases.length && !searchFolders.length && !searchDocuments?.length)
  ) {
    return t('kb.fields.searchFolders.root');
  }

  const folderName = folders
    ?.filter((f) => searchFolders.includes(f.id))
    ?.map((f) => f.name)
    ?.join('\n');
  const baseName = allBases
    ?.filter((b) => searchBases.includes(b.id))
    ?.map((b) => b.name)
    ?.join('\n');
  const documentName = allDocuments
    ?.filter((d) => searchDocuments.includes(d.id))
    ?.map((d) => d.metadata.name)
    ?.join('\n');

  const names = [folderName, baseName, documentName].filter(Boolean);

  return names.length ? names.join('\n') : '';
};
