import { describe, it, expect } from 'vitest';
import { convertListToTree, sortUnionItems, getSelectedKbAndFolderName } from './kb';
import { ROOT_PATH } from '@/constants/kb';
import type { Folder, Kb, KBDocument } from '@/types';

describe('kb utilities', () => {
  const mockFolders: Folder[] = [
    {
      id: 'folder1',
      name: 'Folder 1',
      path: '/folder1',
      parentDirId: ROOT_PATH,
      count: 0,
      childFolderCount: 0,
      childKbCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: 'folder2',
      name: 'Folder 2',
      path: '/folder2',
      parentDirId: ROOT_PATH,
      count: 0,
      childFolderCount: 0,
      childKbCount: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  describe('convertListToTree', () => {
    it('should convert an empty list to a root-only tree', () => {
      const result = convertListToTree([]);

      expect(result).toHaveLength(1);
      expect(result[0].id).toBe(ROOT_PATH);
      expect(result[0].children).toHaveLength(0);
      expect(result[0].count).toBe(0);
      expect(result[0].childFolderCount).toBe(0);
    });

    it('should convert a flat list to a tree structure', () => {
      const result = convertListToTree(mockFolders);

      expect(result).toHaveLength(1);
      expect(result[0].children).toHaveLength(2);
      expect(result[0].count).toBe(2);
      expect(result[0].childFolderCount).toBe(2);
    });

    it('should handle nested folders correctly', () => {
      const folders: Folder[] = [
        {
          id: 'folder1',
          name: 'Folder 1',
          path: '/folder1',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: 'subfolder1',
          name: 'Subfolder 1',
          path: '/folder1/subfolder1',
          parentDirId: 'folder1',
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const result = convertListToTree(folders);

      expect(result[0].children).toHaveLength(1);
      expect(result[0].children?.[0]?.id).toBe('folder1');
      expect(result[0].children?.[0]?.children).toHaveLength(1);
      expect(result[0].children?.[0]?.children?.[0]?.id).toBe('subfolder1');
    });
  });

  describe('sortUnionItems', () => {
    it('should union and sort items by creation date', () => {
      const date1 = new Date('2023-01-01');
      const date2 = new Date('2023-01-02');
      const date3 = new Date('2023-01-03');

      const folders1: Folder[] = [
        {
          id: 'folder1',
          name: 'Folder 1',
          path: '/folder1',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: date2,
          updatedAt: date2,
        },
      ];

      const folders2: Folder[] = [
        {
          id: 'folder1', // Same ID as in folders1
          name: 'Folder 1',
          path: '/folder1',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: date2,
          updatedAt: date2,
        },
        {
          id: 'folder2',
          name: 'Folder 2',
          path: '/folder2',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: date1,
          updatedAt: date1,
        },
        {
          id: 'folder3',
          name: 'Folder 3',
          path: '/folder3',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: date3,
          updatedAt: date3,
        },
      ];

      const result = sortUnionItems(folders1, folders2);

      expect(result).toHaveLength(3); // Duplicates are removed
      expect(result[0].id).toBe('folder2'); // Earliest date first
      expect(result[1].id).toBe('folder1');
      expect(result[2].id).toBe('folder3'); // Latest date last
    });
  });

  describe('getSelectedKbAndFolderName', () => {
    const mockT = vi.fn((key) => key);

    it('should return root text when ROOT_PATH is in searchFolders', () => {
      const result = getSelectedKbAndFolderName([], [ROOT_PATH], [], [], [], [], mockT);

      expect(result).toBe('kb.fields.searchFolders.root');
    });

    it('should return root text when all search arrays are empty', () => {
      const result = getSelectedKbAndFolderName([], [], [], [], [], [], mockT);

      expect(result).toBe('kb.fields.searchFolders.root');
    });

    it('should return folder names when folders are selected', () => {
      const result = getSelectedKbAndFolderName(
        [],
        ['folder1', 'folder2'],
        [],
        mockFolders,
        [],
        [],
        mockT
      );

      expect(result).toBe('Folder 1\nFolder 2');
    });

    it('should return base names when bases are selected', () => {
      const bases: Kb[] = [
        {
          id: 'base1',
          name: 'Base 1',
          description: 'Base 1 description',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'base2',
          name: 'Base 2',
          description: 'Base 2 description',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ];

      const result = getSelectedKbAndFolderName(['base1', 'base2'], [], [], [], bases, [], mockT);

      expect(result).toBe('Base 1\nBase 2');
    });

    it('should return document names when documents are selected', () => {
      const documents: KBDocument[] = [
        {
          id: 'doc1',
          metadata: {
            name: 'Document 1',
            contentType: 'text/plain',
            contentLength: 100,
            uploadUrl: 'https://example.com/upload',
            downloadUrl: 'https://example.com/download',
            uploadStatus: '',
            downloadUrlExpires: null,
            createdBy: '',
            previewText: '',
            documentContentPreview: '',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          type: 'document',
        },
        {
          id: 'doc2',
          metadata: {
            name: 'Document 2',
            contentType: '',
            contentLength: 0,
            uploadUrl: '',
            downloadUrl: '',
            uploadStatus: '',
            downloadUrlExpires: null,
            createdBy: '',
            previewText: '',
            documentContentPreview: '',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          type: 'document',
        },
      ];

      const result = getSelectedKbAndFolderName([], [], ['doc1', 'doc2'], [], [], documents, mockT);

      expect(result).toBe('Document 1\nDocument 2');
    });

    it('should combine names from different types when multiple types are selected', () => {
      const folders: Folder[] = [
        {
          id: 'folder1',
          name: 'Folder 1',
          path: '/folder1',
          parentDirId: ROOT_PATH,
          count: 0,
          childFolderCount: 0,
          childKbCount: 0,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      const bases: Kb[] = [
        {
          id: 'base1',
          name: 'Base 1',
          createdAt: new Date().toISOString(),
          description: '',
          updatedAt: '',
        },
      ];

      const documents: KBDocument[] = [
        {
          id: 'doc1',
          metadata: {
            name: 'Document 1',
            contentType: '',
            contentLength: 0,
            uploadUrl: '',
            downloadUrl: '',
            uploadStatus: '',
            downloadUrlExpires: null,
            createdBy: '',
            previewText: '',
            documentContentPreview: '',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          type: 'document',
        },
      ];

      const result = getSelectedKbAndFolderName(
        ['base1'],
        ['folder1'],
        ['doc1'],
        folders,
        bases,
        documents,
        mockT
      );

      expect(result).toBe('Folder 1\nBase 1\nDocument 1');
    });

    it('should return empty string when no matching items are found', () => {
      const result = getSelectedKbAndFolderName(
        ['nonexistent'],
        ['nonexistent'],
        ['nonexistent'],
        [],
        [],
        [],
        mockT
      );

      expect(result).toBe('');
    });
  });
});
