{"name": "ai-widgets", "private": true, "version": "0.13.0", "type": "module", "description": "DECA AI Widgets", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "biome lint .", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "biome format --write .", "lint-staged-check": "lint-staged", "lint:eslint:fix": "biome lint --apply .", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "release": "standard-version -t ai-widgets@", "release:minor": "standard-version -t ai-widgets@ --release-as minor", "release:patch": "standard-version -t ai-widgets@ --release-as patch", "release:major": "standard-version -t ai-widgets@ --release-as major", "translation:pull": "env-cmd tolgee pull", "translation:push": "env-cmd tolgee push"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tolgee/react": "^5.29.1", "@tolgee/web": "^5.29.2", "axios": "^1.8.2", "dayjs": "^1.11.12", "dotenv": "16.4.7", "lodash": "^4.17.21", "nanoid": "^5.0.9", "prettier": "^3.2.1", "react": "^18.2.0", "react-arborist": "^3.4.0", "react-dom": "^18.2.0", "react-hook-form": "7.54.2", "react-hook-form-mantine": "^3.1.3", "react-router-dom": "6.21.3", "standard-version": "^9.5.0", "swr": "^2.3.0", "vite-tsconfig-paths": "^4.2.0", "zod": "3.24.1"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/biome-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.3.0", "@testing-library/user-event": "14.6.1", "@tolgee/cli": "^2.4.1", "@types/lodash": "^4.17.13", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react-swc": "3.8.1", "@vitest/coverage-v8": "2.1.9", "env-cmd": "10.1.0", "husky": "^8.0.3", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "vite": "5.4.19", "vite-plugin-circular-dependency": "^0.4.1", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx,jsx}": ["biome format --write", "biome lint --apply"]}}