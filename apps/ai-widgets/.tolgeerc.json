{"$schema": "https://tolgee.io/cli-schema.json", "projectId": 11756, "format": "JSON_TOLGEE", "patterns": ["./src/**/*.ts?(x)"], "push": {"files": [{"path": "./src/locales/language/en.json", "language": "en", "namespace": "language"}, {"path": "./src/locales/language/ja.json", "language": "ja", "namespace": "language"}, {"path": "./src/locales/home/<USER>", "language": "en", "namespace": "home"}, {"path": "./src/locales/home/<USER>", "language": "ja", "namespace": "home"}], "languages": ["en", "ja"], "forceMode": "OVERRIDE"}, "pull": {"namespaces": ["language", "home"], "path": "./src/locales"}}