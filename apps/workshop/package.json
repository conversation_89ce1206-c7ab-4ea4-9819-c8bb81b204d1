{"name": "workshop", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "storybook dev -p 6006", "build": "storybook build", "postbuild": "node postBuild.cjs", "lint": "echo \"Note: no lint specified\" && exit 0", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage"}, "dependencies": {"@mantine/core": "7.17.7", "@mantine/emotion": "7.17.7", "@resola-ai/models": "workspace:^", "@resola-ai/services-shared": "workspace:^", "@resola-ai/ui": "workspace:*", "@storybook/builder-vite": "^8.6.0-alpha.0", "@tabler/icons-react": "3.17.0", "dayjs": "^1.11.11", "react": "^18.2.0", "react-dom": "^18.2.0", "storybook-dark-mode": "^4.0.2"}, "devDependencies": {"@storybook/addon-actions": "^8.6.0-alpha.0", "@storybook/addon-essentials": "^8.6.0-alpha.0", "@storybook/addon-interactions": "^8.6.0-alpha.0", "@storybook/addon-links": "^8.6.0-alpha.0", "@storybook/addon-onboarding": "^8.6.0-alpha.0", "@storybook/blocks": "^8.6.0-alpha.0", "@storybook/manager-api": "^8.6.0-alpha.0", "@storybook/preview-api": "^8.6.0-alpha.0", "@storybook/react": "^8.6.0-alpha.0", "@storybook/react-vite": "^8.6.0-alpha.0", "@storybook/test": "^8.6.0-alpha.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "^2.1.9", "dotenv": "16.3.1", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-storybook": "^0.8.0", "jest-environment-jsdom": "^29.7.0", "storybook": "^8.6.0-alpha.0", "storybook-addon-deep-controls": "0.9.2", "storybook-addon-mantine": "^4.0.2", "typescript": "5.6.3", "vite": "5.4.19", "vitest": "^2.1.9"}}