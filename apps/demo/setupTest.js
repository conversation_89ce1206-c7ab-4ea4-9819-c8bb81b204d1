import { expect } from 'vitest';
import * as matchers from '@testing-library/jest-dom/matchers';
import '@testing-library/jest-dom';

const originalError = console.error;
const originalWarn = console.warn;

// Mock ResizeObserver
beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };
});

expect.extend(matchers);

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});