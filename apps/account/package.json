{"name": "account", "private": true, "version": "0.2.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "format": "biome format --write .", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "cleanup": "npx rimraf node_modules", "release": "standard-version", "release:major": "standard-version --release-as major", "release:minor": "standard-version --release-as minor", "release:patch": "standard-version --release-as patch", "coverage": "vitest --run --coverage"}, "dependencies": {"@auth0/auth0-react": "^2.2.1", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@resola-ai/models": "workspace:*", "@resola-ai/schema": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@sentry/react": "^7.81.0", "@tabler/icons-react": "3.17.0", "axios": "^1.8.2", "dayjs": "^1.11.12", "react": "18.2.0", "react-dom": "18.2.0", "react-imask": "^7.1.3", "react-router-dom": "6.21.3", "swr": "^2.2.2", "zod": "^3.24.1", "@tolgee/react": "^5.29.1", "@tolgee/web": "^5.29.2"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/lodash": "^4.14.199", "@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.2.1", "@vitest/coverage-v8": "2.1.9", "@vitest/ui": "2.1.9", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "jest-environment-jsdom": "^29.7.0", "typescript": "5.6.3", "vite": "5.4.19", "vite-tsconfig-paths": "^4.2.0", "vitest": "2.1.9"}}