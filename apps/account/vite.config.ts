import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import tsconfigPaths from 'vite-tsconfig-paths';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd(), '');
    const CDN_URL = env.VITE_CDN_PREFIX ?? '/';
    const BASE_PATH = env.VITE_BASE_PATH ?? '/';
    return {
        plugins: [tsconfigPaths(), react()],
        base: BASE_PATH,
        experimental: {
            renderBuiltUrl(
                filename: string,
                {
                    hostId,
                    type,
                }: {
                    hostId: string;
                    hostType: 'js' | 'css' | 'html';
                    type: 'public' | 'asset';
                },
            ) {
                if (type === 'public') {
                    return CDN_URL + filename;
                } else if (path.extname(hostId) === '.js') {
                    return CDN_URL + filename;
                } else {
                    return CDN_URL + filename;
                }
            },
        },
        server: {
            port: env.VITE_APP_PORT ?? 3003,
        },
        preview: {
            port: 3003,
        },
        test: {
            globals: true,
            environment: 'jsdom',
            setupFiles: './setupTest.js',
            coverage: {
                enabled: true,
                provider: 'v8',
                reporter: ['text', 'json', 'html', 'lcov'],
                reportsDirectory: './coverage',
                exclude: [
                    'node_modules/**',
                    'dist/**',
                    '**/*.d.ts',
                    'test/**',
                    '**/*.test.ts',
                    '**/*.test.tsx',
                    '**/*.spec.ts',
                    '**/*.spec.tsx',
                    'vite.config.ts'
                ]
            }
        }
    };
});
