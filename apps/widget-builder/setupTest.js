/** setupTest.js */
import { expect, vi, beforeAll } from 'vitest';
import * as matchers from '@testing-library/jest-dom/matchers';

const originalError = console.error;
const originalWarn = console.warn;

expect.extend(matchers);

class MockResizeObserver {
  observe = vi.fn();
  unobserve = vi.fn();
  disconnect = vi.fn();
}

class EventEmitter {
  addListener = vi.fn();
  removeListener = vi.fn();
  emit = vi.fn();
}

class EventSource {
  static CONNECTING = 1;
  static OPEN = 2;
  static CLOSED = 3;

  __emitter = new EventEmitter();
  onerror = vi.fn();
  onmessage = vi.fn();
  onopen = vi.fn();
  readyState = 0;
  url = '';
  withCredentials = false;

  constructor(
    url,
    configuration = {
      withCredentials: false,
    }
  ) {
    this.url = url;
    this.withCredentials = configuration.withCredentials;
    this.readyState = 0;
    this.__emitter = new EventEmitter();
  }

  addEventListener(eventName, listener) {
    this.__emitter.addListener(eventName, listener);
  }

  removeEventListener(eventName, listener) {
    this.__emitter.removeListener(eventName, listener);
  }

  close() {
    this.readyState = EventSource.CLOSED;
  }

  emit(eventName, messageEvent) {
    this.__emitter.emit(eventName, messageEvent);
  }

  emitError(error) {
    if (typeof this.onerror === 'function') {
      this.onerror(error);
    }
  }

  emitOpen() {
    this.readyState = EventSource.OPEN;
    if (typeof this.onopen === 'function') {
      this.onopen();
    }
  }

  emitMessage(message) {
    if (typeof this.onmessage === 'function') {
      this.onmessage(message);
    }
  }
}

window.ResizeObserver = MockResizeObserver;

Object.defineProperty(window, 'EventEmitter', {
  writable: true,
  value: EventEmitter,
});

Object.defineProperty(window, 'EventSource', {
  writable: true,
  value: EventSource,
});

Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

beforeAll(() => {
  console.warn = (...args) => {
    return;
  };
  console.error = (...args) => {
    return;
  };

  vi.mock('@tolgee/react', () => ({
    useTranslate: () => ({
      t: key => key,
    }),
  }));
});

Object.defineProperty(window, 'postMessage', {
  value: vi.fn(),
  writable: true,
});

Object.defineProperty(window, 'scroll', {
  value: vi.fn(),
  writable: true,
});

afterAll(() => {
  console.warn = originalWarn;
  console.error = originalError;
});
