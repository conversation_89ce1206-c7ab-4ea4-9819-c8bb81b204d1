{"name": "widget-builder", "private": true, "version": "0.5.0", "type": "module", "description": "DECA Widget Builder", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint-staged-check": "lint-staged", "lint:eslint:fix": "eslint . --fix", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "release": "standard-version -t widget-builder@", "release:minor": "standard-version -t widget-builder@ --release-as minor", "release:patch": "standard-version -t widget-builder@ --release-as patch", "release:major": "standard-version -t widget-builder@ --release-as major", "translation:pull": "env-cmd tolgee pull", "translation:push": "env-cmd tolgee push"}, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/serialize": "1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@monaco-editor/react": "^4.6.0", "@resola-ai/models": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tolgee/react": "^5.29.1", "@tolgee/web": "^5.29.2", "axios": "^1.8.2", "dayjs": "^1.11.12", "dompurify": "^3.2.5", "dotenv": "16.4.7", "esbuild-wasm": "0.25.0", "lodash": "^4.17.21", "mantine-react-table": "2.0.0-beta.9", "nanoid": "^5.0.9", "prettier": "^3.2.1", "react": "^18.2.0", "react-arborist": "^3.4.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-router-dom": "6.21.3", "standard-version": "^9.5.0", "swr": "^2.3.0", "vite-tsconfig-paths": "^4.2.0", "zod": "^3.24.1"}, "devDependencies": {"@eslint/js": "^8.56.0", "@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/jest-dom": "6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "14.6.1", "@tolgee/cli": "^2.4.1", "@types/lodash": "^4.17.13", "@types/react": "18.3.18", "@types/react-dom": "18.3.5", "@types/wicg-file-system-access": "2023.10.5", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "env-cmd": "10.1.0", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "globals": "^15.9.0", "husky": "^8.0.3", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "typescript": "5.6.3", "typescript-eslint": "^8.1.0", "vite": "5.4.19", "vite-plugin-circular-dependency": "^0.4.1", "vitest": "2.1.9"}, "lint-staged": {"*.{js,ts,tsx, jsx}": ["prettier --write", "eslint --fix"]}}