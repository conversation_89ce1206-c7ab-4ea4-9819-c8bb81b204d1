{"name": "chatbox", "private": true, "version": "0.13.0", "scripts": {"dev": "vite", "dev:client": "node ../../scripts/run-chatbox-client.js", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives", "prepare:web:pr": "node ../../packages/scripts/src/preparePreviewEnv.js", "preview": "vite preview", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint-staged-check": "lint-staged", "lint:eslint:fix": "eslint . --fix", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "prebuild:client": "node ../../packages/scripts/src/updatePackageJson.js './package.json' 'remove-type'", "build:client": "pnpm run prebuild:client && vite build --config client.vite.config.ts && pnpm run postbuild:client", "postbuild:client": "node ../../packages/scripts/src/updatePackageJson.js './package.json' 'add-type-module'", "release": "standard-version -t chatbox@", "release:minor": "standard-version -t chatbox@ --release-as minor", "release:patch": "standard-version -t chatbox@ --release-as patch", "release:major": "standard-version -t chatbox@ --release-as major"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/styled": "^11.11.0", "@emotion/utils": "1.4.2", "@hello-pangea/dnd": "^16.5.0", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@resola-ai/blocknote-editor": "workspace:*", "@resola-ai/models": "workspace:*", "@resola-ai/scripts": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/ui": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "axios": "^1.8.2", "centrifuge": "5.0.0", "color-scheme": "^1.0.1", "dayjs": "^1.11.12", "dotenv": "16.3.1", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "lodash": "^4.17.21", "nanoid": "^5.0.9", "prettier": "^3.2.1", "react": "^18.2.0", "react-color": "^2.19.3", "react-device-detect": "^2.2.3", "react-dom": "^18.2.0", "react-gsap-hook": "^0.0.3", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "^3.1.3", "react-i18next": "14.0.1", "react-router-dom": "6.21.3", "standard-version": "^9.5.0", "swr": "^2.2.4", "ua-parser-js": "^1.0.37", "uuid": "^9.0.1", "vite-tsconfig-paths": "^4.2.0", "wcag-color": "^1.1.1"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@resola-ai/widget-engine": "workspace:*", "@sentry/react": "^7.81.0", "@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "2.1.9", "husky": "^8.0.3", "jsdom": "^23.2.0", "lint-staged": "^15.5.0", "postcss-preset-mantine": "^1.12.3", "ts-loader": "^9.5.1", "typescript": "5.6.3", "vite": "5.4.19", "vitest": "2.1.9", "jest-environment-jsdom": "^29.7.0"}, "lint-staged": {"*.{js,ts,tsx, jsx}": ["prettier --write", "eslint --fix"]}, "type": "module"}