import { describe, it, expect, beforeEach, afterEach, vi, beforeAll, afterAll } from 'vitest';

// Test constants
const FORWARD_REF_WARNING = 'Warning: Function components cannot be given refs';
const BLOCKNOTE_STACK_TRACE = `
  at Component (@blocknote/react:123:45)
  at ReactNodeView (blocknote-editor/component:67:89)
`;
const REACT_SHADOW_STACK_TRACE = `
  at Component (react-shadow/index:123:45)
  at ShadowDOM (app/component:67:89)
`;
const REGULAR_STACK_TRACE = `
  at Component (regular-library:123:45)
  at UserComponent (app/component:67:89)
`;
const REGULAR_ERROR_MESSAGE = 'This is a regular error message';
const OTHER_WARNING_MESSAGE = 'Warning: This is another type of warning';

describe('suppressWarnings', () => {
  let originalConsoleError: typeof console.error;
  let originalError: typeof Error;
  let consoleErrorCalls: any[][];

  beforeAll(() => {
    // Store originals before any test runs
    originalConsoleError = console.error;
    originalError = global.Error;
  });

  afterAll(() => {
    // Restore everything at the very end
    console.error = originalConsoleError;
    global.Error = originalError;
  });

  // Helper function to setup mock console and import module
  const setupTestEnvironment = async () => {
    // Create mock that captures calls
    const mockConsoleError = vi.fn((...args: any[]) => {
      consoleErrorCalls.push(args);
    });

    // Set up console.error mock before importing
    console.error = mockConsoleError;

    // Import module (this will capture our mock as originalConsoleError)
    const suppressWarnings = await import('./suppressWarnings');

    return { suppressWarnings, mockConsoleError };
  };

  beforeEach(() => {
    // Track console.error calls
    consoleErrorCalls = [];

    // Mock Error constructor
    const mockError = vi.fn().mockImplementation((message?: string) => ({
      stack: REGULAR_STACK_TRACE,
      message: message || '',
      name: 'Error',
    }));
    mockError.captureStackTrace = vi.fn();
    mockError.stackTraceLimit = 10;
    mockError.prepareStackTrace = vi.fn();
    global.Error = mockError as any;

    // Clear all mocks
    vi.resetAllMocks();
    vi.resetModules();
  });

  afterEach(() => {
    // Reset Error mock
    (global.Error as any).mockClear?.();
    consoleErrorCalls = [];
  });

  describe('initializeWarningSuppressionSystem', () => {
    it('should initialize the warning suppression system', async () => {
      const currentConsoleError = console.error;

      // Dynamically import to trigger initialization
      const { initializeWarningSuppressionSystem } = await import('./suppressWarnings');
      initializeWarningSuppressionSystem();

      expect(console.error).not.toBe(currentConsoleError);
    });

    it('should not reinitialize if already initialized', async () => {
      const { initializeWarningSuppressionSystem } = await import('./suppressWarnings');

      initializeWarningSuppressionSystem();
      const firstPatchedError = console.error;

      initializeWarningSuppressionSystem();
      const secondPatchedError = console.error;

      expect(firstPatchedError).toBe(secondPatchedError);
    });
  });

  describe('enableBlockNoteWarningSuppression', () => {
    it('should suppress forwardRef warnings when suppression is active', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      console.error(FORWARD_REF_WARNING);

      expect(consoleErrorCalls).toHaveLength(0);
    });

    it('should allow non-forwardRef warnings through when suppression is active', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      console.error(REGULAR_ERROR_MESSAGE);

      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([REGULAR_ERROR_MESSAGE]);
    });

    it('should allow other warning types through when suppression is active', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      console.error(OTHER_WARNING_MESSAGE);

      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([OTHER_WARNING_MESSAGE]);
    });
  });

  describe('disableBlockNoteWarningSuppression', () => {
    it('should allow forwardRef warnings through when suppression is disabled', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      suppressWarnings.disableBlockNoteWarningSuppression();
      console.error(FORWARD_REF_WARNING);

      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
    });

    it('should still allow regular errors through when suppression is disabled', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      suppressWarnings.disableBlockNoteWarningSuppression();
      console.error(REGULAR_ERROR_MESSAGE);

      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([REGULAR_ERROR_MESSAGE]);
    });
  });

  describe('restoreConsoleError', () => {
    it('should restore original console.error function', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      // console.error is already patched by setupTestEnvironment
      const patchedConsoleError = console.error;

      suppressWarnings.restoreConsoleError();

      // After restore, console.error should be different (restored to what the module captured)
      expect(console.error).not.toBe(patchedConsoleError);
    });

    it('should disable suppression when restoring', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();
      suppressWarnings.restoreConsoleError();

      // After restoring, initialize again to test suppression is disabled
      suppressWarnings.initializeWarningSuppressionSystem();
      console.error(FORWARD_REF_WARNING);

      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
    });
  });

  describe('warning suppression logic', () => {
    describe('stack trace detection', () => {
      it('should suppress forwardRef warnings with @blocknote in stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({ stack: BLOCKNOTE_STACK_TRACE }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(0);
      });

      it('should suppress forwardRef warnings with react-shadow in stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({ stack: REACT_SHADOW_STACK_TRACE }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(0);
      });

      it('should suppress forwardRef warnings with blocknote-editor in stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({
          stack: 'Error at blocknote-editor/component:123:45',
        }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(0);
      });

      it('should allow forwardRef warnings with regular stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({ stack: REGULAR_STACK_TRACE }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
      });
    });

    describe('message content detection', () => {
      it('should suppress warnings containing ReactNodeView', async () => {
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(`${FORWARD_REF_WARNING} at ReactNodeView component`);

        expect(consoleErrorCalls).toHaveLength(0);
      });

      it('should allow forwardRef warnings without specific keywords', async () => {
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(`${FORWARD_REF_WARNING} at SomeOtherComponent`);

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([`${FORWARD_REF_WARNING} at SomeOtherComponent`]);
      });
    });

    describe('edge cases', () => {
      it('should handle non-string error messages', async () => {
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        const errorObject = { message: 'Test error', name: 'Error' };
        console.error(errorObject);

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([errorObject]);
      });

      it('should handle multiple arguments in console.error', async () => {
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(REGULAR_ERROR_MESSAGE, 'additional', 'arguments');

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([REGULAR_ERROR_MESSAGE, 'additional', 'arguments']);
      });

      it('should handle missing stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({ stack: undefined }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
      });

      it('should handle empty stack trace', async () => {
        (global.Error as any).mockImplementation(() => ({ stack: '' }));
        const { suppressWarnings } = await setupTestEnvironment();

        suppressWarnings.initializeWarningSuppressionSystem();
        console.error(FORWARD_REF_WARNING);

        expect(consoleErrorCalls).toHaveLength(1);
        expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
      });
    });
  });

  describe('integration scenarios', () => {
    it('should work correctly with enable/disable cycles', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.initializeWarningSuppressionSystem();

      // Initially disabled - should allow warnings
      console.error(FORWARD_REF_WARNING);
      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);

      consoleErrorCalls.length = 0; // Clear array

      // Enable suppression
      suppressWarnings.enableBlockNoteWarningSuppression();
      console.error(FORWARD_REF_WARNING);
      expect(consoleErrorCalls).toHaveLength(0);

      // Disable suppression
      suppressWarnings.disableBlockNoteWarningSuppression();
      console.error(FORWARD_REF_WARNING);
      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
    });

    it('should maintain suppression state across multiple calls', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.enableBlockNoteWarningSuppression();

      // Multiple suppressed calls
      console.error(FORWARD_REF_WARNING);
      console.error(FORWARD_REF_WARNING);
      console.error(FORWARD_REF_WARNING);

      expect(consoleErrorCalls).toHaveLength(0);

      // Regular errors should still go through
      console.error(REGULAR_ERROR_MESSAGE);
      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([REGULAR_ERROR_MESSAGE]);
    });

    it('should handle rapid enable/disable operations', async () => {
      const { suppressWarnings } = await setupTestEnvironment();

      suppressWarnings.initializeWarningSuppressionSystem();

      // Rapid enable/disable cycles
      suppressWarnings.enableBlockNoteWarningSuppression();
      suppressWarnings.disableBlockNoteWarningSuppression();
      suppressWarnings.enableBlockNoteWarningSuppression();
      suppressWarnings.disableBlockNoteWarningSuppression();

      // Should be disabled at the end
      console.error(FORWARD_REF_WARNING);
      expect(consoleErrorCalls).toHaveLength(1);
      expect(consoleErrorCalls[0]).toEqual([FORWARD_REF_WARNING]);
    });
  });
});
