/**
 * Utility to suppress specific React warnings that come from third-party libraries
 * This is specifically for BlockNote's internal components that have forwardRef issues
 */

let isSuppressionActive = false;
let isInitialized = false;
const originalConsoleError = console.error;

const patchedConsoleError = (...args: any[]) => {
  const message = typeof args[0] === 'string' ? args[0] : '';

  // Check if this is a forwardRef warning
  if (message.includes('Warning: Function components cannot be given refs')) {
    // Check if the warning is coming from BlockNote or our shadow DOM context
    const stackTrace = new Error().stack || '';

    if (
      stackTrace.includes('@blocknote') ||
      stackTrace.includes('react-shadow') ||
      stackTrace.includes('blocknote-editor') ||
      message.includes('ReactNodeView') ||
      isSuppressionActive
    ) {
      // Suppress this warning
      return;
    }
  }

  // Allow all other console.error messages through
  originalConsoleError.apply(console, args);
};

export const initializeWarningSuppressionSystem = () => {
  if (!isInitialized) {
    console.error = patchedConsoleError;
    isInitialized = true;
  }
};

export const enableBlockNoteWarningSuppression = () => {
  initializeWarningSuppressionSystem();
  isSuppressionActive = true;
};

export const disableBlockNoteWarningSuppression = () => {
  isSuppressionActive = false;
};

export const restoreConsoleError = () => {
  console.error = originalConsoleError;
  isSuppressionActive = false;
  isInitialized = false;
};

// Auto-initialize when the module loads
initializeWarningSuppressionSystem();
