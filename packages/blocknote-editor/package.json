{"name": "@resola-ai/blocknote-editor", "version": "1.0.19", "type": "module", "main": "./index.tsx", "types": "./index.tsx", "exports": {".": {"import": "./index.tsx", "require": "./dist/blocknote-editor.cjs.js"}, "./styles.css": "./dist/styles.css"}, "publishConfig": {"main": "./dist/blocknote-editor.cjs.js", "module": "./dist/blocknote-editor.es.js", "types": "./dist/index.d.ts", "access": "public", "exports": {".": {"import": "./dist/blocknote-editor.es.js", "require": "./dist/blocknote-editor.cjs.js", "types": "./dist/index.d.ts"}, "./styles.css": "./dist/styles.css"}}, "files": ["dist", "README.md", "CHANGELOG.md"], "sideEffects": ["**/*.css", "dist/styles.css"], "scripts": {"dev": "vite", "build": "tsc && pnpm run lint", "package:build": "rimraf dist && tsc -p tsconfig.node.json && vite build && tsc -p tsconfig.build.json", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest --run", "test:watch": "vitest", "coverage": "vitest --run --coverage", "version": "changeset version", "publish": "pnpm run package:build && changeset publish --no-git-checks --no-verify-access", "publish:interactive": "node scripts/publish.js", "prepublishOnly": "pnpm run package:build"}, "peerDependencies": {"@blocknote/core": "0.15.5", "@blocknote/react": "0.15.5", "@blocknote/shadcn": "0.15.5", "@emotion/react": "^11.14.0", "@mantine/core": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/hooks": "7.17.7", "react": "^18.2.0", "react-dom": "^18.2.0"}, "dependencies": {"@tabler/icons-react": "^3.31.0", "@testing-library/react": "^13.0.0", "@tiptap/extension-link": "2.10.4", "lodash": "^4.17.21", "react-phone-input-2": "^2.15.0", "react-shadow": "^20.6.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.27.1", "@testing-library/jest-dom": "^6.6.3", "@types/node": "^20.17.24", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^2.1.9", "rimraf": "^5.0.5", "terser": "^5.39.0", "typescript": "^5.6.3", "vite": "^5.4.18", "vite-plugin-dts": "4.5.0", "vitest": "2.1.9"}}