import { useCallback, useEffect, memo, useRef, forwardRef } from 'react';
import { useCreateBlockNote } from '@blocknote/react';
import { BlockNoteView } from '@blocknote/shadcn';
import { Box } from '@mantine/core';
import root from 'react-shadow';

import BlockNoteSchema from './BlockNoteSchema';
import BlockNoteExtensions from './BlockNoteExtensions';
import BlockNoteThemeProvider from './BlockNoteThemeProvider';
import { useBlockNoteStyles } from '../hooks/useBlockNoteStyles';
import { shadcnStyleCSS, interStyleCSS, inlineEditorViewerCSS } from './BlockNote.styles';
import useEditorMediaViewer from '../hooks/useEditorMediaViewer';
import { themeConfigurations } from '../constants';
import { useEditorCopyClipboard } from '../hooks/useEditorCopyClipboard';
import { convertBreakLineToHTML, replaceWhiteSpaceToHTMLNbsp } from '../utils/string';
import EditorMediaViewer from './EditorMediaViewer';
import {
  enableBlockNoteWarningSuppression,
  disableBlockNoteWarningSuppression,
} from '../utils/suppressWarnings';

import '@blocknote/core/fonts/inter.css';

// Fallback color in case themeConfigurations is not available
const FALLBACK_ACTION_COLOR = '#1F84F4'; // decaBlue[5]

export type BlockNoteViewerProps = {
  initialHTML?: string;
  className?: string;
  isBordered?: boolean;
  isMarkdown?: boolean;
  isUsingInlineCSS?: boolean;
  actionColor?: string;
  useActionColorAsHyperlink?: boolean;
  id?: string;
  shadowRoot?: ShadowRoot | null;
  enableMediaViewer?: boolean;
  textColor?: string;
};

/**
 * BlockNoteViewer - A read-only viewer component for BlockNote content
 *
 * @important CSS Import Required: You must import the CSS file in your main application file:
 * ```tsx
 * import '@resola-ai/blocknote-editor/styles.css';
 * ```
 *
 * @param props - BlockNoteViewerProps containing viewer configuration
 * @returns React component for the BlockNote viewer
 */
const BlockNoteViewer = forwardRef<HTMLDivElement, BlockNoteViewerProps>((props, ref) => {
  const {
    className,
    initialHTML = '',
    isBordered = false,
    isMarkdown = false,
    isUsingInlineCSS = true,
    actionColor = '',
    useActionColorAsHyperlink = false,
    id = '',
    shadowRoot = null,
    enableMediaViewer = true,
    textColor,
  } = props;

  // Enable warning suppression for BlockNote
  useEffect(() => {
    enableBlockNoteWarningSuppression();
    return () => {
      disableBlockNoteWarningSuppression();
    };
  }, []);

  const editor = useCreateBlockNote({
    schema: BlockNoteSchema,
    ...BlockNoteExtensions,
  });

  const { classes, cx } = useBlockNoteStyles({ isBordered });

  // Add ref to track the shadow root element itself (not just its shadow root)
  const rootElementRef = useRef<HTMLElement | null>(null);
  // Add ref to track the shadow root
  const shadowRootRef = useRef<ShadowRoot | null>(null);

  // Get reference to the shadow root when available
  const handleShadowRootRef = useCallback((node: HTMLElement | null) => {
    if (node) {
      rootElementRef.current = node;
      shadowRootRef.current = node.shadowRoot;
    }
  }, []);

  // Initialize media viewer hook
  const { isOpen, closeViewer, currentSrc, currentAlt } = useEditorMediaViewer({
    id,
    editorSelector: '[data-blocknote-viewer] .bn-editor',
    enabled: enableMediaViewer,
    shadowRoot: shadowRootRef.current ?? shadowRoot,
    rootElement: rootElementRef.current,
  });

  /**
   * Load initial HTML content to the editor
   * @param htmlContent HTML content to load
   * @returns void
   */
  const loadInitialHTML = useCallback(
    async (htmlContent: string) => {
      const blocks = isMarkdown
        ? await editor.tryParseMarkdownToBlocks(htmlContent)
        : await editor.tryParseHTMLToBlocks(
            replaceWhiteSpaceToHTMLNbsp(convertBreakLineToHTML(htmlContent))
          );
      editor.replaceBlocks(editor.document, blocks);
    },
    [editor, isMarkdown]
  );

  /**
   * Load initial HTML content on mount
   * @returns void
   */
  useEffect(() => {
    if (initialHTML) {
      loadInitialHTML(initialHTML);
    }
  }, [initialHTML, loadInitialHTML]);

  // Ensure we update the shadow root ref if it becomes available after initial render
  useEffect(() => {
    if (rootElementRef.current && !shadowRootRef.current) {
      shadowRootRef.current = rootElementRef.current.shadowRoot;
    }
  }, []);

  // Bind copy event for BlockNoteViewer
  useEditorCopyClipboard();

  return (
    <div>
      <root.div
        id={`block-note-shadow-root${id}`}
        ref={handleShadowRootRef}
        suppressHydrationWarning>
        {isUsingInlineCSS && (
          <style
            dangerouslySetInnerHTML={{
              __html: `
                ${interStyleCSS}
                ${shadcnStyleCSS}
                ${inlineEditorViewerCSS({
                  actionColorLink: useActionColorAsHyperlink
                    ? actionColor
                    : (themeConfigurations?.colors?.decaBlue?.[6] ?? FALLBACK_ACTION_COLOR),
                  textColor,
                })}
              `,
            }}
          />
        )}
        <BlockNoteThemeProvider shadowRootId={`block-note-shadow-root${id}`}>
          <Box className={classes.blockNoteViewer} data-blocknote-viewer ref={ref}>
            <BlockNoteView
              className={cx(classes.editorContainer, className)}
              editor={editor}
              theme={'light'}
              editable={false}
            />
          </Box>
        </BlockNoteThemeProvider>
      </root.div>

      {/* Media Viewer Modal */}
      {currentSrc && (
        <BlockNoteThemeProvider>
          <EditorMediaViewer
            src={currentSrc}
            alt={currentAlt ?? ''}
            opened={isOpen}
            onClose={closeViewer}
          />
        </BlockNoteThemeProvider>
      )}
    </div>
  );
});

// Add displayName for better debugging
BlockNoteViewer.displayName = 'BlockNoteViewer';

export default memo(BlockNoteViewer);
