import { useCallback, useEffect, useRef } from 'react';
import { useCreateBlockNote } from '@blocknote/react';
import { BaseEditorProps } from '../types/blocknote';
import BlockNoteLocales from '../components/BlockNoteLocales';
import BlockNoteExtensions from '../components/BlockNoteExtensions';
import { validateUploadFile } from '../utils';

// Define the expected TipTap editor interface
export interface TipTapEditor {
  isFocused: boolean;
  on: (event: string, handler: any) => void;
  off: (event: string, handler: any) => void;
  commands: {
    insertContent: (content: any) => void;
    focus: () => void;
  };
}

/**
 * Props for the useBlockNoteEditor hook
 */
export type UseBlockNoteEditorProps = Pick<
  BaseEditorProps,
  'language' | 'autoFocus' | 'uploadFile' | 'onBlur' | 'onFocus'
> & {
  schema?: any; // Schema can be any BlockNote schema
};

/**
 * Custom hook to handle common BlockNote editor functionality
 */
export const useBlockNoteEditor = ({
  schema,
  language = 'en',
  autoFocus = false,
  uploadFile,
  onBlur,
  onFocus,
}: UseBlockNoteEditorProps) => {
  // Reference to the editor container element
  const editorRef = useRef<HTMLDivElement>(null);

  // Reference to store requestAnimationFrame ID for cleanup
  const rafRef = useRef<number>();

  // Create the BlockNote editor instance
  const editor = useCreateBlockNote({
    schema,
    dictionary: BlockNoteLocales[language],
    ...BlockNoteExtensions,
    ...(uploadFile
      ? {
          uploadFile: async (file: File) => {
            if (!uploadFile || !validateUploadFile(file)) return '';
            return await uploadFile(file);
          },
        }
      : {}),
  });

  /**
   * Handles focus events on the editor
   * Ensures proper editor focus state
   */
  const handleAutoFocus = useCallback(() => {
    if (!(editor._tiptapEditor as unknown as TipTapEditor)?.isFocused && editorRef.current) {
      // Use requestAnimationFrame for better performance when focusing
      if (typeof window !== 'undefined') {
        rafRef.current = requestAnimationFrame(() => {
          editor.focus();
          rafRef.current = undefined;
        });
      } else {
        editor.focus();
      }
    }
  }, [editor]);

  /**
   * Simplified blur handler that only manages the onBlur callback
   */
  const bindOnBlur = useCallback(
    context => {
      if (!onBlur || !editorRef.current) return;
      const { event } = context;

      // Only trigger onBlur when focus moves outside the editor
      const isTargetWithinEditor =
        event.relatedTarget && editorRef.current?.contains(event.relatedTarget);

      if (!isTargetWithinEditor) {
        onBlur();
      } else {
        handleAutoFocus();
      }
    },
    [onBlur, handleAutoFocus]
  );

  /**
   * Focus handler that calls the onFocus callback when the editor receives focus
   */
  const bindOnFocus = useCallback(() => {
    if (!onFocus) return;
    onFocus();
  }, [onFocus]);

  // Set up listeners for blur and focus events
  useEffect(() => {
    const tipTapEditor = editor._tiptapEditor as unknown as TipTapEditor;

    // Only bind events if the callbacks are provided
    if (onBlur) {
      tipTapEditor.on('blur', bindOnBlur);
    }

    if (onFocus) {
      tipTapEditor.on('focus', bindOnFocus);
    }

    // Auto-focus the editor if required
    if (autoFocus) {
      handleAutoFocus();
    }

    // Cleanup function to remove event listeners
    return () => {
      if (onBlur) {
        tipTapEditor.off('blur', bindOnBlur);
      }

      if (onFocus) {
        tipTapEditor.off('focus', bindOnFocus);
      }

      // Cancel any pending focus operations
      if (rafRef.current) {
        cancelAnimationFrame(rafRef.current);
      }
    };
  }, [editor, autoFocus, onBlur, onFocus, bindOnBlur, bindOnFocus, handleAutoFocus]);

  return {
    editor,
    editorRef,
    handleAutoFocus,
  };
};
