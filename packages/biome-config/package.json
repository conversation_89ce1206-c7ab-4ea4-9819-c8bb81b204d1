{"name": "@resola-ai/biome-config", "version": "1.0.0", "license": "MIT", "type": "module", "exports": {"./biome": "./biome.json"}, "devDependencies": {"@biomejs/biome": "^1.5.3", "standard-version": "^9.5.0", "@vitest/coverage-v8": "^2.1.9", "@testing-library/jest-dom": "^6.2.0", "jest-environment-jsdom": "^29.7.0"}, "publishConfig": {"access": "public"}, "scripts": {"release": "standard-version -t @resola-ai/biome-config@", "release:minor": "standard-version -t @resola-ai/biome-config@ --release-as minor", "release:patch": "standard-version -t @resola-ai/biome-config@ --release-as patch", "release:major": "standard-version -t @resola-ai/biome-config@ --release-as major", "coverage": "echo 'Do not need test coverage'"}}