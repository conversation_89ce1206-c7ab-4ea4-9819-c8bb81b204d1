import {
  Field,
  RuleType,
  defaultOperators,
  defaultRuleProcessorMongoDB,
  RuleGroupType,
} from 'react-querybuilder';
import { FieldType } from '../components';
import { SUPPORTED_LANGUAGE } from '../constants';
import en from '../locales/en/query-builder.json';
import ja from '../locales/ja/query-builder.json';
import { TFunction } from 'i18next';
import { isEmpty } from 'lodash';
import dayjs from 'dayjs';
type SupportedLanguage = (typeof SUPPORTED_LANGUAGE)[keyof typeof SUPPORTED_LANGUAGE];

const validator = (r: RuleType) => !!r.value;
const dateTimeOperators = ['=', '!=', '>', '<', 'between', 'notBetween', 'relativeDate'];
const dateTimeOperatorLabels = {
  '=': 'isEqualTo',
  '!=': 'isNotEqualTo',
  '>': 'isAfter',
  '<': 'isBefore',
  between: 'between',
  notBetween: 'notBetween',
  relativeDate: 'relativeDate',
} as const;
const operatorsByLanguage = {
  en: defaultOperators.map(op => ({ ...op, label: en.operators[op.name] ?? op.label })),
  ja: defaultOperators.map(op => ({ ...op, label: ja.operators[op.name] ?? op.label })),
} as const;

type DateTimeOperatorLabel = (typeof dateTimeOperatorLabels)[keyof typeof dateTimeOperatorLabels];

export const CUSTOM_FILTER_OPERATORS = {
  relativeDate: 'relativeDate',
} as const;

export const getQueryBuilderFieldOptions = (
  lang: SupportedLanguage,
  t: TFunction<'table'>
): Partial<Record<FieldType, Partial<Field>>> => {
  const operators = operatorsByLanguage[lang];
  const ns = 'query-builder';
  return {
    autoId: {
      inputType: 'number',
      validator,
      operators,
    },
    checkbox: {
      valueEditorType: 'checkbox',
      operators: operators.filter(op => op.name === '='),
    },
    currency: {
      inputType: 'number',
      validator,
      operators,
    },
    datetime: {
      inputType: 'datetime-local',
      operators: operators
        .filter(op => dateTimeOperators.includes(op.name))
        .map(op => ({
          ...op,
          label: t(`operators.${dateTimeOperatorLabels[op.name] as DateTimeOperatorLabel}`, { ns }),
        })),
    },
    createdAt: {
      inputType: 'datetime-local',
      operators: operators
        .filter(op => dateTimeOperators.includes(op.name))
        .map(op => ({
          ...op,
          label: t(`operators.${dateTimeOperatorLabels[op.name] as DateTimeOperatorLabel}`, { ns }),
        })),
    },
    updatedAt: {
      inputType: 'datetime-local',
      operators: operators
        .filter(op => dateTimeOperators.includes(op.name))
        .map(op => ({
          ...op,
          label: t(`operators.${dateTimeOperatorLabels[op.name] as DateTimeOperatorLabel}`, { ns }),
        })),
    },
    email: {
      validator,
      operators,
    },
    longtext: {
      validator,
      operators,
    },
    multiSelect: {
      valueEditorType: 'multiselect',
      inputType: 'multiSelect',
      operators: operators.filter(op => op.name === 'in'),
    },
    number: {
      inputType: 'number',
      validator,
      operators,
    },
    percentage: {
      inputType: 'number',
      validator,
      operators,
    },
    phone: {
      validator,
      operators,
    },
    text: {
      validator,
      operators,
    },
    select: {
      valueEditorType: 'select',
      inputType: 'select',
      operators: operators.filter(op => op.name === '='),
    },
    time: {
      inputType: 'time',
      operators,
    },
    url: {
      validator,
      operators,
    },
    tag: {
      inputType: 'tag',
      operators: operators.filter(op => op.name === 'in'),
    },
  };
};

export const formatQueryPreservingGroups = (ruleOrGroup: RuleGroupType | RuleType): any => {
  // Handle Groups
  if ('combinator' in ruleOrGroup) {
    const group = ruleOrGroup as RuleGroupType;
    const operator = group.combinator === 'and' ? '$and' : '$or';

    // Recursively format children, filtering out null/empty results
    const children = group.rules
      .map(child => formatQueryPreservingGroups(child))
      .filter(childResult => childResult !== null && !isEmpty(childResult));

    // Return group structure if children exist, otherwise null (or handle as needed)
    if (children.length === 0) {
      return null;
    }
    return { [operator]: children };
  }
  // Handle Rules
  else {
    const rule = ruleOrGroup as RuleType;
    // Handle custom relativeDate operator
    if (rule.operator === CUSTOM_FILTER_OPERATORS.relativeDate) {
      // Replicate logic from customRuleProcessor for this specific operator
      return {
        [rule.field]: {
          // @ts-ignore assuming rule.customValue is correct shape
          $relativeDate: { ...rule.customValue, timezone: dayjs().format('Z') },
        },
      };
    }
    // Defer to default processor for standard operators
    else {
      try {
        // defaultRuleProcessorMongoDB returns a stringified JSON, so parse it
        const processedRuleString = defaultRuleProcessorMongoDB(rule, {});
        // Handle cases where the processor might return an empty string or invalid JSON for certain rule types/values
        if (!processedRuleString || processedRuleString === '{}') {
          return null;
        }
        const processedRule = JSON.parse(processedRuleString);
        return isEmpty(processedRule) ? null : processedRule;
      } catch (e) {
        console.error('Error processing rule:', rule, e);
        return null; // Return null or handle error appropriately
      }
    }
  }
};
