{"name": "@resola-ai/ui", "version": "1.0.3", "type": "module", "main": "./index.tsx", "types": "./index.tsx", "license": "MIT", "scripts": {"lint": "eslint .", "generate:component": "turbo gen react-component", "build": "echo \"Note: No build specified\" && exit 0", "test": "vitest --run", "test:watch": "vitest watch", "test:unit": "vitest --run", "coverage": "vitest --run --coverage", "release": "standard-version -t @resola-ai/ui@", "release:minor": "standard-version -t @resola-ai/ui@ --release-as minor", "release:patch": "standard-version -t @resola-ai/ui@ --release-as patch", "release:major": "standard-version -t @resola-ai/ui@ --release-as major"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@testing-library/dom": "^10.1.0", "@testing-library/jest-dom": "^6.4.5", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.5.2", "@turbo/gen": "^1.11.3", "@types/lodash": "^4.17.5", "@types/node": "^20.11.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@vitest/coverage-v8": "^2.1.9", "framer-motion": "^10.18.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.1", "standard-version": "^9.5.0", "typescript": "5.6.3", "vitest": "2.1.9"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/serialize": "^1.3.3", "@emotion/utils": "1.4.2", "@hookform/resolvers": "^3.3.1", "@mantine/core": "7.17.7", "@mantine/dates": "7.17.7", "@mantine/dropzone": "7.17.7", "@mantine/emotion": "7.17.7", "@mantine/form": "7.17.7", "@mantine/hooks": "7.17.7", "@mantine/modals": "7.17.7", "@mantine/notifications": "7.17.7", "@mantine/tiptap": "7.17.7", "@react-querybuilder/dnd": "8.5.0", "@react-querybuilder/mantine": "8.5.0", "@resola-ai/blocknote-editor": "workspace:*", "@resola-ai/services-shared": "workspace:*", "@resola-ai/shared-components": "workspace:*", "@resola-ai/shared-constants": "workspace:*", "@resola-ai/utils": "workspace:*", "@tabler/icons-react": "3.17.0", "@tiptap/core": "2.10.4", "@tiptap/extension-dropcursor": "2.10.4", "@tiptap/extension-heading": "2.10.4", "@tiptap/extension-highlight": "2.10.4", "@tiptap/extension-image": "2.10.4", "@tiptap/extension-link": "2.10.4", "@tiptap/extension-placeholder": "2.10.4", "@tiptap/extension-typography": "2.10.4", "@tiptap/extension-underline": "2.10.4", "@tiptap/pm": "2.10.4", "@tiptap/react": "2.10.4", "@tiptap/starter-kit": "2.10.4", "@tolgee/react": "^6.2.4", "@types/json-schema": "^7.0.15", "@vitejs/plugin-react-swc": "3.8.0", "clsx": "2.1.1", "dayjs": "^1.11.12", "dompurify": "3.2.4", "gsap": "3.12.4", "history": "^5.3.0", "i18next": "23.10.0", "i18next-browser-languagedetector": "7.2.0", "lodash": "^4.17.21", "mantine-react-table": "2.0.0-beta.8", "@monaco-editor/react": "^4.6.0", "react": "^18.2.0", "react-contexify": "^6.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hook-form-mantine": "3.1.3", "react-i18next": "14.0.1", "react-markdown": "^9.0.1", "react-phone-input-2": "^2.15.1", "react-querybuilder": "8.5.0", "react-router-dom": "6.21.3", "react-shadow": "^20.5.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.0", "remark-html": "^16.0.1", "timezones-list": "^3.0.3", "uuid": "^9.0.1", "vite": "5.4.19", "vite-plugin-dts": "4.5.0", "zod": "^3.24.1"}}