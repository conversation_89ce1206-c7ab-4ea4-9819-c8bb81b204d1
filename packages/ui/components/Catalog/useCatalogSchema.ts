import { useState, useEffect } from 'react';
import { ICategory, NodeItem } from './types';

export const convertToNode = (node: NodeItem, category: string, disabledNodes?: string[]): NodeItem => ({
  name: node.name,
  displayName: node.displayName,
  icon: node.icon,
  category,
  description: node.description || '',
  enabled: node.enabled !== false,
  id: node.id,
  disabled: !!disabledNodes?.includes(node.name || ''),
});

export const useCatalogSchema = (schema: Record<string, any>, disabledNodes?: string[]) => {
  const [nodes, setNodes] = useState<NodeItem[]>([]);
  const [categories, setCategories] = useState<Record<string, ICategory>>({});
  const [searchFields, setSearchFields] = useState<string[]>(['name', 'description']);
  const [searchPlaceholder, setSearchPlaceholder] = useState('');

  useEffect(() => {
    if (!schema || Object.keys(schema).length === 0) return;

    const properties = schema.properties || {};
    const allNodes: NodeItem[] = [];
    const categoryProps = properties.categories?.properties || {};
    // Process each node type
    Object.entries(categoryProps).forEach(([key]: [string, any]) => {
      if (properties[key]?.default && Array.isArray(properties[key]?.default) && key !== 'all') {
        const typeNodes = properties[key]?.default
          .filter((node: any) => node.enabled !== false)
          .map((node: any) => convertToNode(node, key, disabledNodes));
        allNodes.push(...typeNodes);
      }
    });

    setNodes(allNodes);

    if (properties.search?.properties?.placeholder?.default) {
      setSearchPlaceholder(properties.search.properties.placeholder.default);
    }

    if (properties.search?.properties?.searchFields?.default) {
      setSearchFields(properties.search.properties.searchFields.default);
    }

    if (properties.categories?.properties) {
      const categoryProps = properties.categories.properties;
      const updatedCategories = Object.entries(categoryProps).reduce(
        (acc: Record<string, ICategory>, [key, prop]: [string, any]) => {
          acc[key] = {
            key,
            displayName: prop.displayName,
            defaultEnabled: prop.default !== false,
            icon: prop.icon,
            headingName: prop.headingName,
          };
          return acc;
        },
        {} as Record<string, ICategory>
      );
      setCategories(updatedCategories);
    }
  }, [schema, disabledNodes]);
  return {
    nodes,
    categories,
    searchFields,
    searchPlaceholder,
  };
};
