import { Box, Text } from '@mantine/core';
import { Control, UseFormWatch } from 'react-hook-form';
import { SchemaField } from '../../FormChema/type';
import { FORM_SCHEMA_CONFIGS } from '../../FormChema/configForm';
import { useTranslate } from '@tolgee/react';
interface DynamicSettingsFormProps {
  control: Control<any>;
  selectedCredential?: SchemaField;
  watch: UseFormWatch<any>;
}

const DyanmicField = ({
  field,
  control,
  prefix,
  watch,
}: {
  field: SchemaField;
  control: Control<any>;
  prefix: string;
  watch: UseFormWatch<any>;
}) => {
  const { t } = useTranslate('common');
  const fieldName = prefix ? `${prefix}.${field.name}` : field.name;
  const FormSchemaComponent = Object.values(FORM_SCHEMA_CONFIGS).find(config =>
    config.types.includes(field.type)
  );

  if (field.visibleIf) {
    const shouldShow = Object.entries(field.visibleIf).every(([key, values]) => {
      const value = watch(prefix ? `${prefix}.${key}` : key);
      return Array.isArray(values) && values.includes(value);
    });

    if (!shouldShow) return null;
  }

  const rules: any = {
    required: field.required ? t('error.required') : false,
    validate: (value: any) => {
      if (field.pattern && !new RegExp(field.pattern).test(value)) {
        return t('error.pattern', { pattern: field.pattern });
      }
      return true;
    },
  };

  if (!FormSchemaComponent) {
    return null;
  }

  return (
    <FormSchemaComponent.Component
      schema={field}
      control={control}
      name={fieldName}
      key={fieldName}
      rules={rules}
    />
  );
};

export const DynamicSettingsForm = ({
  selectedCredential,
  control,
  watch,
}: DynamicSettingsFormProps) => {
  if (!selectedCredential) return null;
  return (
    <Box>
      <Text fw={600} mt='md' data-testid='credential-type-text'>
        {selectedCredential.displayName}
      </Text>
      <Text>{selectedCredential.description}</Text>
      {Object.values(selectedCredential.properties).map((field) => (
        <DyanmicField
          key={(field as SchemaField).name}
          field={field as SchemaField} 
          control={control}
          prefix='settings'
          watch={watch}
        />
      ))}
    </Box>
  );
};
