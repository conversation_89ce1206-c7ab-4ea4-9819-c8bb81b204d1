import { Accordion, Box, Flex, rem, Text } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconArticle } from '@tabler/icons-react';
import { uniqueId } from 'lodash';
import { ArticleType, Category, CategoryArticle, CategoryTypes } from '../../types/pageBuilder';
import { useEffect, useState } from 'react';
import { isResponsiveProp, createResponsiveValue, generateResponsiveStyles, generateResponsivePadding } from '../../utils';

const useStyles = createStyles((theme) => ({
  categoryTreeContainer: {
    '.mantine-Accordion-content': {
      paddingRight: '0px !important',
    },
    '.mantine-Accordion-control:hover' : {
      backgroundColor: 'transparent !important'
    },
    'a.category-link': {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]} !important`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`
      }
    },
  },
  subCategoryItem: {
    borderBottom: '0px !important',
    'a.subcategory-link': {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]} !important`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`
      }
    }
  },
  articleItem: {
    color: `${theme.colors.decaGrey[6]} !important`,
    '&:hover': {
      color: `${theme.colors.decaBlue[6]} !important`
    },
    a: {
      cursor: 'pointer',
      color: `${theme.colors.decaGrey[6]}`,
      textDecoration: 'none !important',
      '&:hover': {
        color: `${theme.colors.decaBlue[6]} !important`
      }
    }
  }
}));

const ArticleItem = ({ label, url }: { label: string, url: string }) => {
  const { classes } = useStyles();
  return (
    <Flex my={rem(10)} align='center' gap={rem(10)} c={'decaGrey.8'} className={classes.articleItem}>
      <Box h={rem(20)} w={rem(20)}>
        <IconArticle size={20} color='currentColor' />
      </Box>
      <Text truncate><a href={url} title={label}>{label}</a></Text>
    </Flex>
  );
};

type CategoryTreeElementProps = {
  padding: Record<string, number>;
  width: Record<string, any>;
  categories: Category[] | CategoryArticle[];
  selectedCategory: string;
  selectedSubCategory: string;
  selectedArticle?: string;
  selectedElement?: string;
  articleDetailSlug?: string;
  categoryListSlug?: string;
  styles?: Record<string, any>;
  showRightDivider: boolean;
  dividerColor: string
};

export const CategoryTreeElement = ({
  padding,
  width,
  categories,
  selectedCategory,
  selectedSubCategory,
  selectedArticle,
  selectedElement,
  articleDetailSlug,
  categoryListSlug,
  styles,
  showRightDivider,
  dividerColor
}: CategoryTreeElementProps) => {
  const { classes } = useStyles();
  const [isShow, setIsShow] = useState(true);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string[]>([])
  const [selectedSubCategoryId, setSelectedSubCategoryId] = useState<string[]>([])

  const getUrl = (item: Category | CategoryArticle, type?: string) => {
    if (!categoryListSlug || !articleDetailSlug) {
      return '#';
    }
    if (type === CategoryTypes.Category) {
      return `${categoryListSlug}?element_id=${selectedElement}&faq_category_id=${item.id}`;
    } else if (type === CategoryTypes.SubCategory) {
      return `${categoryListSlug}?element_id=${selectedElement}&faq_category_id=${selectedCategory}&faq_sub_category_id=${item.id}`;
    } else {
      return `${articleDetailSlug}?faq_article_id=${(item as CategoryArticle).value}&faq_base_id=${(item as CategoryArticle).parentId}&element_id=${selectedElement}`;
    }
  };

  // Tempoary hide category tree on tablet and mobile
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setIsShow(window?.screen?.width >= 1200);
    }
  }, []);

  useEffect(() => {
    if (selectedArticle) {
      categories?.forEach(category => {
        if (!selectedCategoryId?.length) {
          if (category.subType === 'article') {
            if (
              category.data.find(
                article =>
                  article?.value?.toLowerCase() === selectedArticle?.toLowerCase()
              )
            ) {
              setSelectedCategoryId([category.id]);
            }
          } else {
            category.data.forEach(subCategory => {
              if (!selectedSubCategoryId?.length) {
                if (
                  subCategory.data.find(
                    article =>
                      article?.value?.toLowerCase() === selectedArticle?.toLowerCase()
                  )
                ) {
                  setSelectedCategoryId([category.id]);
                  setSelectedSubCategoryId([subCategory.id]);
                }
              }
            });
          }
        }
      });
    } else {
      setSelectedCategoryId(selectedCategory ? [selectedCategory] : []);
      setSelectedSubCategoryId(selectedSubCategory ? [selectedSubCategory] : []);
    }
  }, [categories, selectedArticle, selectedCategory, selectedSubCategory])

  if (!isShow) return null;

  return (
    <Flex
      align={'center'}
      h='100%'
      w='100%'
      styles={generateResponsiveStyles({
        width: isResponsiveProp(width)
          ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
          : rem(width),
        minWidth: isResponsiveProp(width)
          ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
          : rem(width),
        maxWidth: isResponsiveProp(width)
          ? createResponsiveValue(rem(width.mobile), rem(width.tablet), rem(width.desktop))
          : rem(width),
        padding: generateResponsivePadding(padding),
        borderRight: showRightDivider ? `1px solid ${dividerColor}` : 'none',
      })}
      className={classes.categoryTreeContainer}>
      <Accordion
        w='100%'
        multiple={true}
        value={selectedCategoryId}
        onChange={setSelectedCategoryId}
        styles={styles}>
        {categories?.map(category => (
          <Accordion.Item key={category.id} value={category.id || ''}>
            <Accordion.Control>
              <Text truncate>
                <a href={getUrl(category, CategoryTypes.Category)} className='category-link' title={category.name}>
                  {category.name}
                </a>
              </Text>
            </Accordion.Control>
            {!!category.data?.length && (
              <Accordion.Panel pr={0}>
                {category.subType === ArticleType.Category ? (
                  <Accordion
                    w='100%'
                    pr={0}
                    styles={styles}
                    multiple={true}
                    value={selectedSubCategoryId}
                    onChange={setSelectedSubCategoryId}>
                    {(category.data as Category[])?.map((subCategory: Category) => (
                      <Accordion.Item
                        key={subCategory.id}
                        value={subCategory.id || ''}
                        className={classes.subCategoryItem}>
                        <Accordion.Control>
                          <Text truncate>
                            <a
                              href={getUrl(subCategory, CategoryTypes.SubCategory)}
                              className='subcategory-link'
                              title={subCategory.name}>
                              {subCategory.name}
                            </a>
                          </Text>
                        </Accordion.Control>
                        <Accordion.Panel pr={0}>
                          <Box pl={rem(10)}>
                            {(subCategory.data as CategoryArticle[])?.map(article => (
                              <ArticleItem
                                key={uniqueId('article-item-')}
                                label={article.label || ''}
                                url={getUrl(article)}
                              />
                            ))}
                          </Box>
                        </Accordion.Panel>
                      </Accordion.Item>
                    ))}
                  </Accordion>
                ) : (
                  <Box pl={rem(10)}>
                    {(category.data as CategoryArticle[])?.map(article => (
                      <ArticleItem
                        key={uniqueId('article-item-')}
                        label={article.label || ''}
                        url={getUrl(article)}
                      />
                    ))}
                  </Box>
                )}
              </Accordion.Panel>
            )}
          </Accordion.Item>
        ))}
      </Accordion>
    </Flex>
  );
};
