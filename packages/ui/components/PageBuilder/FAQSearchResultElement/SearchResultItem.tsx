import { ActionIcon, rem, useMantineTheme } from '@mantine/core';
import { IconArrowRight } from '@tabler/icons-react';
import { Group, Text } from '@mantine/core';
import { IconArticle, IconCurrencyQuetzal } from '@tabler/icons-react';
import { ICON_CONFIG, ICON_SIZE_MAP } from '../../../constants/page-builder';
import { withTolgee } from '../../hoc/withTolgee';
import { createStyles } from '@mantine/emotion';
import { generateResponsiveStyles } from '../../../utils';

type IllustrationType = keyof typeof ICON_CONFIG;

const getIconSize = (type: IllustrationType, size: number) => {
  return ICON_CONFIG[type].sizes[ICON_SIZE_MAP[size as keyof typeof ICON_SIZE_MAP] || 'S'];
};

const useStyles = createStyles(theme => ({
  searchResultItem: {
    display: 'flex',
    width: '100%',
    alignItems: 'center',
    textDecoration: 'none',
    color: `${theme.colors.decaGrey[8]} !important`,
    '&:hover': {
      color: `${theme.colors.decaBlue[6]} !important`,
    },
  },
}));

const SearchResultItem = (props: Record<string, any>) => {
  const {
    result,
    iconSize,
    iconColor,
    iconBgColor,
    backgroundColor,
    borderColor,
    categoryType,
    illustrationType,
    articleDetailSlug,
    textColor,
    index,
    totalItems,
  } = props;
  const theme = useMantineTheme();
  const { classes } = useStyles();
  const actualIconSize = getIconSize(illustrationType as IllustrationType, iconSize);
  const isRealResult = !!result.faq_article_id;
  const searchUrl =  isRealResult ? location.href : '';
  const searchPageName = isRealResult ? document.title.split(' - ')?.[1] ?? '' : '';

  const isFirst = index === 0;
  const isLast = index === totalItems - 1;
  const isSingle = isFirst && isLast;

  return (
    <Group
      p={categoryType === 'box' ? rem(20) : `${rem(20)} 0`}
      w={'100%'}
      justify='space-between'
      styles={generateResponsiveStyles({
        backgroundColor: backgroundColor && categoryType === 'solid' ? backgroundColor : '#fff',
        borderBottom: isLast ? 'none' : `1px solid ${borderColor}`,
        borderRadius: isSingle
          ? theme.radius.lg
          : isFirst
            ? `${theme.radius.lg} ${theme.radius.lg} 0 0`
            : isLast
              ? `0 0 ${theme.radius.lg} ${theme.radius.lg}`
              : 'none',
      })}>
      <a
        className={classes.searchResultItem}
        href={
          isRealResult
            ? `${articleDetailSlug}?faq_article_id=${result.faq_article_id}&faq_base_id=${result.faq_base_id}&element_id=${result.element_id}&faq_search_url=${encodeURIComponent(searchUrl)}&faq_search_page_name=${encodeURIComponent(searchPageName)}&faq_search_item_name=${encodeURIComponent(result.title)}`
            : '#'
        }>
        <Group
          style={{
            flexGrow: 1,
          }}>
          {illustrationType === 'icon' ? (
            <IconArticle size={actualIconSize} color={iconColor} style={{ flexShrink: 0 }} />
          ) : (
            <ActionIcon
              size={actualIconSize}
              bg={iconBgColor}
              radius={theme.radius.xl}
              style={{ flexShrink: 0 }}>
              <IconCurrencyQuetzal color={iconColor} />
            </ActionIcon>
          )}
          <Text fw={500} c={textColor}>
            {result.title}
          </Text>
        </Group>
        <IconArrowRight size={20} color={textColor} />
      </a>
    </Group>
  );
};

export default withTolgee(SearchResultItem);
