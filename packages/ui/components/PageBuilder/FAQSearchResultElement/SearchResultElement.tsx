import { Group, Title, Flex, rem, useMantineTheme, Loader } from '@mantine/core';
import SearchResultItem from './SearchResultItem';
import { withTolgee } from '../../hoc/withTolgee';
import { useTranslate } from '@tolgee/react';
import {
  generateResponsiveMaxWidth,
  generateResponsivePadding,
  generateResponsiveStyles,
} from '../../../utils';

const SearchResultElement = (props: Record<string, any>) => {
  const {
    searchResults: results,
    backgroundColor,
    maxWidth,
    categoryType,
    searchResultPageSlug,
    elementId,
    illustrationType,
    iconSize,
    iconColor,
    iconBgColor,
    borderColor,
    hasMaxWidth,
    articleDetailSlug,
    isLoading = false,
    padding,
    textColor,
  } = props;
  const theme = useMantineTheme();
  const { t } = useTranslate('page-builder');

  if (isLoading) {
    return (
      <Flex
        w='100%'
        p={rem(80)}
        styles={generateResponsiveStyles({
          backgroundColor: backgroundColor,
        })}
        align='center'
        justify='flex-start'
        gap={rem(10)}>
        <Loader size='sm' color='decaNavy.3' />
        <Title order={3}>{t('loadingResults')}</Title>
      </Flex>
    );
  }

  return (
    <Flex
      direction='column'
      w='100%'
      align='center'
      styles={generateResponsiveStyles({
        padding: generateResponsivePadding(padding),
        backgroundColor: backgroundColor,
      })}>
      <Group
        styles={generateResponsiveStyles({
          maxWidth: generateResponsiveMaxWidth(hasMaxWidth, maxWidth),
        })}>
        <Title order={3} mb={rem(20)} c={textColor}>
          {`${results?.length} ${t('numResultsFound')}`}
        </Title>
        <Group
          styles={generateResponsiveStyles({
            borderRadius: theme.radius.lg,
            border: `${categoryType === 'solid' ? 'none' : `1px solid ${borderColor}`}`,
          })}
          w={'100%'}
          gap={0}>
          {results?.map((result: any, index: number) => (
            <SearchResultItem
              key={`search-result-${index}`}
              result={result}
              index={index}
              totalItems={results.length}
              categoryType={categoryType}
              backgroundColor={backgroundColor}
              maxWidth={maxWidth}
              searchResultPageSlug={searchResultPageSlug}
              elementId={elementId}
              illustrationType={illustrationType}
              iconSize={iconSize}
              iconColor={iconColor}
              iconBgColor={iconBgColor}
              borderColor={borderColor}
              hasMaxWidth={hasMaxWidth}
              articleDetailSlug={articleDetailSlug}
              padding={padding}
              textColor={textColor}
            />
          ))}
        </Group>
      </Group>
    </Flex>
  );
};

export default withTolgee(SearchResultElement);
