import { useCallback, useMemo, useState } from 'react';
import { Box, Flex, rem, ScrollArea } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconTrash } from '@tabler/icons-react';
import ReactQueryBuilder, {
  CombinatorSelectorProps,
  Field,
  FieldSelectorProps,
  FlexibleOptionList,
  FullCombinator,
  OperatorSelectorProps,
  Rule,
  RuleGroupType,
  RuleProps,
  ValueEditorProps,
} from 'react-querybuilder';
import {
  MantineValueEditor,
  MantineValueSelector,
  QueryBuilderMantine,
} from '@react-querybuilder/mantine';
import { QueryBuilderDnD } from '@react-querybuilder/dnd';
import * as ReactDnD from 'react-dnd';
import * as ReactDndHtml5Backend from 'react-dnd-html5-backend';
import { I18nextProvider, useTranslation } from 'react-i18next';
import { TFunction } from 'i18next';
import { uiI18nInstance } from '../../i18n';
import {
  AddConditionButton,
  AddGroupButton,
  RemoveConditionButton,
  RemoveGroupButton,
  RelativeDate,
} from './components';
import { DecaButton } from '../DecaButton';
import 'react-querybuilder/dist/query-builder.css';
import { DatePickerInput, DatesRangeValue, DateValue } from '@mantine/dates';
import dayjs from 'dayjs';
import 'dayjs/locale/en';
import 'dayjs/locale/ja';
import { FieldTypes } from '../DecaTable/types';
import CustomTagsSelect, { TagsOption } from './CustomTagsSelect';
import isDate from 'lodash/isDate';
import isString from 'lodash/isString';
import { CUSTOM_FILTER_OPERATORS } from '../../constants';

const useStyles = createStyles<string, { t: TFunction<'query-builder'> }>((theme, { t }) => ({
  queryBuilder: {
    border: `1px solid ${theme.colors.decaLight[2]}`,
    boxShadow: theme.shadows.md,

    '.ruleGroup': {
      flexDirection: 'column-reverse',
      backgroundColor: 'transparent',
      border: 'none',
    },
    '.ruleGroup-body': {
      display: 'grid !important',
      gridTemplateColumns: 'auto auto',
      alignItems: 'start',
      '>.rule:first-of-type, >.ruleGroup:first-of-type': {
        gridColumnStart: 2,
      },
      '>.ruleGroup': {
        backgroundColor: theme.colors.decaLight[1],
      },
      '.ruleGroup': {
        flexDirection: 'column',
      },
      '.ruleGroup-combinators': {
        width: '6rem',
      },
      '.ruleGroup-combinators :not(:first-of-type), .ruleGroup-combinators input': {
        textTransform: 'lowercase',
      },
      '.ruleGroup-header .queryBuilder-dragHandle:first-of-type': {
        display: 'flex',
        gap: '.5rem',
        width: '100%',
      },
      '.ruleGroup-header .queryBuilder-dragHandle:first-of-type::after': {
        content: `"${t('label.anyOfTheFollowingAreTrue')}"`,
      },
    },
    '.queryBuilder > .ruleGroup > .ruleGroup-body::after': {
      content: `"${t('label.where')}"`,
      display: 'flex',
      alignItems: 'center',
      gridColumnStart: 1,
      gridRowStart: 1,
      paddingRight: rem(8),
      paddingLeft: rem(8),
      width: '6rem',
      height: '38px',
    },
    '.queryBuilder > .ruleGroup > .ruleGroup-body > div:first-of-type .queryBuilder-dragHandle:first-of-type::after':
      {
        content: 'none',
      },

    '.ruleGroup .ruleGroup': {
      backgroundColor: theme.colors.decaLight[1],
      borderRadius: theme.radius.sm,
      padding: rem(8),
    },

    '.field-selector-tag': {
      '.mantine-Select-item': {
        position: 'relative',
      },
      '.mantine-Select-item:last-of-type': {
        marginTop: rem(10),
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          borderTop: `1px solid ${theme.colors.gray[3]}`,
          marginTop: rem(-5),
        },
      },
    },

    '.relativeDateRuleContent': {
      gridColumnStart: 2,
      marginBottom: rem(10),
    },

    // Enable all combinators to ensure they have default styling
    '.ruleGroup-combinators .mantine-Select-input': {
      border: '1px solid #ced4da',
      backgroundColor: '#ffffff',
      boxShadow: 'none',
      cursor: 'pointer',
      color: theme.black,
      padding: theme.spacing.xs,
      pointerEvents: 'auto',
    },
    '.ruleGroup-combinators .mantine-Select-rightSection': {
      display: 'flex',
    },

    // Then disable specific ones (second+ in each group)
    '.betweenRules ~ .betweenRules .ruleGroup-combinators .mantine-Select-input': {
      border: 'none',
      backgroundColor: 'transparent',
      boxShadow: 'none',
      cursor: 'default',
      color: theme.colors.dark[7],
      padding: `0 ${rem(8)}`,
      pointerEvents: 'none',
    },
    '.betweenRules ~ .betweenRules .ruleGroup-combinators .mantine-Select-section': {
      display: 'none',
    },
  },
  queryBuilderEmpty: {
    '.queryBuilder > .ruleGroup > .ruleGroup-body::after': {
      content: 'none',
    },
  },
  clearAll: {
    marginTop: '-2.37rem',
  },
}));

interface QueryBuilderProps {
  fields: Field[];
  initialQuery?: RuleGroupType;
  onQueryChange: (query: RuleGroupType) => void;
  className?: string;
  disabled?: boolean;
}

const CustomValueSelector = (selectorProps: any) => (
  <MantineValueSelector
    {...selectorProps}
    checkIconPosition='right'
    comboboxProps={{ withinPortal: true }}
  />
);

const CustomValueEditor = (props: ValueEditorProps & { disabled?: boolean }) => {
  const { i18n } = useTranslation('query-builder');
  const [value, setValue] = useState<[Date | null, Date | null]>(
    props.value ? [dayjs(props.value[0]).toDate(), dayjs(props.value[1]).toDate()] : [null, null]
  );
  const isDateRange = props.operator.includes('between') || props.operator.includes('notBetween');
  if (props.fieldData.inputType === 'datetime-local') {
    if (props.rule?.operator.includes(CUSTOM_FILTER_OPERATORS.relativeDate)) {
      return <></>;
    }
    if (isDateRange) {
      return (
        <DatePickerInput
          popoverProps={{ withinPortal: true }}
          type='range'
          value={value}
          onChange={(values: DatesRangeValue) => {
            setValue(values);
            props.handleOnChange(
              values
                ? `${dayjs(values[0]).format('YYYY-MM-DDTHH:mmZ')},${dayjs(values[1]).format(
                    'YYYY-MM-DDTHH:mmZ'
                  )}`
                : null
            );
          }}
          locale={i18n.language.includes('ja') ? 'ja' : 'en'}
          monthLabelFormat='YYYY/MM'
          disabled={props.disabled}
        />
      );
    }
    return (
      <DatePickerInput
        popoverProps={{ withinPortal: true }}
        value={
          !props.value || (isString(props.value) && props.value.split(',').length > 1)
            ? null
            : dayjs(props.value).toDate()
        }
        onChange={(value: DateValue) =>
          props.handleOnChange(
            !value || !isDate(value) ? null : dayjs(value).format('YYYY-MM-DDTHH:mmZ')
          )
        }
        locale={i18n.language.includes('ja') ? 'ja' : 'en'}
        monthLabelFormat='YYYY/MM'
        disabled={props.disabled}
      />
    );
  }
  if (props.fieldData.inputType === FieldTypes.TAG) {
    return (
      <CustomTagsSelect
        value={
          !props.value ? [] : typeof props.value === 'string' ? props.value.split(',') : props.value
        }
        values={props.fieldData.values as TagsOption[]}
        onChange={props.handleOnChange}
      />
    );
  }

  return (
    <MantineValueEditor
      {...props}
      disabled={props.disabled}
      selectorComponent={CustomValueSelector}
    />
  );
};

const CustomFieldSelector = (props: FieldSelectorProps) => {
  const isTagFilter = props.options.some(option => option.inputType === FieldTypes.TAG);
  return (
    <MantineValueSelector
      {...props}
      className={`field-selector-${isTagFilter ? 'tag' : ''}`}
      checkIconPosition='right'
      comboboxProps={{ withinPortal: true }}
    />
  );
};

const CustomOperatorSelector = (props: OperatorSelectorProps) => {
  const { t } = useTranslation('query-builder');

  if (props.fieldData.inputType === 'datetime-local') {
    const options = [
      ...props.options,
      // Add custom relativeDate operator
      {
        name: 'relativeDate',
        label: t('operators.relativeDate'),
        value: 'relativeDate',
      },
    ];
    return (
      <MantineValueSelector
        className={`operator-selector`}
        {...props}
        options={options as any}
        checkIconPosition='right'
        comboboxProps={{ withinPortal: true }}
      />
    );
  }
  return (
    <MantineValueSelector
      className={`operator-selector`}
      {...props}
      checkIconPosition='right'
      comboboxProps={{ withinPortal: true }}
    />
  );
};

const CustomCombinatorSelector = (props: CombinatorSelectorProps) => {
  return (
    <MantineValueSelector
      className={`combinator-selector`}
      {...props}
      checkIconPosition='right'
      comboboxProps={{ withinPortal: true }}
    />
  );
};

const CustomRule = (props: RuleProps) => {
  if (props.rule?.operator?.includes(CUSTOM_FILTER_OPERATORS.relativeDate)) {
    return <RelativeDate {...props} />;
  }
  return <Rule {...props} />;
};

const QueryBuilderInternal = (props: QueryBuilderProps) => {
  const { fields, initialQuery, onQueryChange, className, disabled=false } = props;
  const { t } = useTranslation('query-builder');
  const { classes, cx } = useStyles({ t });
  const [query, setQuery] = useState<RuleGroupType>({
    combinator: 'and',
    rules: [],
    ...initialQuery,
  });

  const combinators = useMemo<FlexibleOptionList<FullCombinator>>(
    () => [
      { name: 'and', label: t('combinators.and') },
      { name: 'or', label: t('combinators.or') },
    ],
    [t]
  );

  const fieldsWithPlaceholder = useMemo(() => {
    return fields.map(field => ({ ...field, placeholder: t('placeholder.default') }));
  }, [fields, t]);

  const _onQueryChange = useCallback(
    (query: RuleGroupType) => {
      setQuery(query);
      onQueryChange(query);
    },
    [onQueryChange]
  );

  const onClearAllClick = useCallback(() => {
    const resetQuery: RuleGroupType = { combinator: 'and', rules: [] };
    setQuery(resetQuery);
    onQueryChange(resetQuery);
  }, [onQueryChange]);

  return (
    <Box
      className={cx(classes.queryBuilder, className, {
        [classes.queryBuilderEmpty]: query.rules.length === 0,
      })}
      p={rem(8)}>
      <Box>
        <QueryBuilderMantine>
          <QueryBuilderDnD dnd={{ ...ReactDnD, ...ReactDndHtml5Backend }}>
            <ReactQueryBuilder
              disabled={disabled}
              combinators={combinators}
              showCombinatorsBetweenRules
              fields={fieldsWithPlaceholder}
              query={query}
              onQueryChange={_onQueryChange}
              controlElements={{
                addRuleAction: AddConditionButton,
                addGroupAction: AddGroupButton,
                removeRuleAction: RemoveConditionButton,
                removeGroupAction: RemoveGroupButton,
                valueEditor: CustomValueEditor,
                fieldSelector: CustomFieldSelector,
                operatorSelector: CustomOperatorSelector,
                combinatorSelector: CustomCombinatorSelector,
                rule: CustomRule,
              }}
            />
          </QueryBuilderDnD>
        </QueryBuilderMantine>
      </Box>
      <Box>
        <Flex justify='end' px={rem(8)}>
          <DecaButton
            className={classes.clearAll}
            variant='negative_text'
            size='sm'
            title={t('button.clearAll')}
            leftSection={<IconTrash size={16} />}
            onClick={onClearAllClick}
            disabled={disabled}>
            {t('button.clearAll')}
          </DecaButton>
        </Flex>
      </Box>
    </Box>
  );
};

const QueryBuilder = (props: QueryBuilderProps) => (
  <I18nextProvider i18n={uiI18nInstance}>
    <ScrollArea.Autosize mah={rem(500)}>
      <QueryBuilderInternal {...props} />
    </ScrollArea.Autosize>
  </I18nextProvider>
);

export default QueryBuilder;
