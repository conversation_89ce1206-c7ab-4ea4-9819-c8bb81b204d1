import {
  Box,
  Checkbox,
  Flex,
  Input,
  Menu,
  rem,
  ScrollArea,
  Text,
  useMantineTheme,
} from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { IconChevronDown } from '@tabler/icons-react';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { DecaStatus } from '../DecaStatus';

const useStyles = createStyles(() => ({
  select: {
    alignItems: 'center',
    height: '36px',
    border: '1px solid #ccc',
    borderRadius: '4px',
    padding: '5px',
    cursor: 'pointer',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
  },
  selectedValue: {
    alignItems: 'center',
    overflow: 'hidden',
    whiteSpace: 'nowrap',
    textOverflow: 'ellipsis',
    flex: 1,
  },
  input: {
    '& input': {
      border: 'none',
      outline: 'none',
    },
  },
}));

export interface TagsOption {
  label: string;
  value: string;
}

const CustomTagsSelect = ({
  value: initialValue,
  values,
  onChange,
}: {
  value: string[];
  values: TagsOption[];
  onChange: (value: string[]) => void;
}) => {
  const { t } = useTranslation('table');
  const [selectedValues, setSelectedValues] = useState<string[]>(initialValue);
  const [dropdownOpened, setDropdownOpened] = useState(false);
  const [overflowCount, setOverflowCount] = useState(0);
  const theme = useMantineTheme();
  const [searchValue, setSearchValue] = useState('');
  const { classes } = useStyles();

  useEffect(() => {
    setSelectedValues(initialValue ?? []);
  }, [initialValue]);

  const toggleValue = value => {
    const _selectedValues = selectedValues.includes(value)
      ? selectedValues.filter(item => item !== value)
      : [...selectedValues, value];
    setSelectedValues(_selectedValues);
    onChange(_selectedValues);
  };

  useEffect(() => {
    const calculateOverflow = () => {
      setOverflowCount(selectedValues.length > 1 ? selectedValues.length - 1 : 0);
    };

    calculateOverflow();
  }, [selectedValues]);

  const filteredData = values.filter(item =>
    item.label.toLowerCase().includes(searchValue.toLowerCase())
  );

  const sortedSelectedValues = useMemo(() => {
    return [...selectedValues]?.sort(
      (a, b) =>
        values.findIndex(item => item.value === a) - values.findIndex(item => item.value === b)
    );
  }, [selectedValues, values]);

  return (
    <Menu
      shadow='md'
      width={rem(240)}
      opened={dropdownOpened}
      onClose={() => setDropdownOpened(false)}
      withinPortal={true}>
      <Menu.Target>
        <Flex
          w={rem(240)}
          justify='space-between'
          onClick={() => setDropdownOpened(prev => !prev)}
          className={classes.select}>
          <Flex className={classes.selectedValue}>
            {sortedSelectedValues.length > 0 ? (
              <DecaStatus
                variant='grey'
                text={values.find(item => item.value === sortedSelectedValues[0])?.label}
                showRemove
                onRemove={() => toggleValue(sortedSelectedValues[0])}
              />
            ) : (
              <Text c={'decaGrey.4'} fz={rem(12)}>
                {t('selectValue')}
              </Text>
            )}
            {overflowCount > 0 && (
              <Text ml={rem(5)}>
                <DecaStatus variant='blue' text={`+${overflowCount}`} showRemove={false} />
              </Text>
            )}
          </Flex>
          <IconChevronDown color={theme.colors.decaNavy[5]} size={16} />
        </Flex>
      </Menu.Target>

      <Menu.Dropdown>
        <Input
          className={classes.input}
          placeholder={t('find')}
          value={searchValue}
          onChange={e => setSearchValue(e.target.value)}
        />
        <ScrollArea h={filteredData.length > 5 ? rem(200) : 'auto'}>
          <Flex direction='column' gap={rem(8)}>
            {filteredData.map(item => (
              <Box key={item.value} sx={{ display: 'flex', alignItems: 'center', padding: rem(5) }}>
                <Checkbox
                  checked={selectedValues.includes(item.value)}
                  onChange={() => toggleValue(item.value)}
                  label={item.label}
                />
              </Box>
            ))}
          </Flex>
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
  );
};

export default CustomTagsSelect;
