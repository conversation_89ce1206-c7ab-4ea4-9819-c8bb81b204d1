import { render, screen, fireEvent, act, waitFor } from '../../../test-utils/testing-library';
import { vi } from 'vitest';
import {
  MessageFileDropzoneProvider,
  useMessageFileDropzone,
  useMessageFileDropzoneOptional,
} from './MessageFileDropzone';
import React from 'react';

// Mock Mantine components
vi.mock('@mantine/core', async importOriginal => {
  const actual = await importOriginal<typeof import('@mantine/core')>();
  return {
    ...actual,
    Box: ({ children, style, className, onMouseLeave, ...props }: any) => (
      <div style={style} className={className} onMouseLeave={onMouseLeave} {...props}>
        {children}
      </div>
    ),
  };
});

// Mock Tabler icons
vi.mock('@tabler/icons-react', () => ({
  IconX: ({ size, color, ...props }: any) => (
    <div data-testid='icon-x' data-size={size} data-color={color} {...props}>
      X
    </div>
  ),
  IconFilePlus: ({ size, color, ...props }: any) => (
    <div data-testid='icon-file-plus' data-size={size} data-color={color} {...props}>
      +
    </div>
  ),
}));

// Mock Mantine Dropzone with more complete implementation
vi.mock('@mantine/dropzone', () => {
  const DropzoneComponent = ({
    children,
    onDrop,
    onReject,
    onClick,
    maxSize,
    maxFiles,
    accept,
    multiple,
    disabled,
    style,
    ...props
  }: any) => {
    // Filter out Dropzone-specific props that shouldn't be passed to DOM
    const {
      maxSize: _,
      maxFiles: __,
      accept: ___,
      multiple: ____,
      disabled: _____,
      ...domProps
    } = props;

    return (
      <div
        data-testid='dropzone'
        onClick={onClick}
        onDrop={(e: any) => {
          e.preventDefault();
          const files = Array.from(e.dataTransfer?.files || []);

          // Simulate file validation
          const rejectedFiles = files.filter((file: any) => file.size > maxSize);
          const acceptedFiles = files.filter((file: any) => file.size <= maxSize);

          if (rejectedFiles.length > 0) {
            onReject?.(rejectedFiles);
          }
          if (acceptedFiles.length > 0) {
            onDrop(acceptedFiles);
          }
        }}
        onDragOver={(e: any) => {
          e.preventDefault();
        }}
        style={style}
        {...domProps}>
        {children}
      </div>
    );
  };

  // Add sub-components as static properties
  DropzoneComponent.Accept = ({ children }: any) => (
    <div data-testid='dropzone-accept'>{children}</div>
  );
  DropzoneComponent.Reject = ({ children }: any) => (
    <div data-testid='dropzone-reject'>{children}</div>
  );
  DropzoneComponent.Idle = ({ children }: any) => <div data-testid='dropzone-idle'>{children}</div>;

  return {
    Dropzone: DropzoneComponent,
  };
});

// Mock the styles hook
vi.mock('../hooks/useMessageDropzoneStyles', () => ({
  useMessageDropzoneStyles: () => ({
    classes: {
      dropzoneOverlay: 'dropzone-overlay',
      dropzoneContent: 'dropzone-content',
      dropzoneInner: 'dropzone-inner',
      dropzoneText: 'dropzone-text',
      dropzoneSubtext: 'dropzone-subtext',
    },
  }),
}));

// Mock console.error to test error handling
const mockConsoleError = vi.spyOn(console, 'error').mockImplementation(() => {});

// Enhanced DOM API mocks
global.DragEvent = class DragEvent extends Event {
  dataTransfer: DataTransfer | null;

  constructor(type: string, eventInitDict?: DragEventInit) {
    super(type, eventInitDict);
    this.dataTransfer = eventInitDict?.dataTransfer || null;
  }
} as any;

global.DataTransfer = class DataTransfer {
  dropEffect: string = 'none';
  effectAllowed: string = 'all';
  files: FileList = [] as any;
  items: DataTransferItemList = [] as any;
  types: string[] = [];

  clearData(format?: string): void {}
  getData(format: string): string {
    return '';
  }
  setData(format: string, data: string): void {}
  setDragImage(image: Element, x: number, y: number): void {}
} as any;

// Mock document.activeElement and blur for testing file rejection blur timeout
const mockActiveElement = {
  blur: vi.fn(),
};

Object.defineProperty(document, 'activeElement', {
  get: () => mockActiveElement,
  configurable: true,
});

// Test component that uses the hook
const TestHookComponent = () => {
  const { isDragActive, onFilesDrop, addFiles, registerInputHandler } = useMessageFileDropzone();

  React.useEffect(() => {
    const unregister = registerInputHandler(files => {
      // Handler registered for testing
    });
    return unregister;
  }, [registerInputHandler]);

  return (
    <div>
      <div data-testid='drag-active'>{isDragActive ? 'active' : 'inactive'}</div>
      <button
        data-testid='trigger-drop'
        onClick={() => onFilesDrop([new File(['test'], 'test.txt') as any])}>
        Trigger Drop
      </button>
      <button
        data-testid='add-files'
        onClick={() => addFiles([new File(['test'], 'test.txt') as any])}>
        Add Files
      </button>
    </div>
  );
};

// Test component that uses hook outside provider (should throw)
const TestHookOutsideProvider = () => {
  try {
    useMessageFileDropzone();
    return <div>Should not render</div>;
  } catch (error) {
    return <div data-testid='hook-error'>{(error as Error).message}</div>;
  }
};

// Test component that uses the optional hook
const TestOptionalHookComponent = () => {
  const context = useMessageFileDropzoneOptional();
  return <div data-testid='optional-hook-result'>{context ? 'has-context' : 'no-context'}</div>;
};

// Test component for testing multiple input handlers
const TestMultipleHandlersComponent = () => {
  const { registerInputHandler, onFilesDrop } = useMessageFileDropzone();
  const [handlerCount, setHandlerCount] = React.useState(0);

  React.useEffect(() => {
    const handler1 = (files: any[]) => {
      // Handler 1 for testing
    };
    const handler2 = (files: any[]) => {
      // Handler 2 for testing
    };
    const handler3 = (files: any[]) => {
      throw new Error('Handler 3 error');
    };

    const unregister1 = registerInputHandler(handler1);
    const unregister2 = registerInputHandler(handler2);
    const unregister3 = registerInputHandler(handler3);

    setHandlerCount(3);

    return () => {
      unregister1();
      unregister2();
      unregister3();
    };
  }, [registerInputHandler]);

  return (
    <div>
      <div data-testid='handler-count'>{handlerCount}</div>
      <button
        data-testid='trigger-handlers'
        onClick={() => onFilesDrop([new File(['test'], 'test.txt') as any])}>
        Trigger Handlers
      </button>
    </div>
  );
};

describe('MessageFileDropzone', () => {
  beforeEach(() => {
    mockConsoleError.mockClear();
    mockActiveElement.blur.mockClear();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.useRealTimers();
  });

  describe('useMessageFileDropzone Hook', () => {
    it('throws error when used outside provider', () => {
      render(<TestHookOutsideProvider />);
      expect(screen.getByTestId('hook-error')).toHaveTextContent(
        'useMessageFileDropzone must be used within a MessageFileDropzoneProvider'
      );
    });

    it('provides context when used within provider', () => {
      const onFilesDrop = vi.fn();
      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
      expect(screen.getByTestId('trigger-drop')).toBeInTheDocument();
      expect(screen.getByTestId('add-files')).toBeInTheDocument();
    });

    it('memoizes context value to prevent unnecessary re-renders', () => {
      const onFilesDrop = vi.fn();
      let contextValue1: any;
      let contextValue2: any;

      const TestMemoization = () => {
        const context = useMessageFileDropzone();
        if (!contextValue1) {
          contextValue1 = context;
        } else if (!contextValue2) {
          contextValue2 = context;
        }
        return <div>Test</div>;
      };

      const { rerender } = render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestMemoization />
        </MessageFileDropzoneProvider>
      );

      rerender(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestMemoization />
        </MessageFileDropzoneProvider>
      );

      // Context values should be referentially equal due to memoization
      expect(contextValue1).toBe(contextValue2);
    });
  });

  describe('useMessageFileDropzoneOptional Hook', () => {
    it('returns null when used outside provider', () => {
      render(<TestOptionalHookComponent />);
      expect(screen.getByTestId('optional-hook-result')).toHaveTextContent('no-context');
    });

    it('returns context when used within provider', () => {
      const onFilesDrop = vi.fn();
      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestOptionalHookComponent />
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByTestId('optional-hook-result')).toHaveTextContent('has-context');
    });
  });

  describe('MessageFileDropzoneProvider', () => {
    it('renders children correctly', () => {
      const onFilesDrop = vi.fn();
      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <div data-testid='child-content'>Test Content</div>
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByTestId('child-content')).toHaveTextContent('Test Content');
    });

    it('handles file drop through context', () => {
      const onFilesDrop = vi.fn();
      const onAddFilesToInput = vi.fn();

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          onAddFilesToInput={onAddFilesToInput}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      const triggerButton = screen.getByTestId('trigger-drop');
      fireEvent.click(triggerButton);

      expect(onFilesDrop).toHaveBeenCalled();
      expect(onAddFilesToInput).toHaveBeenCalled();
    });

    it('handles addFiles through context', () => {
      const onFilesDrop = vi.fn();
      const onAddFilesToInput = vi.fn();

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          onAddFilesToInput={onAddFilesToInput}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      const addButton = screen.getByTestId('add-files');
      fireEvent.click(addButton);

      expect(onAddFilesToInput).toHaveBeenCalled();
      expect(onFilesDrop).not.toHaveBeenCalled();
    });

    it('handles addFiles without onAddFilesToInput callback', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      const addButton = screen.getByTestId('add-files');
      fireEvent.click(addButton);

      // Should not throw error even without onAddFilesToInput
      expect(onFilesDrop).not.toHaveBeenCalled();
    });

    it('handles global drag events with proper counter management', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      // Simulate multiple drag enter events (nested elements)
      const createDragEvent = (type: string, hasItems = true) => {
        const event = new DragEvent(type, {
          bubbles: true,
          dataTransfer: new DataTransfer(),
        });

        if (hasItems) {
          Object.defineProperty(event.dataTransfer, 'items', {
            value: [{ kind: 'file' }],
            writable: false,
          });
        }

        return event;
      };

      // First drag enter - should activate
      act(() => {
        document.dispatchEvent(createDragEvent('dragenter'));
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('active');

      // Second drag enter (child element) - should stay active
      act(() => {
        document.dispatchEvent(createDragEvent('dragenter'));
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('active');

      // First drag leave - should stay active (counter = 1)
      act(() => {
        document.dispatchEvent(createDragEvent('dragleave'));
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('active');

      // Second drag leave - should deactivate (counter = 0)
      act(() => {
        document.dispatchEvent(createDragEvent('dragleave'));
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
    });

    it('handles drag over events with correct drop effect', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      const dragOverEvent = new DragEvent('dragover', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });

      act(() => {
        document.dispatchEvent(dragOverEvent);
      });

      expect(dragOverEvent.dataTransfer?.dropEffect).toBe('copy');
    });

    it('ignores drag events when disabled', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} disabled={true}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      const dragEnterEvent = new DragEvent('dragenter', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });

      Object.defineProperty(dragEnterEvent.dataTransfer, 'items', {
        value: [{ kind: 'file' }],
        writable: false,
      });

      act(() => {
        document.dispatchEvent(dragEnterEvent);
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
    });

    it('handles overlay mouse leave to reset drag state', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      // First activate drag state
      const dragEnterEvent = new DragEvent('dragenter', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });

      Object.defineProperty(dragEnterEvent.dataTransfer, 'items', {
        value: [{ kind: 'file' }],
        writable: false,
      });

      act(() => {
        document.dispatchEvent(dragEnterEvent);
      });

      expect(screen.getByTestId('drag-active')).toHaveTextContent('active');

      // Find the overlay element and trigger mouse leave
      const overlay = screen.getByTestId('dropzone').closest('[data-active="true"]');

      if (overlay) {
        act(() => {
          fireEvent.mouseLeave(overlay);
        });

        expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
      }
    });

    it('handles input handler registration and cleanup', () => {
      const onFilesDrop = vi.fn();
      const mockHandler = vi.fn();

      const TestComponent = () => {
        const { registerInputHandler } = useMessageFileDropzone();

        React.useEffect(() => {
          const unregister = registerInputHandler(mockHandler);
          return unregister;
        }, [registerInputHandler]);

        return <div>Test</div>;
      };

      const { unmount } = render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestComponent />
        </MessageFileDropzoneProvider>
      );

      // Unmount to test cleanup
      unmount();

      // Handler should be cleaned up and not called
      expect(mockHandler).not.toHaveBeenCalled();
    });

    it('handles multiple input handlers with error handling', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestMultipleHandlersComponent />
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByTestId('handler-count')).toHaveTextContent('3');

      const triggerButton = screen.getByTestId('trigger-handlers');
      fireEvent.click(triggerButton);

      // Should handle error in one handler gracefully
      expect(mockConsoleError).toHaveBeenCalledWith('Error in input handler:', expect.any(Error));
      expect(onFilesDrop).toHaveBeenCalled();
    });

    it('handles file rejection with blur timeout', async () => {
      const onFilesDrop = vi.fn();
      const onFilesReject = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} onFilesReject={onFilesReject}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Get the provider dropzone
      const dropzones = screen.getAllByTestId('dropzone');
      const providerDropzone = dropzones.find(d => d.style.position === 'absolute');

      if (providerDropzone) {
        // Create a large file that should be rejected
        const largeFile = new File(['x'.repeat(20000000)], 'large.txt');
        Object.defineProperty(largeFile, 'size', { value: 20000000 });

        fireEvent.drop(providerDropzone, {
          dataTransfer: { files: [largeFile] },
        });

        expect(onFilesReject).toHaveBeenCalledWith([largeFile]);

        // Fast-forward the blur timeout
        act(() => {
          vi.advanceTimersByTime(100);
        });

        expect(mockActiveElement.blur).toHaveBeenCalled();
      }
    });

    it('handles different file types in accept configuration', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          acceptedFileTypes={['image/*', '.pdf', '.doc', '.docx', '.txt', 'application/json']}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Component should render without errors with various file types
      expect(screen.getByText('Test')).toBeInTheDocument();
    });

    it('renders dropzone overlay with all states', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Check for all dropzone states
      expect(screen.getByTestId('dropzone-accept')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-idle')).toBeInTheDocument();

      // Check for icons in different states
      expect(screen.getByTestId('icon-x')).toBeInTheDocument(); // Reject state
      expect(screen.getAllByTestId('icon-file-plus')).toHaveLength(2); // Accept and Idle states
    });

    it('cleans up event listeners on unmount', () => {
      const onFilesDrop = vi.fn();
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');

      const { unmount } = render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragenter', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragleave', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragover', expect.any(Function));
    });

    it('cleans up event listeners when disabled', () => {
      const onFilesDrop = vi.fn();
      const removeEventListenerSpy = vi.spyOn(document, 'removeEventListener');

      const { rerender } = render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} disabled={false}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      rerender(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} disabled={true}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragenter', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragleave', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('dragover', expect.any(Function));
    });

    it('handles empty accepted file types array', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} acceptedFileTypes={[]}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByText('Test')).toBeInTheDocument();
    });

    it('handles successful file drop on provider dropzone', () => {
      const onFilesDrop = vi.fn();
      const onAddFilesToInput = vi.fn();

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          onAddFilesToInput={onAddFilesToInput}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Get the provider dropzone
      const dropzones = screen.getAllByTestId('dropzone');
      const providerDropzone = dropzones.find(d => d.style.position === 'absolute');

      if (providerDropzone) {
        const file = new File(['test'], 'test.txt', { type: 'text/plain' });
        Object.defineProperty(file, 'size', { value: 1000 }); // Small file

        fireEvent.drop(providerDropzone, {
          dataTransfer: { files: [file] },
        });

        expect(onFilesDrop).toHaveBeenCalledWith([file]);
        expect(onAddFilesToInput).toHaveBeenCalledWith([file]);
      }
    });

    it('uses custom reject messages correctly', () => {
      const onFilesDrop = vi.fn();
      const customRejectMessage = 'Custom file validation error';
      const customRejectSubMessage = 'Please check your file and try again';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Verify the dropzone states are rendered
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-accept')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-idle')).toBeInTheDocument();
    });

    it('handles drag events without dataTransfer items gracefully', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      // Simulate drag enter without items
      const dragEnterEvent = new DragEvent('dragenter', {
        bubbles: true,
        dataTransfer: new DataTransfer(),
      });

      // No items in dataTransfer
      Object.defineProperty(dragEnterEvent.dataTransfer, 'items', {
        value: [],
        writable: false,
      });

      act(() => {
        document.dispatchEvent(dragEnterEvent);
      });

      // Should not activate drag state without files
      expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
    });

    it('maintains component structure with custom file size and count limits', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          maxFileSize={5000000} // 5MB
          maxFiles={5}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      expect(screen.getByText('Test')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone')).toBeInTheDocument();
    });

    it('uses default reject messages when custom messages are not provided', () => {
      const onFilesDrop = vi.fn();

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Check that default reject messages are rendered
      const rejectState = screen.getByTestId('dropzone-reject');
      expect(rejectState).toBeInTheDocument();

      // The default messages should be from UI_TEXT constants
      // Since we're testing the component structure, we verify the reject state exists
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
    });

    it('uses custom reject messages when provided', () => {
      const onFilesDrop = vi.fn();
      const customRejectMessage = 'Custom file validation error';
      const customRejectSubMessage = 'Please check your file and try again';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Check that the dropzone reject state exists
      const rejectState = screen.getByTestId('dropzone-reject');
      expect(rejectState).toBeInTheDocument();

      // Verify the reject icon is present
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
    });

    it('handles custom reject message props correctly', () => {
      const onFilesDrop = vi.fn();
      const customRejectMessage = 'File too large (maximum size is 10MB) or unsupported';
      const customRejectSubMessage = 'Please try again!';

      const { rerender } = render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <div data-testid='test-content'>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Verify component renders with custom messages
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();

      // Test with different custom messages
      const newRejectMessage = 'Different error message';
      const newRejectSubMessage = 'Different sub message';

      rerender(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectMessage={newRejectMessage}
          rejectSubMessage={newRejectSubMessage}>
          <div data-testid='test-content'>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Component should still render correctly with new messages
      expect(screen.getByTestId('test-content')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
    });

    it('handles only rejectMessage prop provided', () => {
      const onFilesDrop = vi.fn();
      const customRejectMessage = 'Custom error message only';

      render(
        <MessageFileDropzoneProvider onFilesDrop={onFilesDrop} rejectMessage={customRejectMessage}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Should render without errors even with only one custom message
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
    });

    it('handles only rejectSubMessage prop provided', () => {
      const onFilesDrop = vi.fn();
      const customRejectSubMessage = 'Custom sub message only';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectSubMessage={customRejectSubMessage}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // Should render without errors even with only sub message
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('icon-x')).toBeInTheDocument();
    });

    it('handles file rejection with custom messages', () => {
      const onFilesDrop = vi.fn();
      const onFilesReject = vi.fn();
      const customRejectMessage = 'Files rejected with custom message';
      const customRejectSubMessage = 'Custom rejection details';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          onFilesReject={onFilesReject}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      // Verify component renders with custom reject messages
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('drag-active')).toHaveTextContent('inactive');
    });

    it('preserves other functionality with custom reject messages', () => {
      const onFilesDrop = vi.fn();
      const onAddFilesToInput = vi.fn();
      const customRejectMessage = 'Custom reject';
      const customRejectSubMessage = 'Custom sub reject';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          onAddFilesToInput={onAddFilesToInput}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <TestHookComponent />
        </MessageFileDropzoneProvider>
      );

      // Test that other functionality still works
      const triggerButton = screen.getByTestId('trigger-drop');
      fireEvent.click(triggerButton);

      expect(onFilesDrop).toHaveBeenCalled();
      expect(onAddFilesToInput).toHaveBeenCalled();

      // Test add files functionality
      const addButton = screen.getByTestId('add-files');
      fireEvent.click(addButton);

      expect(onAddFilesToInput).toHaveBeenCalledTimes(2);
    });

    it('renders all dropzone states with custom reject messages', () => {
      const onFilesDrop = vi.fn();
      const customRejectMessage = 'Translation key message';
      const customRejectSubMessage = 'Translation key sub message';

      render(
        <MessageFileDropzoneProvider
          onFilesDrop={onFilesDrop}
          rejectMessage={customRejectMessage}
          rejectSubMessage={customRejectSubMessage}>
          <div>Test</div>
        </MessageFileDropzoneProvider>
      );

      // All three dropzone states should be present
      expect(screen.getByTestId('dropzone-accept')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-reject')).toBeInTheDocument();
      expect(screen.getByTestId('dropzone-idle')).toBeInTheDocument();

      // Icons should be present in different states
      expect(screen.getByTestId('icon-x')).toBeInTheDocument(); // Reject state
      expect(screen.getAllByTestId('icon-file-plus')).toHaveLength(2); // Accept and Idle states
    });
  });
});
