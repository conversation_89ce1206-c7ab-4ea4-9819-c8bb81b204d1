import { Modal as BaseModal, Box, Divider, Flex, ModalProps, rem } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import { DecaButton, DecaButtonProps } from '../DecaButton';
import { IconTrash } from '@tabler/icons-react';

const useStyles = createStyles(() => ({
  body: {
    whiteSpace: 'pre-line',
  },
  modal: {
    '.mantine-Modal-header': {
      padding: `${rem(16)} ${rem(24)}`,
    },
    '.mantine-Modal-title': {
      fontSize: rem(18),
      fontWeight: 500,
    },
    '.mantine-Modal-body': {
      padding: 0,
    },
  },
}));

type Props = ModalProps & {
  opened?: boolean;
  okText?: string;
  cancelText?: string;
  deleteText?: string;
  okButtonProps?: Omit<DecaButtonProps, 'children' | 'onClick'>;
  cancelButtonProps?: Omit<DecaButtonProps, 'children' | 'onClick'>;
  deleteButtonProps?: Omit<DecaButtonProps, 'children' | 'onClick'>;
  onOk?: () => void;
  onCancel?: () => void;
  onDelete?: () => void;
  bodyPadding?: string | number;
  children?: React.ReactNode;
  footerDivider?: boolean;
  leftButton?: React.ReactNode;
};

export const Modal = (props: Props) => {
  const {
    className,
    okText = '',
    cancelText = '',
    deleteText = '',
    okButtonProps,
    cancelButtonProps,
    deleteButtonProps,
    closeOnClickOutside = false,
    children,
    bodyPadding = rem(24),
    onOk,
    onCancel,
    onDelete,
    footerDivider = true,
    leftButton,
    ...rest
  } = props;
  const { classes, cx } = useStyles();

  return (
    <BaseModal
      closeOnClickOutside={closeOnClickOutside}
      className={cx(classes.modal, className)}
      {...rest}>
      <Box px={bodyPadding} py={rem(16)} className={classes.body}>
        {children}
      </Box>
      {footerDivider && <Divider w={'100%'} />}
      {(deleteText || okText || cancelText || leftButton) && (
        <Flex
          px={rem(24)}
          py={rem(16)}
          justify={deleteText || leftButton ? 'space-between' : 'flex-end'}
          align='center'>
          {leftButton}
          {deleteText && (
            <DecaButton
              leftSection={<IconTrash />}
              variant='negative_text'
              onClick={onDelete}
              data-testid='deca-modal-delete-button'
              {...deleteButtonProps}>
              {deleteText}
            </DecaButton>
          )}
          {!okText && !cancelText ? null : (
            <Flex gap={rem(16)} justify='flex-end'>
              {cancelText && (
                <DecaButton
                  data-testid='deca-modal-cancel-button'
                  variant='neutral'
                  onClick={onCancel}
                  {...cancelButtonProps}>
                  {cancelText}
                </DecaButton>
              )}
              {okText && (
                <DecaButton
                  data-testid='deca-modal-ok-button'
                  variant='primary'
                  onClick={onOk}
                  {...okButtonProps}>
                  {okText}
                </DecaButton>
              )}
            </Flex>
          )}
        </Flex>
      )}
    </BaseModal>
  );
};
