import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import DecaMarkdown from './DecaMarkdown';
import {
  getTargetFromLinkQuery,
  normalizeMarkdownForReactMarkdown,
} from '@resola-ai/blocknote-editor';

// Mock Mantine components
vi.mock('@mantine/core', () => ({
  Box: ({ children, className, ...props }) => (
    <div className={className} data-testid='deca-markdown' {...props}>
      {children}
    </div>
  ),
}));

vi.mock('@mantine/emotion', () => ({
  createStyles: vi.fn(() => () => ({
    classes: {
      markdownBox: 'markdownBox',
    },
    cx: (...args) => args.filter(Boolean).join(' '),
  })),
}));

// Mock ReactMarkdown with a more realistic implementation
vi.mock('react-markdown', () => ({
  default: ({ children, components }) => {
    const content = children || '';

    // Handle different markdown content types based on the content
    if (content.includes('[') && content.includes('](')) {
      // Handle links
      const linkMatch = content.match(/\[(.*?)\]\((.*?)\)/);
      if (linkMatch && components?.a) {
        const LinkComponent = components.a;
        return (
          <div data-testid='react-markdown'>
            <LinkComponent href={linkMatch[2]}>{linkMatch[1]}</LinkComponent>
          </div>
        );
      }
    }

    // Handle phone links
    if (content.includes('tel:') && components?.phone) {
      const PhoneComponent = components.phone;
      const phoneMatch = content.match(/\[(.*?)\]\((tel:.*?)\)/);
      if (phoneMatch) {
        return (
          <div data-testid='react-markdown'>
            <PhoneComponent href={phoneMatch[2]}>{phoneMatch[1]}</PhoneComponent>
          </div>
        );
      }
    }

    // Handle task lists with checkboxes
    if (content.includes('- [x]') || content.includes('- [ ]')) {
      const CheckboxComponent = components?.input;
      return (
        <div data-testid='react-markdown'>
          <ul className='contains-task-list'>
            <li className='task-list-item'>
              <p>
                {CheckboxComponent ? (
                  <CheckboxComponent checked={content.includes('- [x]')} />
                ) : (
                  <input type='checkbox' checked={content.includes('- [x]')} readOnly />
                )}
                Task item
              </p>
            </li>
          </ul>
        </div>
      );
    }

    // Handle regular lists
    if (content.includes('- ') && !content.includes('- [')) {
      return (
        <div data-testid='react-markdown'>
          <ul>
            <li>List item</li>
          </ul>
        </div>
      );
    }

    // Handle headings
    if (content.startsWith('#')) {
      const level = content.match(/^#+/)?.[0].length || 1;
      const text = content.replace(/^#+\s*/, '');
      const HeadingTag = `h${level}` as keyof JSX.IntrinsicElements;
      return (
        <div data-testid='react-markdown'>
          <HeadingTag>{text}</HeadingTag>
        </div>
      );
    }

    // Handle code blocks
    if (content.includes('```')) {
      const codeMatch = content.match(/```.*?\n([\s\S]*?)```/);
      return (
        <div data-testid='react-markdown'>
          <pre>
            <code>{codeMatch?.[1] || ''}</code>
          </pre>
        </div>
      );
    }

    // Handle tables
    if (content.includes('|') && content.includes('---')) {
      return (
        <div data-testid='react-markdown'>
          <table>
            <thead>
              <tr>
                <th>Header 1</th>
                <th>Header 2</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Cell 1</td>
                <td>Cell 2</td>
              </tr>
            </tbody>
          </table>
        </div>
      );
    }

    // Default case - just render the content
    return <div data-testid='react-markdown'>{content}</div>;
  },
}));

// Mock remark and rehype plugins
vi.mock('remark-gfm', () => ({ default: {} }));
vi.mock('remark-html', () => ({ default: {} }));
vi.mock('rehype-raw', () => ({ default: {} }));

// Mock the blocknote-editor functions
vi.mock('@resola-ai/blocknote-editor', () => ({
  getTargetFromLinkQuery: vi.fn(),
  normalizeMarkdownForReactMarkdown: vi.fn(markdown => markdown),
}));

// Test constants
const MOCK_MARKDOWN = {
  HEADING: '# Hello World',
  PARAGRAPH: 'This is a simple paragraph.',
  LINK: '[Visit Resola](https://resola.ai)',
  LINK_WITH_TARGET: '[Visit Resola](https://resola.ai?target=_self)',
  PHONE_LINK: 'Call us at [+123456789](tel:+123456789)',
  UNORDERED_LIST: '- Item 1\n- Item 2\n- Item 3',
  TASK_LIST_CHECKED: '- [x] Completed task',
  TASK_LIST_UNCHECKED: '- [ ] Incomplete task',
  CODE_BLOCK: '```javascript\nconst hello = "world";\nconsole.log(hello);\n```',
  TABLE: '| Header 1 | Header 2 |\n|----------|----------|\n| Cell 1   | Cell 2   |',
  EMPTY: '',
} as const;

describe('DecaMarkdown', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Set default mock return values
    (getTargetFromLinkQuery as any).mockReturnValue(null);
    (normalizeMarkdownForReactMarkdown as any).mockImplementation(markdown => markdown);
  });

  describe('Basic Rendering', () => {
    it('renders markdown content correctly', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.HEADING} />);

      expect(screen.getByTestId('deca-markdown')).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Hello World');
    });

    it('applies default className when no custom className provided', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.PARAGRAPH} />);

      const container = screen.getByTestId('deca-markdown');
      expect(container).toHaveClass('markdownBox');
    });

    it('applies custom className alongside default className', () => {
      const customClass = 'custom-markdown-class';
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.PARAGRAPH} className={customClass} />);

      const container = screen.getByTestId('deca-markdown');
      expect(container).toHaveClass('markdownBox', customClass);
    });

    it('handles empty markdown content', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.EMPTY} />);

      expect(screen.getByTestId('deca-markdown')).toBeInTheDocument();
      expect(screen.getByTestId('react-markdown')).toBeEmptyDOMElement();
    });
  });

  describe('Link Rendering', () => {
    it('renders regular links with default target="_blank"', () => {
      (getTargetFromLinkQuery as any).mockReturnValue(null);

      render(<DecaMarkdown markdown={MOCK_MARKDOWN.LINK} />);

      const link = screen.getByText('Visit Resola');
      expect(link.tagName).toBe('A');
      expect(link).toHaveAttribute('href', 'https://resola.ai');
      expect(link).toHaveAttribute('target', '_blank');
      expect(link).toHaveAttribute('rel', 'noreferrer');
    });

    it('renders links with custom target from query parameter', () => {
      (getTargetFromLinkQuery as any).mockReturnValue('_self');

      render(<DecaMarkdown markdown={MOCK_MARKDOWN.LINK_WITH_TARGET} />);

      const link = screen.getByText('Visit Resola');
      expect(link).toHaveAttribute('target', '_self');
      expect(getTargetFromLinkQuery).toHaveBeenCalledWith('https://resola.ai?target=_self');
    });

    it('renders phone links correctly', () => {
      (getTargetFromLinkQuery as any).mockReturnValue(null);

      render(<DecaMarkdown markdown={MOCK_MARKDOWN.PHONE_LINK} />);

      const phoneLink = screen.getByText('+123456789');
      expect(phoneLink.tagName).toBe('A');
      expect(phoneLink).toHaveAttribute('href', 'tel:+123456789');
      expect(phoneLink).toHaveAttribute('target', '_blank');
      expect(phoneLink).toHaveAttribute('rel', 'noreferrer');
    });
  });

  describe('List Rendering', () => {
    it('renders unordered lists correctly', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.UNORDERED_LIST} />);

      const list = screen.getByRole('list');
      expect(list).toBeInTheDocument();
      expect(screen.getByRole('listitem')).toBeInTheDocument();
    });

    it('renders task lists with checked checkbox', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.TASK_LIST_CHECKED} />);

      const container = screen.getByTestId('react-markdown');
      const taskList = container.querySelector('.contains-task-list');
      expect(taskList).toBeInTheDocument();

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeChecked();
      expect(checkbox).toHaveAttribute('readOnly');
    });

    it('renders task lists with unchecked checkbox', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.TASK_LIST_UNCHECKED} />);

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).not.toBeChecked();
      expect(checkbox).toHaveAttribute('readOnly');
    });
  });

  describe('Code and Table Rendering', () => {
    it('renders code blocks correctly', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.CODE_BLOCK} />);

      const codeElement = screen.getByText((content, element) => {
        return (
          element?.tagName === 'CODE' &&
          content.includes('const hello = "world";') &&
          content.includes('console.log(hello);')
        );
      });
      expect(codeElement.tagName).toBe('CODE');
      expect(codeElement.parentElement?.tagName).toBe('PRE');
    });

    it('renders tables correctly', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.TABLE} />);

      expect(screen.getByRole('table')).toBeInTheDocument();
      expect(screen.getByText('Header 1')).toBeInTheDocument();
      expect(screen.getByText('Header 2')).toBeInTheDocument();
      expect(screen.getByText('Cell 1')).toBeInTheDocument();
      expect(screen.getByText('Cell 2')).toBeInTheDocument();
    });
  });

  describe('Component Integration', () => {
    it('calls normalizeMarkdownForReactMarkdown with provided markdown', () => {
      const testMarkdown = '# Test Markdown';
      render(<DecaMarkdown markdown={testMarkdown} />);

      expect(normalizeMarkdownForReactMarkdown).toHaveBeenCalledWith(testMarkdown);
    });

    it('passes correct props to ReactMarkdown', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.HEADING} />);

      // Verify the component rendered correctly, indicating proper props were passed
      expect(screen.getByTestId('react-markdown')).toBeInTheDocument();
    });

    it('uses memoization for performance optimization', () => {
      const { rerender } = render(<DecaMarkdown markdown={MOCK_MARKDOWN.HEADING} />);

      // First render should call normalizeMarkdownForReactMarkdown
      expect(normalizeMarkdownForReactMarkdown).toHaveBeenCalledTimes(1);

      // Rerender with same markdown should not call normalizeMarkdownForReactMarkdown again
      rerender(<DecaMarkdown markdown={MOCK_MARKDOWN.HEADING} />);
      expect(normalizeMarkdownForReactMarkdown).toHaveBeenCalledTimes(1);

      // Rerender with different markdown should call normalizeMarkdownForReactMarkdown again
      rerender(<DecaMarkdown markdown={MOCK_MARKDOWN.PARAGRAPH} />);
      expect(normalizeMarkdownForReactMarkdown).toHaveBeenCalledTimes(2);
    });
  });

  describe('Custom Renderers', () => {
    it('uses LinkRenderer for regular links', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.LINK} />);

      const link = screen.getByText('Visit Resola');
      expect(link).toBeInTheDocument();
      expect(getTargetFromLinkQuery).toHaveBeenCalled();
    });

    it('uses PhoneRenderer for phone links', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.PHONE_LINK} />);

      const phoneLink = screen.getByText('+123456789');
      expect(phoneLink).toBeInTheDocument();
    });

    it('uses CheckboxRenderer for task list checkboxes', () => {
      render(<DecaMarkdown markdown={MOCK_MARKDOWN.TASK_LIST_CHECKED} />);

      const checkbox = screen.getByRole('checkbox');
      expect(checkbox).toBeInTheDocument();
      expect(checkbox).toHaveAttribute('readOnly');
    });
  });

  describe('Edge Cases', () => {
    it('handles malformed markdown gracefully', () => {
      const malformedMarkdown = '[Incomplete link](';

      expect(() => {
        render(<DecaMarkdown markdown={malformedMarkdown} />);
      }).not.toThrow();

      expect(screen.getByTestId('deca-markdown')).toBeInTheDocument();
    });

    it('handles very long markdown content', () => {
      const longMarkdown = '# '.repeat(1000) + 'Long Content';

      expect(() => {
        render(<DecaMarkdown markdown={longMarkdown} />);
      }).not.toThrow();

      expect(screen.getByTestId('deca-markdown')).toBeInTheDocument();
    });

    it('handles markdown with special characters', () => {
      const specialCharsMarkdown = '# Title with émojis 🚀 and spéciàl chars!';

      render(<DecaMarkdown markdown={specialCharsMarkdown} />);

      expect(screen.getByTestId('deca-markdown')).toBeInTheDocument();
    });
  });
});
