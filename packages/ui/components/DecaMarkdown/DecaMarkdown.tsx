import React, { useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkHtml from 'remark-html';
import rehypeRaw from 'rehype-raw';
import { Box } from '@mantine/core';
import { createStyles } from '@mantine/emotion';
import {
  getTargetFromLinkQuery,
  normalizeMarkdownForReactMarkdown,
} from '@resola-ai/blocknote-editor';

// Use pixel to support the client site when embedding the ChatWindow widget
const useStyles = createStyles(theme => ({
  markdownBox: {
    lineHeight: 1.6,
    fontSize: '14px',
    // Reset default margins and standardize font sizes
    '& *': {
      marginBlockStart: 0,
      marginBlockEnd: 0,
      fontSize: '14px',
    },
    a: {
      fontSize: '14px !important',
    },
    p: {
      marginBottom: '6px',
      fontSize: '14px !important',
      whiteSpace: 'break-spaces',
    },
    // Heading styles with consistent spacing
    'h1, h2, h3, h4, h5, h6': {
      marginTop: '4px',
      marginBottom: '8px',
    },
    h1: {
      fontSize: '32px',
    },
    h2: {
      fontSize: '24px',
    },
    h3: {
      fontSize: '18.72px',
    },
    h4: {
      fontSize: '16px',
    },
    h5: {
      fontSize: '13.28px',
    },
    h6: {
      fontSize: '10.72px',
    },
    // List styles
    'ul, ol': {
      paddingLeft: '16px',
    },
    // Task list specific styles
    'ul.contains-task-list': {
      'li.task-list-item': {
        listStyle: 'none',
        marginLeft: '-16px',
        whiteSpace: 'break-spaces',
      },
      'li > input': {
        marginRight: '4px',
      },
      '.task-list-item > p': {
        display: 'flex',
        alignItems: 'flex-start',
        gap: '6px',
        whiteSpace: 'normal',
      },
      '.task-list-item > p > input': {
        marginTop: '5px',
        appearance: 'none',
        backgroundColor: 'transparent',
        color: theme.colors.decaGrey?.[4],
        border: `1px solid ${theme.colors.decaGrey?.[4]}`,
        width: '14px',
        height: '14px',
        borderRadius: '2px',
        position: 'relative',
        '&:checked': {
          backgroundColor: theme.colors.decaBlue?.[4],
          borderColor: theme.colors.decaBlue?.[4],
        },
        '&:before': {
          content: '""',
          width: '6px',
          height: '9px',
          display: 'inline-block',
          position: 'absolute',
          left: '3px',
          top: '0',
        },
        '&:checked:before': {
          transform: 'rotate(45deg)',
          transition: '120ms transform ease-in-out',
          borderBottom: `2px solid ${theme.colors.decaMono?.[1]}`,
          borderRight: `2px solid ${theme.colors.decaMono?.[1]}`,
        },
      },
    },
  },
}));

// Define better types for the renderer props
interface RendererProps {
  href?: string;
  children: React.ReactNode;
}

/**
 * Custom renderer for links in markdown
 * Opens links in a new tab by default unless configured otherwise
 * @param props - Link element props with href and children
 */
const LinkRenderer = React.memo(function LinkRenderer({ href = '', children }: RendererProps) {
  const target = getTargetFromLinkQuery(href) ?? '_blank';

  return (
    <a href={href} target={target} rel='noreferrer'>
      {children}
    </a>
  );
});

/**
 * Custom renderer for phone links in markdown
 * Renders just the content if the phone link appears inside another link
 * @param props - Phone link element props with href and children
 */
const PhoneRenderer = React.memo(function PhoneRenderer({ href = '', children }: RendererProps) {
  // Check if we're already inside an anchor tag
  const isInsideAnchor = React.Children.toArray(children).some(
    child =>
      React.isValidElement(child) &&
      (child.type === 'a' ||
        (typeof child.type === 'function' && child.type.name === 'LinkRenderer'))
  );

  // If we're already inside an anchor tag, just render the children to avoid nesting
  if (isInsideAnchor) {
    return <>{children}</>;
  }

  const target = getTargetFromLinkQuery(href) ?? '_blank';

  return (
    <a href={href} target={target} rel='noreferrer'>
      {children}
    </a>
  );
});

/**
 * Custom renderer for checkbox inputs in task lists
 * Ensures inputs are always controlled to prevent React warnings
 * @param props - Input element props
 */
const CheckboxRenderer = React.memo(function CheckboxRenderer(props: any) {
  const { checked, ...otherProps } = props;

  // Ensure checked is always a boolean to maintain controlled state
  const isChecked = Boolean(checked);

  return <input {...otherProps} type='checkbox' checked={isChecked} readOnly />;
});

/**
 * Props interface for the DecaMarkdown component
 */
interface DecaMarkdownProps {
  /** Markdown content to render */
  markdown: string;
  /** Optional custom class name for the container */
  className?: string;
}

/**
 * DecaMarkdown component for rendering markdown content with custom styling
 *
 * Features:
 * - GitHub flavored markdown support
 * - Custom link and phone number handling
 * - Task lists, tables, and code blocks support
 * - Consistent styling across all markdown elements
 *
 * @param props - Component props containing markdown content and optional class name
 */
const DecaMarkdown: React.FC<DecaMarkdownProps> = ({ markdown, className = '' }) => {
  const { classes, cx } = useStyles();

  // Memoize the normalized markdown to avoid redundant processing
  const normalizedMarkdown = useMemo(() => normalizeMarkdownForReactMarkdown(markdown), [markdown]);

  // Memoize the components object to avoid recreating on each render
  const components = useMemo(
    () =>
      ({
        a: LinkRenderer,
        phone: PhoneRenderer,
        input: CheckboxRenderer,
      }) as any,
    []
  );

  return (
    <Box className={cx(className, classes.markdownBox)} data-testid='deca-markdown'>
      <ReactMarkdown
        remarkPlugins={[remarkGfm, remarkHtml]}
        rehypePlugins={[rehypeRaw]}
        remarkRehypeOptions={{ passThrough: ['link'] }}
        components={components}>
        {normalizedMarkdown}
      </ReactMarkdown>
    </Box>
  );
};

// Memoize the entire component to prevent unnecessary re-renders
export default React.memo(DecaMarkdown);
