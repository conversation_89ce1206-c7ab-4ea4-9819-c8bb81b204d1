{"name": "@resola-ai/utils", "version": "1.0.1", "main": "./index.ts", "types": "./index.ts", "license": "MIT", "scripts": {"lint": "eslint .", "generate:component": "turbo gen react-component", "test": "vitest --run", "test:watch": "vitest", "test:unit": "vitest --run", "coverage": "echo 'Do not need test coverage'", "build": "echo \"Note: no build specified\" && exit 0", "release": "standard-version -t @resola-ai/utils@", "release:minor": "standard-version -t @resola-ai/utils@ --release-as minor", "release:patch": "standard-version -t @resola-ai/utils@ --release-as patch", "release:major": "standard-version -t @resola-ai/utils@ --release-as major"}, "devDependencies": {"@resola-ai/eslint-config": "workspace:*", "@resola-ai/typescript-config": "workspace:*", "@turbo/gen": "^1.11.3", "@types/node": "^20.11.0", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.18", "@resola-ai/models": "workspace:*", "react": "^18.2.0", "standard-version": "^9.5.0", "typescript": "5.6.3", "vitest": "2.1.9", "@vitest/coverage-v8": "^2.1.9", "@testing-library/jest-dom": "^6.2.0", "jest-environment-jsdom": "^29.7.0"}, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/core": "7.17.7", "@mantine/hooks": "7.17.7", "@resola-ai/services-shared": "workspace:*", "moment": "2.29.4"}}