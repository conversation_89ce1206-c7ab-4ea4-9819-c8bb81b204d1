name: Check Branch Name
on:
  push:

env:
  W<PERSON><PERSON><PERSON>OW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'check-branch-name'
      timeout-minutes: 2

  check-branch-name:
    name: Check Branch Name
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check Branch Name
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        run: node scripts/check-branch-name.js "${{ github.event.pull_request.head.ref }}"
