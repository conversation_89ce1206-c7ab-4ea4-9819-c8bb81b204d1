name: Amplify Build Trigger
on:
  push:
  # pull_request:
  #   branches:
  #     - develop
  #     - main
  #   types: [closed]

env:
  WORKFLOW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'amplify-build-trigger'
      timeout-minutes: 2

  amplify-build-trigger:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    name: Amplify Build Trigger
    env:
      GH_TOKEN: ${{ github.token }}
    # if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'

      - name: Amplify Build Trigger
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        env:
          AMPLIFY_TRIGGER_HOOKS: ${{ secrets.AMPLIFY_TRIGGER_HOOKS }}
          BEFORE_REF: ${{ github.event.before }}
          AFTER_REF: ${{ github.event.after }}
        run: node scripts/detect-changes/amplify-build-trigger.js "${AMPLIFY_TRIGGER_HOOKS}" ${{ github.ref }} ${BEFORE_REF} ${AFTER_REF}
