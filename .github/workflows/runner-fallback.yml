name: Runner Fallback Template

on:
  workflow_call:
    inputs:
      job-name:
        required: true
        type: string
        description: 'Job name for runner selection'
      timeout-minutes:
        required: false
        type: number
        default: 30
        description: 'Timeout for self-hosted runner attempt'
    outputs:
      runner-type:
        description: 'The runner type that should be used'
        value: ${{ jobs.determine-runner.outputs.runner-type }}
      use-fallback:
        description: 'Whether fallback to GitHub-hosted runner is needed'
        value: ${{ jobs.determine-runner.outputs.use-fallback }}

jobs:
  determine-runner:
    name: Determine Runner
    runs-on: ubuntu-latest
    outputs:
      runner-type: ${{ steps.check.outputs.runner-type }}
      use-fallback: ${{ steps.check.outputs.use-fallback }}
    steps:
      - name: Check runner availability
        id: check
        run: |
          SELF_HOSTED_RUNNER="${{ fromJSON(vars.RUNNER_TYPES)[inputs.job-name] || vars.RUNNER_TYPE }}"
          
          if [ "$SELF_HOSTED_RUNNER" != "" ] && [ "$SELF_HOSTED_RUNNER" != "ubuntu-latest" ]; then
            echo "runner-type=$SELF_HOSTED_RUNNER" >> $GITHUB_OUTPUT
            echo "use-fallback=false" >> $GITHUB_OUTPUT
            echo "Will attempt to use self-hosted runner: $SELF_HOSTED_RUNNER"
          else
            echo "runner-type=ubuntu-latest" >> $GITHUB_OUTPUT
            echo "use-fallback=true" >> $GITHUB_OUTPUT
            echo "Will use GitHub-hosted runner: ubuntu-latest"
          fi

  test-self-hosted:
    name: Test Self-hosted Availability
    needs: determine-runner
    if: needs.determine-runner.outputs.use-fallback == 'false'
    runs-on: ${{ needs.determine-runner.outputs.runner-type }}
    timeout-minutes: ${{ inputs.timeout-minutes }}
    continue-on-error: true
    outputs:
      available: ${{ steps.test.outputs.available }}
    steps:
      - name: Test runner availability
        id: test
        run: |
          echo "Self-hosted runner is available"
          echo "available=true" >> $GITHUB_OUTPUT