name: Run E2E Tests

on:
  workflow_dispatch:
    inputs:
      project:
        type: choice
        options:
          - chatbox
          - chatbot
          - kb
        description: 'Project'
        default: "chatbox"
        required: true
      screen_size:
        description: 'Screen size (width x height)'
        default: '1920x1080'
        required: true
      browser:
        type: choice
        options:
          - chrome
          - firefox
          - edge
        description: 'Browser'
        default: "chrome"
        required: true
      environment:
        type: choice
        options:
          - dev
          - staging
          - production
        description: 'Environment'
        default: "dev"
        required: true
      custom_command:
        description: "Custom Command"
        default: ""
        required: false
env:
  RF_QASE_API_TOKEN: ${{ secrets.RF_QASE_API_TOKEN }}
  QASE_USER_TOKEN: ${{ secrets.QASE_USER_TOKEN }}
  SCREEN_SIZE: ${{ inputs.screen_size }}
  BROWSER: ${{ inputs.browser }}
  PROJECT: ${{ inputs.project }}
  ENVIRONMENT: ${{ inputs.environment }}
  CUSTOM_COMMAND: ${{ inputs.custom_command }}
  DECA_USER: ${{ secrets.DECA_USER }}
  DECA_PWD: ${{ secrets.DECA_PASSWORD }}
    
jobs:
  # Determine runner strategy  
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'run-e2e-tests_build'
      timeout-minutes: 2

  build:
    name: E2E Tests
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    timeout-minutes: 12
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - name: Install Ubuntu Libraries
        run: |
          sudo apt-get update
          sudo apt-get install xvfb -y
      - name: Install OpenCV Tesseract-OCR
        if: env.PROJECT == 'chatbot'
        run: |
          sudo apt-get install xvfb -y
          sudo apt-get update
          sudo apt-get install -y libasound2 libc6-i386 libc6-x32 libxext6 libxi6 libxrender1 libxtst6 libopencv-dev tesseract-ocr maven
      - name: Install dependencies
        run: |
          cd apps/${PROJECT}/tests/e2e
          chmod a+x install.sh
          ./install.sh ubuntu
      - name: Get API Bearer Token - (not KB)
        if: env.PROJECT != 'kb'
        run: |
          cd apps/${PROJECT}/tests/e2e
          . venv/bin/activate
          xvfb-run --server-args="-screen 0, ${SCREEN_SIZE}x24" python run.py \
          -t "Get API Token" \
          --env ${ENVIRONMENT}
          deactivate
      - name: Running Tests
        if: env.CUSTOM_COMMAND == ''
        run: |
          cd apps/${PROJECT}/tests/e2e
          . venv/bin/activate
          xvfb-run --server-args="-screen 0, ${SCREEN_SIZE}x24" python run.py \
          --qase \
          --env ${ENVIRONMENT} \
          --rerun-failed \
          --browser ${BROWSER}
          deactivate

      - name: Running Tests With Custom Command
        if: env.CUSTOM_COMMAND != ''
        run: |
          cd apps/${PROJECT}/tests/e2e
          . venv/bin/activate
          echo "python run.py ${CUSTOM_COMMAND} --qase --env ${ENVIRONMENT} --browser ${BROWSER} --rerun-failed" > run.sh
          chmod a+x run.sh
          xvfb-run --server-args="-screen 0, ${SCREEN_SIZE}x24" ./run.sh
          deactivate
          git restore .

      - name: Upload logs
        if: ${{ always() }}
        uses: actions/upload-artifact@v4
        with:
          name: test-report-artifact
          path: ${{ github.workspace }}/apps/${{env.PROJECT}}/tests/e2e/Report