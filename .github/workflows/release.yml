name: Manual Release

on:
  workflow_dispatch:
    inputs:
      commitid:
        description: 'Specify a commit ID or tag  (optional, defaults to latest commit)'
        required: false
        type: string

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'manual-release_release'
      timeout-minutes: 2

  release:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for all tags and branches
          ref: ${{ github.event.inputs.commitid || github.sha }}

      - name: Use Node.js 20.12.2
        uses: actions/setup-node@v4
        with:
          node-version: '20.12.2'

      - name: Amplify Build Trigger
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        env:
          AMPLIFY_TRIGGER_HOOKS: ${{ secrets.AMPLIFY_TRIGGER_HOOKS }}
          CURRENT_BRANCH: ${{ github.ref }}
        run: node scripts/manual-release.js "${AMPLIFY_TRIGGER_HOOKS}" "${CURRENT_BRANCH}"
