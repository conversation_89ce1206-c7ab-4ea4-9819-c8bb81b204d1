name: Manual Trigger Amplify Build
on:
  workflow_dispatch:
    inputs:
      appName:
        description: 'Specify the app name'
        required: true
        type: string
      environment:
        description: 'Specify the environment'
        required: true
        type: choice
        options:
          - dev
          - qa
          - prod
        default: "dev"

env:
  WORKFLOW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'manual-trigger-amplify-build'
      timeout-minutes: 2

  manual-trigger-amplify-build:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    name: Manual Trigger Amplify Build
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
            fetch-depth: 0
      - name: Set node
        uses: actions/setup-node@v4
        with:
            node-version-file: '.node-version'
      - name: Manual Trigger Amplify Build
        env:
            AMPLIFY_TRIGGER_HOOKS: ${{ secrets.AMPLIFY_TRIGGER_HOOKS }}
        run: node scripts/detect-changes/manual-trigger-amplify-build.js "${AMPLIFY_TRIGGER_HOOKS}" ${{ github.event.inputs.appName }} ${{ github.event.inputs.environment }}