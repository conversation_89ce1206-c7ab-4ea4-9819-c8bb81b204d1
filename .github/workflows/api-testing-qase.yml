name: QASE-API-Test-Run
on:
  workflow_dispatch:
    inputs:
      qase_api_base_url:
        description: 'Qase API URL'
        default: 'https://api.qase.io/v1'
        required: true
      test_env:
        type: choice
        options:
          - CHATBOX_DEV
          - CHATBOX_PROD
          - CHATBOX_STAGING
          - KB_DEV
          - KB_PROD
          - KB_STAGING
        description: 'Test Environment'
        default: 'CHATBOX_DEV'
        required: true
      qase_run_id:
        description: 'Run Id (optional)'
        required: false
      qase_environment_id:
        description: 'Environment Id (optional)'
        required: false
      qase_milestone_id:
        description: 'Milestone Id (optional)'
        required: false

env:
  QASE_API_BASE_URL: ${{ inputs.qase_api_base_url }}
  TEST_ENV: ${{ inputs.test_env }}
  QASE_RUN_ID: ${{ inputs.qase_run_id }}
  QASE_ENVIRONMENT_ID: ${{ inputs.qase_environment_id }}
  QASE_MILESTONE_ID: ${{ inputs.qase_milestone_id }}
  QASE_TESTOPS_API_TOKEN: ${{ secrets.QASE_API_TOKEN }}
  PM_API_KEY: ${{ secrets.PM_API_KEY }}
  DECA_USER: ${{ secrets.DECA_USER }}
  DECA_PWD: ${{ secrets.DECA_PASSWORD }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'qase-api-test-run_running-test'
      timeout-minutes: 2

  running-test:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    timeout-minutes: 12
    strategy:
      matrix:
        node-version: [20.x]
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
      - name: Install required libs
        run: |
          npm install -g newman newman-reporter-qase
          cd tests/api
          npm i
          npm run install-chromium
      - name: Run KB tests on new test run
        if: ${{ startsWith(inputs.TEST_ENV, 'CHATBOX') }}
        env:
          DEV_ENV_ID: ${{ secrets.CHATBOX_DEV_ENV_ID }}
          PROD_ENV_ID: ${{ secrets.CHATBOX_PROD_ENV_ID }}
          STAGING_ENV_ID: ${{ secrets.CHATBOX_STAGING_ENV_ID }}
        run: |
          cd tests/api
          chmod a+x run.sh
          ./run.sh ${TEST_ENV}_ENV_ID ${QASE_RUN_ID}

      - name: Run KB tests on new test run
        if: ${{ startsWith(inputs.TEST_ENV, 'KB') }}
        env:
          DEV_ENV_ID: 31508737-b55f8416-466c-4587-97c2-af16c16d2cc4
          PROD_ENV_ID: 31508737-b55f8416-466c-4587-97c2-af16c16d2cc4
          STAGING_ENV_ID: 31508737-b55f8416-466c-4587-97c2-af16c16d2cc4
        run: |
          cd tests/api
          chmod a+x run.sh
          ./run.sh ${TEST_ENV}_ENV_ID
