name: PR Agent
on:
  pull_request:
  issue_comment:

env:
  WOR<PERSON>FLOW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'pr-agent_pr_agent_job'
      timeout-minutes: 2

  pr_agent_job:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    permissions:
      issues: write
      pull-requests: write
      contents: write
    name: Run pr agent on every pull request, respond to user comments
    steps:
      - name: PR Agent action step
        id: pragent
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        uses: Codium-ai/pr-agent@main
        env:
          OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          OPENAI_API_BASE: https://api.aig.deca-dev.com/v1
          OPENAI_MODEL: o1-mini
          github_action_config.auto_review: false # enable\disable auto review
          pr_description.enable_help_comment: false
          pr_description.publish_description_as_comment: true
          pr_description.extra_instructions: "Keep the description concise and to the point. Do not include any additional information that is not directly related to the PR."
          pr_code_suggestions.max_context_tokens: 100000
          pr_code_suggestions.suggestions_score_threshold: 4
