name: Check Commit Message
on:
  push:

env:
  WOR<PERSON><PERSON>OW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'check-commit-message'
      timeout-minutes: 2

  check-commit-message:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    name: Check Latest Commit Message
    steps:
      - name: checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
      - name: Get Commit Message
        id: commitmsg
        run: |
          echo "commitmsg=$(git log -1 --pretty=format:"%s")" >> $GITHUB_OUTPUT
      - name: Check Latest Commit Message
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        env: 
          latestcommitmsg: ${{ steps.commitmsg.outputs.commitmsg }}
        run: |
          node scripts/check-commit-message.js "$latestcommitmsg"
