name: Auto Create Sync PR
on:
  pull_request:
    types: [closed]

env:
  WOR<PERSON>FLOW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'auto-create-sync-pr'
      timeout-minutes: 2

  auto-create-sync-pr:
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    name: Auto Create Sync PR
    env:
      GH_TOKEN: ${{ github.token }}
    if: github.event_name == 'pull_request' && github.event.action == 'closed' && github.event.pull_request.merged == true
    # steps:
    #   - uses: actions/checkout@v4
    #   - name: use cli to create a pr
    #     # assigner will be the user who merged the PR
    #     run: gh pr create --base develop --head ${{ github.ref_name }} --title "Sync develop to chatbot/develop in deca-apps" -a ${{ github.actor }} --body "This PR was automatically created by a GitHub Action to sync the develop branch to chatbot/develop in deca-apps. Please review and merge this PR." --label "sync"
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis
      - name: Set node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'

      - name: Auto Create Sync PR
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        env:
          AUTO_MERGE_REVIEWERS: ${{ vars.AUTO_MERGE_REVIEWERS }}
          ALL_APPS_NAMES: ${{ vars.ALL_APPS_NAMES }}
        run: node scripts/detect-changes/auto-create-sync-pr.js ${{ github.event.pull_request.base.ref }} ${{ github.event.pull_request.base.sha }} ${{ github.actor }} ${AUTO_MERGE_REVIEWERS} "${ALL_APPS_NAMES}"
