name: CI
on:
  push:
    branches:
      - develop
      - main
  pull_request:
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      commitid:
        description: 'Specify a commit ID or tag'
        required: false
        type: string

env:
  WORKFLOW_TIMEOUT: ${{ vars.WORKFLOW_TIMEOUT }}

jobs:
  # Determine runner strategy
  runner-check:
    uses: ./.github/workflows/runner-fallback.yml
    with:
      job-name: 'ci_setup'
      timeout-minutes: 5

  setup:
    name: Setup Dependencies
    needs: runner-check
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - name: Setup PNPM
        uses: pnpm/action-setup@v4
        with:
          version: 9
          run_install: false
      - name: Get pnpm store directory
        id: pnpm-cache
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path)" >> $GITHUB_OUTPUT

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ steps.pnpm-cache.outputs.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm

      - name: Install
        run: pnpm i --no-frozen-lockfile

  detect-changes:
    needs: [runner-check, setup]
    name: Detect Changes
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    outputs:
      changed_apps: ${{ steps.detect.outputs.changed_apps }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
      - run: pnpm i --no-frozen-lockfile
      - name: Detect Changes
        id: detect
        run: |
          CHANGED_APPS=$(node scripts/detect-changes/detect-changes-script.js "${{ github.event.pull_request.base.ref }}" "${{ github.event.pull_request.base.sha }}" | grep -oP "(?<={final_output} ).*")
          echo "Changed apps: $CHANGED_APPS"
          echo "changed_apps=$CHANGED_APPS" >> $GITHUB_OUTPUT

  lint:
    needs: [runner-check, setup, detect-changes]
    name: Lint
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
      - run: pnpm i --no-frozen-lockfile
      - name: Run Lint
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        run: node scripts/detect-changes/ci.js lint '${{ needs.detect-changes.outputs.changed_apps }}'

  test:
    needs: [runner-check, setup, detect-changes]
    name: Test
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
      - run: pnpm i --no-frozen-lockfile
      - name: Run Tests
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        run: node scripts/detect-changes/ci.js coverage '${{ needs.detect-changes.outputs.changed_apps }}'
      - name: Merge Coverage Reports
        run: node scripts/coverageMerger.js
      
      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coverage/lcov.info
      
  sonarcloud:
    needs: [runner-check, test, detect-changes]
    name: SonarCloud Scan
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
      - name: Download coverage report
        uses: actions/download-artifact@v4
        with:
          name: coverage-report
          path: coverage
          
      - name: Configure SonarCloud
        run: node scripts/sonarcloud-config.js '${{ needs.detect-changes.outputs.changed_apps }}'
        
      - name: Copy TypeScript Configs
        run: |
          # Create directories if they don't exist
          mkdir -p node_modules/@resola-ai/typescript-config/vitejs.json
          mkdir -p node_modules/@resola-ai/typescript-config/nextjs.json
          mkdir -p node_modules/@resola-ai/typescript-config/react-library.json
          mkdir -p node_modules/@resola-ai/typescript-config/base.json
          
          # Copy base config
          cp packages/typescript-config/base.json node_modules/@resola-ai/typescript-config/base.json/tsconfig.json
          
          # Copy base config to all config directories
          cp packages/typescript-config/base.json node_modules/@resola-ai/typescript-config/react-library.json/base.json
          cp packages/typescript-config/base.json node_modules/@resola-ai/typescript-config/vitejs.json/base.json
          cp packages/typescript-config/base.json node_modules/@resola-ai/typescript-config/nextjs.json/base.json
          
          # Copy vitejs config
          cp packages/typescript-config/vitejs.json node_modules/@resola-ai/typescript-config/vitejs.json/tsconfig.json
          
          # Copy nextjs config
          cp packages/typescript-config/nextjs.json node_modules/@resola-ai/typescript-config/nextjs.json/tsconfig.json
          
          # Copy react-library config
          cp packages/typescript-config/react-library.json node_modules/@resola-ai/typescript-config/react-library.json/tsconfig.json
          
      - name: SonarCloud Scan
        if: success()
        uses: SonarSource/sonarqube-scan-action@master
        env:
            GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
            SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  build:
    needs: [runner-check, setup, detect-changes]
    name: Build
    runs-on: ${{ needs.runner-check.outputs.runner-type }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.inputs.commitid || github.sha }}
      - uses: pnpm/action-setup@v4
        with:
          version: 9
      - uses: actions/setup-node@v4
        with:
          node-version-file: '.node-version'
          cache: pnpm
      - run: pnpm i --no-frozen-lockfile
      - name: Run Build
        timeout-minutes: ${{ fromJSON(env.WORKFLOW_TIMEOUT) }}
        run: node scripts/detect-changes/ci.js build '${{ needs.detect-changes.outputs.changed_apps }}'
      
