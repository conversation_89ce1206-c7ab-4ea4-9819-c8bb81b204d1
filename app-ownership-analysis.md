# DECA Apps - Ownership & Contributor Analysis

*Generated on: June 24, 2025*  
*Branch: analyze-app-ownership*  
*Analysis Period: 2023-2025*

## Overview

This document provides a comprehensive analysis of who works on each application in the DECA Apps monorepo, based on git commit history and contributor patterns.

## Applications Summary

| App | Primary Maintainer(s) | All Contributors | Total Commits | Status |
|-----|----------------------|-----------------|---------------|---------|
| **chatbox** | SangTran | SangT<PERSON>, <PERSON>, da<PERSON>, tinhtinh95, <PERSON> | 1,143 | 🔥 Highly Active |
| **chatbot** | <PERSON>, ngnaoh | <PERSON>, ngnaoh, david<PERSON><PERSON>, blake.nguyen, Trang | 992 | 🔥 Highly Active |
| **livechat** | <PERSON><PERSON> | <PERSON><PERSON>, m<PERSON><PERSON><PERSON>-resol<PERSON>, <PERSON><PERSON>, ha<PERSON>, tinhtinh95 | 730 | 🔥 Highly Active |
| **crm** | <PERSON>, tinht<PERSON>h95 | <PERSON>, tinht<PERSON>h95, na<PERSON>-<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON> | 688 | 🔥 Highly Active |
| **kb** | blake.nguyen, <PERSON> Vo | blake.nguyen, Tu Vo, <PERSON> Nguyen (Tuan), davidresola, tai-resola | 637 | 🔥 Highly Active |
| **form-admin** | <PERSON> | <PERSON>, nhandoantrong, Bum <PERSON>, hieuctfe, hieu | 463 | ✅ Active |
| **tables** | Tam Vu | Tam Vu, SangTran, tamvuquang, Tu Vo, <PERSON> | 357 | ✅ Active |
| **page-admin** | Javier, dao | Javier, dao, hieuctfe, Julie Lai, hieu | 287 | ✅ Active |
| **ai-widgets** | Tu Vo | Tu Vo, Việt Huỳnh, Victor, Viet Huynh | 202 | ✅ Active |
| **widget-builder** | Việt Huỳnh | Việt Huỳnh, Tu Vo, Victor, Viet Huynh, ngdangdat | 175 | ✅ Active |
| **form-client** | Dao | Dao, dao, Phuong, hieu, Victor | 172 | ✅ Active |
| **ai-studio** | Tu Vo, Tina | Tu Vo, Tina, Doan Le, tai-resola, SangTran | 171 | ✅ Active |
| **management** | Yoshiki Ohashi, Ha Do | Yoshiki Ohashi, Ha Do, Tu Vo, yohashi-z123, Victor | 105 | 🔸 Moderate |
| **account** | Michael, Victor | Michael, Victor, Tu Vo, Doan Le | 74 | 🔸 Moderate |
| **workshop** | Nathan Ngo | Nathan Ngo, Victor, Tu Vo, Ruka Murakami | 64 | 🔸 Moderate |
| **node-editor** | Victor, tai-resola | Victor, tai-resola, Tam Vu, Tu Vo, tamvuquang | 60 | 🔸 Moderate |
| **page-builder** | dao | dao, nhandoantrong, Javier, ngdangdat, Julie Lai | 57 | 🔸 Moderate |
| **widget-engine-app** | Victor | Victor, Tu Vo | 39 | 🔹 Low |
| **apex** | Victor, Tu Vo | Tu Vo, Victor, Everton Yoshitani | 23 | 🔹 Low |
| **meet** | Tu Vo | Tu Vo, Victor, dependabot[bot], Việt Huỳnh, ngdangdat | 22 | 🔹 Low |
| **demo** | Victor | Tu Vo, Victor | 20 | 🔹 Low |

---

## Detailed App Analysis

### 🔥 Highly Active Applications (500+ commits)

#### **chatbox** - 1,143 commits
**Primary Maintainer:** SangTran (56% of commits)
- **SangTran**: 643 commits (56%) - *Main Contributor*
- **Victor**: 247 commits (22%) - *Core Contributor*
- **davidresola**: 139 commits (12%)
- **tinhtinh95**: 60 commits (5%)
- **Tu Vo**: 54 commits (5%)

**Focus:** Chat interface and real-time messaging functionality

---

#### **chatbot** - 992 commits
**Primary Maintainers:** Tu Vo (26%), ngnaoh (24%)
- **Tu Vo**: 259 commits (26%) - *Main Contributor*
- **ngnaoh**: 236 commits (24%) - *Main Contributor*
- **davidresola**: 199 commits (20%)
- **blake.nguyen**: 192 commits (19%)
- **Trang**: 106 commits (11%)

**Focus:** AI chatbot functionality and flow management

---

#### **livechat** - 730 commits
**Primary Maintainer:** Doan Le (69% of commits)
- **Doan Le**: 502 commits (69%) - *Main Contributor*
- **murakami-resola**: 92 commits (13%)
- **Ruka Murakami**: 55 commits (8%)
- **haidoan**: 41 commits (6%)
- **tinhtinh95**: 40 commits (5%)

**Focus:** Live chat customer support functionality

---

#### **crm** - 688 commits
**Primary Maintainers:** Nathan Ngo (33%), tinhtinh95 (30%)
- **Nathan Ngo**: 224 commits (33%) - *Main Contributor*
- **tinhtinh95**: 209 commits (30%) - *Main Contributor*
- **nathan-resola**: 117 commits (17%)
- **Tina**: 88 commits (13%)
- **Viet Huynh**: 60 commits (9%)

**Focus:** Customer relationship management and workspace functionality

---

#### **kb** (Knowledge Base) - 637 commits
**Primary Maintainers:** blake.nguyen (32%), Tu Vo (29%)
- **blake.nguyen**: 202 commits (32%) - *Main Contributor*
- **Tu Vo**: 187 commits (29%) - *Main Contributor*
- **Blake Nguyen (Tuan)**: 132 commits (21%) *(same person)*
- **davidresola**: 83 commits (13%)
- **tai-resola**: 33 commits (5%)

**Focus:** Knowledge base and documentation management

---

### ✅ Active Applications (150-500 commits)

#### **form-admin** - 463 commits
**Primary Maintainer:** Javier (27%)
- **Javier**: 125 commits (27%) - *Main Contributor*
- **nhandoantrong**: 93 commits (20%)
- **Bum Cao**: 92 commits (20%)
- **hieuctfe**: 82 commits (18%)
- **hieu**: 71 commits (15%)

**Focus:** Form builder and administration interface

---

#### **tables** - 357 commits
**Primary Maintainer:** Tam Vu (66%)
- **Tam Vu**: 234 commits (66%) - *Main Contributor*
- **SangTran**: 58 commits (16%)
- **tamvuquang**: 38 commits (11%) *(same person)*
- **Tu Vo**: 16 commits (4%)
- **Victor**: 11 commits (3%)

**Focus:** Data table management and visualization

---

#### **page-admin** - 287 commits
**Primary Maintainers:** Javier (30%), dao (25%)
- **Javier**: 85 commits (30%) - *Main Contributor*
- **dao**: 72 commits (25%) - *Main Contributor*
- **hieuctfe**: 53 commits (18%)
- **Julie Lai**: 44 commits (15%)
- **hieu**: 33 commits (11%)

**Focus:** Page content management and administration

---

#### **ai-widgets** - 202 commits
**Primary Maintainer:** Tu Vo (63%)
- **Tu Vo**: 127 commits (63%) - *Main Contributor*
- **Việt Huỳnh**: 48 commits (24%)
- **Victor**: 21 commits (10%) *(victorresola 17 + Victor 4)*
- **Viet Huynh**: 6 commits (3%)

**Focus:** AI-powered widget components

---

#### **widget-builder** - 175 commits
**Primary Maintainer:** Việt Huỳnh (54%)
- **Việt Huỳnh**: 94 commits (54%) - *Main Contributor*
- **Tu Vo**: 72 commits (41%)
- **Victor**: 7 commits (4%)
- **Viet Huynh**: 1 commit (1%)
- **ngdangdat**: 1 commit (1%)

**Focus:** Widget creation and customization tools

---

#### **form-client** - 172 commits
**Primary Maintainer:** Dao (81% combined)
- **Dao**: 79 commits (46%) - *Main Contributor*
- **dao**: 60 commits (35%) *(same person, different config)*
- **Phuong**: 17 commits (10%)
- **hieu**: 9 commits (5%)
- **Victor**: 7 commits (4%)

**Focus:** Form rendering and client-side functionality

---

#### **ai-studio** - 171 commits
**Primary Maintainers:** Tu Vo (28%), Tina (23%)
- **Tu Vo**: 48 commits (28%) - *Main Contributor*
- **Tina**: 39 commits (23%) - *Main Contributor*
- **Doan Le**: 29 commits (17%)
- **tai-resola**: 28 commits (16%)
- **SangTran**: 27 commits (16%)

**Focus:** AI model management and studio interface

---

### 🔸 Moderate Activity Applications (50-150 commits)

#### **management** - 105 commits
**Primary Maintainers:** Yoshiki Ohashi (42%), Ha Do (40%)
- **Yoshiki Ohashi**: 44 commits (42%) - *Main Contributor*
- **Ha Do**: 42 commits (40%) - *Main Contributor*
- **Tu Vo**: 11 commits (10%)
- **yohashi-z123**: 4 commits (4%)
- **Victor**: 4 commits (4%)

**Focus:** System management and administration

---

#### **account** - 74 commits
**Primary Maintainers:** Victor (41%), Michael (31%)
- **Victor**: 30 commits (41%) - *Main Contributor* *(victor 16 + victorresola 14)*
- **Michael**: 23 commits (31%)
- **Tu Vo**: 14 commits (19%)
- **Doan Le**: 7 commits (9%)

**Focus:** User account management and authentication

---

#### **workshop** - 64 commits
**Primary Maintainers:** Victor (34%), Nathan Ngo (31%)
- **Victor**: 22 commits (34%) - *Main Contributor* *(victor 14 + victorresola 8)*
- **Nathan Ngo**: 20 commits (31%)
- **Tu Vo**: 12 commits (19%)
- **Ruka Murakami**: 10 commits (16%)

**Focus:** Development tools and experimentation

---

#### **node-editor** - 60 commits
**Primary Maintainers:** Victor (33%), tai-resola (30%)
- **Victor**: 20 commits (33%) - *Main Contributor*
- **tai-resola**: 18 commits (30%) - *Main Contributor*
- **Tam Vu**: 12 commits (20%)
- **Tu Vo**: 6 commits (10%)
- **tamvuquang**: 4 commits (7%)

**Focus:** Visual node-based editing interface

---

#### **page-builder** - 57 commits
**Primary Maintainer:** dao (40%)
- **dao**: 23 commits (40%) - *Main Contributor*
- **nhandoantrong**: 11 commits (19%)
- **Javier**: 11 commits (19%)
- **ngdangdat**: 6 commits (11%)
- **Julie Lai**: 6 commits (11%)

**Focus:** Dynamic page building and layout management

---

### 🔹 Low Activity Applications (<50 commits)

#### **widget-engine-app** - 39 commits
**Primary Maintainer:** Victor (72%)
- **Victor**: 28 commits (72%) - *Main Contributor* *(victor 19 + d.tran 5 + victorresola 4)*
- **Tu Vo**: 11 commits (28%)

**Focus:** Widget execution engine

---

#### **apex** - 23 commits
**Primary Maintainers:** Victor (52%), Tu Vo (43%)
- **Victor**: 12 commits (52%) - *Main Contributor* *(victorresola 6 + victor 5 + Victor 1)*
- **Tu Vo**: 10 commits (43%)
- **Everton Yoshitani**: 1 commit (4%)

**Focus:** Development apex/shell application

---

#### **meet** - 22 commits
**Primary Maintainer:** Tu Vo (64%)
- **Tu Vo**: 14 commits (64%) - *Main Contributor*
- **Victor**: 5 commits (23%)
- **dependabot[bot]**: 2 commits (9%)
- **Việt Huỳnh**: 1 commit (5%)
- **ngdangdat**: 1 commit (5%)

**Focus:** Video meeting functionality (possibly experimental)

---

#### **demo** - 20 commits
**Primary Maintainer:** Victor (55%)
- **Victor**: 11 commits (55%) - *Main Contributor* *(victor 6 + victorresola 5)*
- **Tu Vo**: 9 commits (45%)

**Focus:** Demonstration and showcase application

---

## Key Team Members & Specializations

### 🏆 Top Overall Contributors

#### **Tu Vo** - Multi-App Lead
**Primary Apps:** ai-widgets, chatbot, kb, ai-studio  
**Secondary Apps:** meet, demo, apex, tables  
**Specialization:** AI/ML features, chatbot functionality, knowledge management

#### **SangTran** - Chat Platform Lead
**Primary Apps:** chatbox  
**Secondary Apps:** tables, ai-studio  
**Specialization:** Real-time chat, messaging systems

#### **Doan Le** - Live Chat Lead
**Primary Apps:** livechat  
**Secondary Apps:** ai-studio, account  
**Specialization:** Customer support systems, live chat functionality

#### **Nathan Ngo** - CRM & Workspace Lead
**Primary Apps:** crm, workshop  
**Specialization:** Customer relationship management, workspace tools

#### **Victor** - Infrastructure & Core Lead
**Primary Apps:** account, workshop, widget-engine-app, apex, demo, node-editor  
**Secondary Apps:** chatbox, ai-widgets, widget-builder, form-client, tables, management, meet  
**Specialization:** Core infrastructure, foundational features, cross-app architecture

#### **Javier** - Form Systems Lead
**Primary Apps:** form-admin, page-admin  
**Specialization:** Form builders, content management

#### **blake.nguyen** - Knowledge Base Lead
**Primary Apps:** kb, chatbot  
**Specialization:** Knowledge management, documentation systems

---

### 🎯 Specialized Teams

#### **AI/ML Team**
- **Tu Vo** (ai-widgets, ai-studio, chatbot)
- **Tina** (ai-studio, crm)
- **tai-resola** (ai-studio, node-editor)

#### **Chat/Communication Team**
- **SangTran** (chatbox)
- **Doan Le** (livechat)
- **ngnaoh** (chatbot)

#### **Form/Content Management Team**
- **Javier** (form-admin, page-admin)
- **Dao/dao** (form-client, page-builder, page-admin)
- **hieuctfe** (form-admin, page-admin)

#### **Data/Tables Team**
- **Tam Vu/tamvuquang** (tables)
- **tinhtinh95** (crm, chatbox, livechat)

#### **Widget/UI Team**
- **Việt Huỳnh/Viet Huynh** (widget-builder, ai-widgets)
- **Tu Vo** (widget-builder, ai-widgets)

---

## Activity Patterns & Insights

### 📈 Development Focus Areas
1. **AI/ML Integration** - High activity in ai-studio, chatbot, ai-widgets
2. **Communication Platforms** - Major focus on chatbox, livechat, chatbot
3. **Data Management** - Significant work on crm, tables, kb
4. **Form/Content Systems** - Active development in form-admin, page-admin

### 🔄 Cross-App Contributors
Several developers work across multiple applications:
- **Tu Vo**: 12+ apps (versatile, AI focus)
- **Victor**: 12+ apps (infrastructure, core features)
- **Javier**: Form systems (form-admin, page-admin)
- **SangTran**: Chat systems (chatbox, tables)

### 📊 App Maturity Levels
- **Mature/Production**: chatbox, chatbot, livechat, crm, kb
- **Active Development**: form-admin, tables, page-admin, ai-widgets
- **Experimental/Low Priority**: demo, apex, meet

### 🚀 Recent Trends (2024-2025)
- Heavy investment in AI-related applications
- Continued focus on chat and communication platforms
- Form and content management system improvements
- Widget and customization platform development

---

*This analysis is based on git commit history from 2023-2025. Individual contributor statistics may vary due to different git configurations or account names used by the same person.*

**Note:** Victor's contributions are consolidated from multiple git identities (victor, victorresola, Victor, d.tran) as they represent the same person.

---

**Generated by:** Git analysis script  
**Last Updated:** June 24, 2025  
**Repository:** deca-apps  
**Total Apps Analyzed:** 21