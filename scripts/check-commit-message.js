const getAppNames = require('./get-apps');
const commitMessage = process.argv[2];
console.log('[Checking latest commit message] ', commitMessage);
if (!commitMessage) {
  console.log('[Checking latest commit message] Ignored. No commit message provided.');
  process.exit(0);
}

// Define the allowed prefixes
const allowedPrefixes = [
  'feat',
  'fix',
  'docs',
  'style',
  'refactor',
  'test',
  'config',
  'structure',
  'build',
  'ci',
  'perf',
  'format',
  'chore',
  'hotfix',
];

console.log('Allowed prefixes: ', allowedPrefixes);

const allowedScopes = [
  ...getAppNames(),
  'chatwindow',
  'sonar',
  'workflows',
  'github-workflows',
  'deps',
  'deps-dev',
  'form',
  'general',
  'page',
  'security',
  'shared',
  'script',
  'scripts',
  'widget-engine',
  'release',
  'ci',
  'ui',
  'blocknote-editor',
  'datadog',
  'test'
];

console.log('Allowed scopes: ', allowedScopes);

const extractScope = () => {
  const regex = /\(([^)]+)\)\:/;
  const match = commitMessage.match(regex);
  return match ? match[1] : null;
};

// Regular expression to match the commit message format
// const commitMessagePattern = /^(feat|fix|docs|style|refactor|test|chore|config|structure|build|ci|perf)(\([^)]+\))?: .{3,}/;
const commitMessagePattern = new RegExp(`^(${allowedPrefixes.join('|')})(\\([^)]+\\))?: .{3,}`);

// if it is a merge commit, ignore
const MERGE_COMMIT_PATTERN = /^Merge/;
if (MERGE_COMMIT_PATTERN.test(commitMessage)) {
  console.log('[Checking latest commit message] Ignored. Merge commit.');
  process.exit(0);
}

if (!commitMessagePattern.test(commitMessage)) {
  throw new Error(
    '[Checking latest commit message] Invalid commit message format. The commit message should match the format: "prefix(scope): message" where scope is optional and the message should be at least 3 characters long.'
  );
} else {
  //   const prefix = commitMessage.match(/^(feat|fix|docs|style|refactor|test|chore|config|structure|build|ci|perf)/)[0];
  const prefix = commitMessage.match(new RegExp(`^(${allowedPrefixes.join('|')})`))[0];
  if (!allowedPrefixes.includes(prefix)) {
    throw new Error(
      `[Checking latest commit message] Invalid commit message prefix. The prefix "${prefix}" is not allowed.`
    );
  }

  const scope = extractScope();

  if (!scope)
    throw new Error(
      `[Checking latest commit message] Invalid commit message scope. The scope is required.`
    );

  if (!allowedScopes.includes(scope)) {
    throw new Error(
      `[Checking latest commit message] Invalid commit message scope. The scope "${scope}" is not allowed. The allowed scopes are: ${allowedScopes.join(', ')}`
    );
  }

  if (scope.startsWith('form') || scope.startsWith('page')) {
    console.log('[Checking latest commit message] Checking extra steps for form', scope);
    if (
      !['form-admin', 'form-client', 'form', 'page-admin', 'page-builder', 'page'].includes(scope)
    ) {
      throw new Error(
        `[Checking latest commit message] Invalid commit message app. The app "${scope}" is not allowed.`
      );
    }
    const regex = /^([A-Z]+-\d+)\s/;
    const explain = commitMessage.split(':')[1].trim();
    if (!regex.test(explain)) {
      throw new Error(
        `[Checking latest commit message] Invalid commit message format. The commit message should match the format: "prefix(scope): JIRA-123 message".`
      );
    } else {
      console.log(`[Checking latest commit message] Commit message is valid explanation.`);
    }
  }
  console.log(`[Checking latest commit message] Commit message is valid.`);
}
